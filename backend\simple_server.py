#!/usr/bin/env python3
"""
Simple standalone server with the fixed tire endpoint
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import sqlite3
import uvicorn
from typing import List, Dict, Any
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the existing models and schemas
try:
    import models
    import schemas
    from database import get_db
    print("✅ Successfully imported existing modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    # Fallback to direct database connection
    pass

app = FastAPI(title="CutRequestStudio API - Fixed")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:9002", "http://127.0.0.1:9002"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def health_check():
    return {"status": "ok"}

@app.get("/api/v1/requests/{request_id}/tires")
def get_request_tires_fixed(request_id: str) -> List[Dict[str, Any]]:
    """
    Get tires for a request from the request_details table.
    Returns data in the RequestDetailRead format.
    """

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Get request details directly from the patched table
        cursor.execute('''
            SELECT
                id,
                request_id,
                tug_number,
                section,
                project_number,
                spec_number,
                tire_size,
                pattern,
                note,
                disposition,
                process_number
            FROM request_details
            WHERE request_id = ?
        ''', (request_id,))

        rows = cursor.fetchall()

        if not rows:
            # Check if request exists
            cursor.execute('SELECT id FROM requests WHERE id = ?', (request_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="Request not found")
            return []  # Request exists but has no tires

        # Convert to RequestDetailRead format
        tire_details = []
        for row in rows:
            detail = {
                "id": row[0],
                "request_id": row[1],
                "tug_number": row[2],
                "section": row[3],
                "project_number": row[4],
                "spec_number": row[5],
                "tire_size": row[6],
                "pattern": row[7],
                "note": row[8] or "",
                "disposition": row[9],
                "process_number": row[10]
            }
            tire_details.append(detail)

        print(f"✅ Returning {len(tire_details)} tire details for request {request_id}")
        return tire_details

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@app.get("/api/v1/requests/{request_id}")
def get_request_fixed(request_id: str) -> Dict[str, Any]:
    """Get a request by ID"""

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT * FROM requests WHERE id = ?', (request_id,))
        row = cursor.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail="Request not found")

        # Get column names
        cursor.execute('PRAGMA table_info(requests)')
        columns = [col[1] for col in cursor.fetchall()]

        # Convert to dict
        request_data = dict(zip(columns, row))

        print(f"✅ Returning request data for {request_id}")
        return request_data

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@app.get("/api/v1/requests")
def get_requests_fixed() -> List[Dict[str, Any]]:
    """Get all requests"""

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT * FROM requests ORDER BY request_date DESC')
        rows = cursor.fetchall()

        # Get column names
        cursor.execute('PRAGMA table_info(requests)')
        columns = [col[1] for col in cursor.fetchall()]

        # Convert to list of dicts
        requests = [dict(zip(columns, row)) for row in rows]

        print(f"✅ Returning {len(requests)} requests")
        return requests

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 Starting CutRequestStudio API server with fixed tire endpoint...")
    uvicorn.run(app, host="127.0.0.1", port=8002)
