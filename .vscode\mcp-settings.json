{"mcp.servers": {"github.com/executeautomation/mcp-playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}, "description": "Playwright MCP Server for browser automation, testing, and web scraping", "capabilities": ["browser_automation", "screenshot_capture", "web_scraping", "javascript_execution", "test_generation"]}, "github.com/modelcontextprotocol/servers/tree/main/src/memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": ".mcp/servers/memory/memory.json"}, "description": "Knowledge Graph Memory Server for persistent memory using a local knowledge graph", "capabilities": ["knowledge_graph", "persistent_memory", "entity_management", "relationship_tracking", "observation_storage"]}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "description": "Sequential Thinking MCP Server for dynamic and reflective problem-solving through structured thinking", "capabilities": ["step_by_step_analysis", "problem_decomposition", "thought_revision", "branching_reasoning", "hypothesis_generation"]}}, "mcp.enableLogging": true, "mcp.logLevel": "info"}