**CutRequestStudio** is a comprehensive tire request management application built with a modern tech stack. It provides a complete solution for managing tire requests, processing details, and associated workflows in an industrial environment.

## Recent Changes

### PIPPO Button Removal (2025-05-28)
- **Removed**: Complete removal of the "PIPPO" button from the tire detail screen
- **Components Modified**:
  - [`src/components/request-detail/tires-section.tsx`](src/components/request-detail/tires-section.tsx): Removed PIPPO button and `onAddTire` prop
  - [`src/app/(dashboard)/dashboard/dettaglio/page.tsx`](src/app/(dashboard)/dashboard/dettaglio/page.tsx): Removed TireSearchDialog integration and related functions
- **Files Deleted**:
  - `src/components/request-detail/tire-search-dialog.tsx`
  - `src/components/request-detail/tire-search-dialog-refactored.tsx`
- **Impact**: The PIPPO button and its associated TireSearchDialog modal have been completely removed. The tire management functionality remains available through the ListFilter button which opens the TireManagementDialog.

### Memory MCP Server Installation (2025-05-28)
- **Added**: Memory MCP Server for persistent knowledge graph management
- **Server Name**: `github.com/modelcontextprotocol/servers/tree/main/src/memory`
- **Package**: `@modelcontextprotocol/server-memory` (installed via NPX)
- **Configuration**: Added to [`.vscode/mcp-settings.json`](.vscode/mcp-settings.json) with custom memory file path
- **Directory Created**: [`.mcp/servers/memory/`](.mcp/servers/memory/) with documentation and examples
- **Capabilities**:
  - Knowledge graph with entities, relations, and observations
  - Persistent memory across sessions
  - Entity management (create, delete, search)
  - Relationship tracking between entities
  - Observation storage for facts about entities
- **Tools Available**: 9 MCP tools including `create_entities`, `create_relations`, `add_observations`, `search_nodes`, `read_graph`
- **Memory File**: [`.mcp/servers/memory/memory.json`](.mcp/servers/memory/memory.json) for knowledge graph storage
- **Use Cases**: User context management, workflow intelligence, knowledge retention, relationship mapping for CutRequestStudio
- **Status**: Configured and ready for use (requires VS Code restart to activate MCP connection)

curl -o .kilocode/rules/memory-bank-instructions.md
https://kilocode.ai/docs/downloads/memory-bank.md