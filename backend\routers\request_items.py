"""
Request Items Router - Simplified Database Structure

This router handles request items (simplified replacement for request_details).
Request items reference the tire catalog and contain only request-specific data.
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from .. import crud, models, schemas
from ..database import get_db
from ..auth import get_current_user

router = APIRouter(
    prefix="/api/request-items",
    tags=["request-items"],
    responses={404: {"description": "Not found"}},
)

@router.get("/request/{request_id}", response_model=List[schemas.RequestItemRead])
def get_request_items_by_request(
    request_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all request items for a specific request.
    
    Returns items with tire details included.
    """
    # Verify request exists
    request = crud.get_request(db=db, request_id=request_id)
    if request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    
    items = crud.get_request_items_by_request(db=db, request_id=request_id)
    return items

@router.get("/{item_id}", response_model=schemas.RequestItemRead)
def get_request_item(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a specific request item by ID.
    
    Returns item with tire details included.
    """
    item = crud.get_request_item(db=db, item_id=item_id)
    if item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    return item

@router.post("/", response_model=schemas.RequestItemRead)
def create_request_item(
    item: schemas.RequestItemCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new request item.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    # Verify request exists
    request = crud.get_request(db=db, request_id=item.request_id)
    if request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    
    # Verify tire exists
    tire = crud.get_tire_enhanced(db=db, tire_id=item.tire_id)
    if tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    
    # Check if item ID already exists
    existing_item = crud.get_request_item(db=db, item_id=item.id)
    if existing_item:
        raise HTTPException(status_code=400, detail="Request item with this ID already exists")
    
    return crud.create_request_item(db=db, item=item)

@router.put("/{item_id}", response_model=schemas.RequestItemRead)
def update_request_item(
    item_id: str,
    item: schemas.RequestItemUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Update a request item.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    # Verify tire exists if tire_id is being updated
    if item.tire_id:
        tire = crud.get_tire_enhanced(db=db, tire_id=item.tire_id)
        if tire is None:
            raise HTTPException(status_code=404, detail="Tire not found")
    
    updated_item = crud.update_request_item(db=db, item_id=item_id, item=item)
    if updated_item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    return updated_item

@router.delete("/{item_id}", response_model=schemas.RequestItemRead)
def delete_request_item(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Delete a request item.
    
    Requires admin or editor role.
    Also deletes all associated cut operations.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    # Check if there are cut operations associated with this item
    cut_operations = crud.get_cut_operations_by_request_item(db=db, request_item_id=item_id)
    if cut_operations:
        raise HTTPException(
            status_code=400, 
            detail=f"Cannot delete request item: {len(cut_operations)} cut operations are associated with it. Delete cut operations first."
        )
    
    deleted_item = crud.delete_request_item(db=db, item_id=item_id)
    if deleted_item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    return deleted_item

@router.get("/{item_id}/cut-operations", response_model=List[schemas.CutOperationRead])
def get_cut_operations_for_item(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all cut operations for a specific request item.
    """
    # Verify item exists
    item = crud.get_request_item(db=db, item_id=item_id)
    if item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    
    operations = crud.get_cut_operations_by_request_item(db=db, request_item_id=item_id)
    return operations

@router.post("/{item_id}/cut-operations", response_model=schemas.CutOperationRead)
def create_cut_operation_for_item(
    item_id: str,
    operation: schemas.CutOperationCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new cut operation for a request item.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    # Verify item exists
    item = crud.get_request_item(db=db, item_id=item_id)
    if item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    
    # Verify processing exists
    processing = crud.get_processing(db=db, processing_id=operation.processing_id)
    if processing is None:
        raise HTTPException(status_code=404, detail="Processing not found")
    
    # Ensure the operation is for the correct item
    operation.request_item_id = item_id
    
    return crud.create_cut_operation(db=db, operation=operation)

@router.get("/{item_id}/summary")
def get_request_item_summary(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a summary of a request item including tire details and cut operations.
    """
    # Get item with tire details
    item = crud.get_request_item(db=db, item_id=item_id)
    if item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    
    # Get cut operations
    cut_operations = crud.get_cut_operations_by_request_item(db=db, request_item_id=item_id)
    
    # Calculate totals
    total_cuts = len(cut_operations)
    total_cost = sum(op.cut_price or 0 for op in cut_operations)
    
    # Group by status
    status_counts = {}
    for op in cut_operations:
        status = op.status or "UNKNOWN"
        status_counts[status] = status_counts.get(status, 0) + 1
    
    return {
        "item": item,
        "cut_operations": cut_operations,
        "summary": {
            "total_cuts": total_cuts,
            "total_cost": total_cost,
            "status_breakdown": status_counts
        }
    }
