

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { UploadCloud, Trash2, Download, FileIcon } from "lucide-react";
import { format } from "date-fns";
import { DeleteConfirmationDialog, DeleteButton } from "@/components/ui/delete-confirmation-dialog";

// Add detailed logging function
function safeFormatDate(dateValue: string | Date | undefined | null) {
  console.log("Date value received:", dateValue, typeof dateValue);

  if (!dateValue) return "-";

  try {
    // If it's already a Date object
    if (dateValue instanceof Date) {
      return format(dateValue, "dd/MM/yyyy HH:mm");
    }

    // If it's a string (could be ISO format or other format)
    const dateObj = new Date(dateValue);
    // console.log("Parsed date:", dateObj);

    if (isNaN(dateObj.getTime())) {
      console.error("Invalid date after parsing:", dateValue);
      return "-";
    }

    return format(dateObj, "dd/MM/yyyy HH:mm");
  } catch (error) {
    console.error("Date formatting error:", error, dateValue);
    return "-";
  }
}
import type { AttachmentFile } from "@/types";

interface AttachmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  requestId: string | null;
  initialAttachments?: AttachmentFile[];
  onUploadComplete: (requestId: string | null, newFiles: AttachmentFile[]) => void;
  onDeleteAttachment: (requestId: string | null, fileId: string) => void;
}

export function AttachmentDialog({
  isOpen,
  onClose,
  requestId,
  initialAttachments = [],
  onUploadComplete,
  onDeleteAttachment,
}: AttachmentDialogProps) {
  const { toast } = useToast();
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [filesToUpload, setFilesToUpload] = React.useState<File[]>([]);
  const [displayedAttachments, setDisplayedAttachments] = React.useState<AttachmentFile[]>(initialAttachments);
  const [fileToDelete, setFileToDelete] = React.useState<string | null>(null);
  const [isDeleteConfirmationOpen, setIsDeleteConfirmationOpen] = React.useState<boolean>(false);

  React.useEffect(() => {
    if (initialAttachments) {
      // Add detailed logging for each attachment
      if (initialAttachments.length > 0) {
        console.log("Initial attachments array:", initialAttachments);
      }

      // Transform the data to ensure consistent property names
      const transformedAttachments = initialAttachments.map(attachment => {
        // Create a new object with both property names to ensure one is available
        return {
          ...attachment,
          // If uploadDate doesn't exist but upload_date does, create it
          uploadDate: attachment.uploadDate || attachment.upload_date,
          // If upload_date doesn't exist but uploadDate does, create it
          upload_date: attachment.upload_date || attachment.uploadDate,
          // Ensure request_id is set
          request_id: attachment.request_id || requestId
        };
      });

      setDisplayedAttachments(transformedAttachments as AttachmentFile[]);
    }
  }, [initialAttachments, isOpen, requestId]);

  // Add debugging to see what data is actually coming from the backend
  React.useEffect(() => {
    if (initialAttachments && initialAttachments.length > 0) {
      // console.log("Attachment data received:", initialAttachments);
    }
  }, [initialAttachments]);

  const handleFileSelectClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const selected = Array.from(event.target.files);
      setFilesToUpload(prev => [...prev, ...selected.filter(f => !prev.some(pf => pf.name === f.name && pf.size === f.size))]);
      event.target.value = "";
      if (selected.length > 0) {
        toast({ title: `${selected.length} file(s) pronti. Clicca 'Upload Selezionati'.`});
      }
    }
  };

  const handleSimulateUpload = () => {
    if (filesToUpload.length === 0) {
      toast({ title: "Nessun File da Caricare", description: "Seleziona prima i file.", variant: "destructive" });
      return;
    }

    const currentDate = new Date();
    const newUploadedFiles: AttachmentFile[] = filesToUpload.map((file, index) => ({
      id: `uploaded-${Date.now()}-${index}-${Math.random().toString(16).slice(2)}`,
      name: file.name,
      size: file.size,
      type: file.type,
      // Set both date properties to ensure compatibility
      uploadDate: currentDate,
      upload_date: currentDate,
      status: "Uploaded",
      fileObject: file,
      // Add request_id to establish the relationship
      request_id: requestId || undefined
    }));

    onUploadComplete(requestId, newUploadedFiles);
    setFilesToUpload([]);
    toast({ title: "Upload Simulato", description: `${newUploadedFiles.length} file "caricati".` });
  };

  const handleDownloadFile = (file: AttachmentFile) => {
    toast({ title: "Download Simulata", description: `Download di ${file.name}...` });
    // In a real app: create a URL for the file (if fileObject exists or from a server) and trigger download
    // For example, if file.fileObject (a File object) exists:
    // const url = URL.createObjectURL(file.fileObject);
    // const a = document.createElement('a');
    // a.href = url;
    // a.download = file.name;
    // document.body.appendChild(a);
    // a.click();
    // document.body.removeChild(a);
    // URL.revokeObjectURL(url);
  };

  const handleDeleteFile = (fileId: string) => {
    setFileToDelete(fileId);
    setIsDeleteConfirmationOpen(true);
  }

  const handleConfirmDelete = () => {
    if (!fileToDelete) return;

    onDeleteAttachment(requestId, fileToDelete);
    toast({ title: "File Eliminato", description: `File rimosso dalla lista.` });
    setFileToDelete(null);
    setIsDeleteConfirmationOpen(false);
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleRemovePendingFile = (fileName: string, fileSize: number) => {
    setFilesToUpload(prev => prev.filter(f => !(f.name === fileName && f.size === fileSize)));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
        if (!open) {
            setFilesToUpload([]);
            onClose();
        }
    }}>
      <DialogContent className="max-w-3xl min-h-[70vh] max-h-[90vh] flex flex-col p-0 bg-background">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className="text-lg font-semibold text-center">Gestione Allegati</DialogTitle>
        </DialogHeader>

        <div className="flex-grow overflow-y-auto p-6 space-y-8">
          <section className="border rounded-xl shadow-md bg-card p-6">
            <h3 className="text-md font-semibold mb-4">Upload File</h3>
            <div className="flex flex-col items-center space-y-4">
              <Button onClick={handleFileSelectClick} variant="outline" className="w-full sm:w-auto">
                <UploadCloud className="mr-2 h-5 w-5" />
                Seleziona File
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                multiple
                hidden
                aria-hidden="true"
              />
              {filesToUpload.length > 0 && (
                <div className="w-full text-sm space-y-3 border-t pt-4 mt-3">
                  <Label className="font-medium text-foreground">File pronti per l'upload ({filesToUpload.length}):</Label>
                  <ScrollArea className="max-h-32 rounded-md p-2 bg-background">
                    <ul className="space-y-1">
                      {filesToUpload.map((file, index) => (
                        <li key={`${file.name}-${file.size}-${index}`} className="flex justify-between items-center text-xs p-1 rounded hover:bg-muted">
                          <div className="flex items-center truncate">
                            <FileIcon className="h-4 w-4 mr-2 shrink-0 text-muted-foreground" />
                            <span className="truncate" title={file.name}>{file.name} ({formatFileSize(file.size)})</span>
                          </div>
                          <DeleteButton
                            onClick={() => handleRemovePendingFile(file.name, file.size)}
                            title="Confirm File Removal"
                            description={`Are you sure you want to remove the file "${file.name}" from the upload list?`}
                            size="icon"
                            className="h-6 w-6"
                          >
                            <Trash2 className="h-3 w-3" />
                          </DeleteButton>
                        </li>
                      ))}
                    </ul>
                  </ScrollArea>
                  <Button onClick={handleSimulateUpload} className="mt-4 w-full sm:w-auto">
                    Upload Selezionati
                  </Button>
                </div>
              )}
            </div>
          </section>

          <section className="border rounded-xl shadow-md bg-card p-6">
            <h3 className="text-md font-semibold mb-4">File Allegati</h3>
            <ScrollArea className="rounded-md min-h-[200px] max-h-96 bg-background">
              <Table>
                <TableHeader className="sticky top-0 bg-card z-10">
                  <TableRow>
                    <TableHead>Nome File</TableHead>
                    <TableHead>Dimensione</TableHead>
                    <TableHead>Data Upload</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead className="text-right">Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayedAttachments.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center h-24 text-muted-foreground">
                        Nessun file allegato.
                      </TableCell>
                    </TableRow>
                  )}
                  {displayedAttachments.map((file) => (
                    <TableRow key={file.id}>
                      <TableCell className="font-medium truncate max-w-xs" title={file.name}>
                        <div className="flex items-center">
                           <FileIcon className="h-4 w-4 mr-2 shrink-0 text-primary" />
                           {file.name}
                        </div>
                      </TableCell>
                      <TableCell>{formatFileSize(file.size)}</TableCell>
                      <TableCell>
                        {(() => {
                          const dateValue = file.upload_date || file.uploadDate;
                          // console.log(`File ${file.id} date:`, dateValue);
                          return safeFormatDate(dateValue);
                        })()}
                      </TableCell>
                      <TableCell>{file.status}</TableCell>
                      <TableCell className="text-right space-x-1">
                         <Button variant="ghost" size="icon" onClick={() => handleDownloadFile(file)} title="Download">
                            <Download className="h-4 w-4" />
                         </Button>
                         <DeleteButton
                            onClick={() => handleDeleteFile(file.id)}
                            title="Confirm File Deletion"
                            description={`Are you sure you want to delete the file "${file.name}"? This action cannot be undone.`}
                            itemType="file"
                            itemName={file.name}
                            size="icon"
                            className="h-8 w-8"
                         />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </section>
        </div>

        <DialogFooter className="p-4 border-t flex-shrink-0">
          <DialogClose asChild>
            <Button variant="default">CLOSE</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
      <DeleteConfirmationDialog
        isOpen={isDeleteConfirmationOpen}
        onClose={() => setIsDeleteConfirmationOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Confirm File Deletion"
        description="Are you sure you want to delete this file? This action cannot be undone."
        itemType="file"
        itemName={fileToDelete ? displayedAttachments.find(f => f.id === fileToDelete)?.name : undefined}
      />
    </Dialog>
  );
}
