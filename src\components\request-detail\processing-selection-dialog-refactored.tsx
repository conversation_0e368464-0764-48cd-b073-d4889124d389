"use client";

import { useState, useEffect, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { DialogProcessing, ProcessingSearchFilters, initialProcessingSearchFilters } from "@/types";
import { searchProcessing } from "@/services/cutProcessingService";
import {
  GenericDialog,
  ColumnConfig,
  FilterConfig
} from "@/components/ui/generic-dialog";

interface ProcessingWithQuantity extends DialogProcessing {
  quantity: number;
}

interface ProcessingSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSelectedProcessing: (selectedProcessing: ProcessingWithQuantity[]) => void;
}

/**
 * Refactored Processing Selection Dialog using GenericDialog component
 *
 * This component demonstrates the usage of the new GenericDialog component
 * to eliminate code duplication. It maintains the same interface as the original
 * component while leveraging the generic functionality.
 *
 * @example
 * ```tsx
 * <ProcessingSelectionDialog
 *   isOpen={isDialogOpen}
 *   onClose={() => setIsDialogOpen(false)}
 *   onAddSelectedProcessing={handleAddProcessing}
 * />
 * ```
 */
export function ProcessingSelectionDialog({
  isOpen,
  onClose,
  onAddSelectedProcessing,
}: ProcessingSelectionDialogProps) {
  const [processing, setProcessing] = useState<DialogProcessing[]>([]);
  const [selectedProcessing, setSelectedProcessing] = useState<Set<string>>(new Set());
  const [processingQuantities, setProcessingQuantities] = useState<Map<string, number>>(new Map());
  const [filters, setFilters] = useState<ProcessingSearchFilters>(initialProcessingSearchFilters);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Load processing data when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadProcessing();
    }
  }, [isOpen]);

  /**
   * Load processing data from the API
   */
  const loadProcessing = async () => {
    try {
      setIsLoading(true);
      const response = await searchProcessing(filters);
      setProcessing(response.data);
    } catch (error) {
      console.error("Error loading processing:", error);
      toast({
        title: "Error",
        description: "Unable to load processing data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle search action
   */
  const handleSearch = () => {
    loadProcessing();
  };

  /**
   * Handle filter changes
   */
  const handleFilterChange = (field: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  /**
   * Handle filter reset
   */
  const handleClearFilters = () => {
    setFilters(initialProcessingSearchFilters);
    setSelectedProcessing(new Set());
    setProcessingQuantities(new Map());
  };

  /**
   * Handle quantity changes for selected processing
   */
  const handleQuantityChange = (processingId: string, quantity: number) => {
    const newQuantities = new Map(processingQuantities);
    if (quantity > 0) {
      newQuantities.set(processingId, quantity);
    } else {
      newQuantities.delete(processingId);
    }
    setProcessingQuantities(newQuantities);
  };

  /**
   * Get quantity for a processing item
   */
  const getQuantity = (processingId: string): number => {
    return processingQuantities.get(processingId) || 1;
  };

  /**
   * Extract numeric value from cost string (e.g., "52.41 €" -> 52.41)
   */
  const extractCostValue = (cost: string): number => {
    const match = cost.match(/(\d+\.?\d*)/);
    return match ? parseFloat(match[1]) : 0;
  };

  /**
   * Calculate total cost of selected processing items
   */
  const calculateTotalCost = (selectedItems: DialogProcessing[]): number => {
    return selectedItems.reduce((total, proc) => {
      const cost = extractCostValue(proc.cost);
      const quantity = getQuantity(proc.id);
      return total + (cost * quantity);
    }, 0);
  };

  /**
   * Handle adding selected processing items
   */
  const handleAddSelected = (selectedItems: DialogProcessing[]) => {
    const selected: ProcessingWithQuantity[] = selectedItems.map(p => ({
      ...p,
      quantity: getQuantity(p.id)
    }));
    onAddSelectedProcessing(selected);
    setSelectedProcessing(new Set());
    setProcessingQuantities(new Map());
    onClose();
  };

  /**
   * Handle dialog close
   */
  const handleClose = () => {
    setSelectedProcessing(new Set());
    setProcessingQuantities(new Map());
    onClose();
  };

  // Column configuration for the processing table
  const columns: ColumnConfig<DialogProcessing>[] = useMemo(() => [
    {
      key: 'id',
      label: 'ID',
      className: 'font-medium',
    },
    {
      key: 'tireType',
      label: 'Type',
      render: (value) => <Badge variant="outline">{value}</Badge>,
    },
    {
      key: 'description1',
      label: 'Description 1',
      className: 'max-w-[150px] truncate',
    },
    {
      key: 'description2',
      label: 'Description 2',
      className: 'max-w-[150px] truncate',
    },
    {
      key: 'testCode1',
      label: 'Test Code 1',
    },
    {
      key: 'testCode2',
      label: 'Test Code 2',
    },
    {
      key: 'cost',
      label: 'Cost',
      className: 'font-medium',
    },
    {
      key: 'id',
      label: 'Total',
      render: (_, item) => {
        if (selectedProcessing.has(item.id)) {
          const cost = extractCostValue(item.cost);
          const quantity = getQuantity(item.id);
          const total = cost * quantity;
          return `€${total.toFixed(2)}`;
        }
        return "-";
      },
    },
  ], [selectedProcessing, processingQuantities]);

  // Filter configuration for the search form
  const filterConfigs: FilterConfig<ProcessingSearchFilters>[] = useMemo(() => [
    {
      key: 'tireType',
      label: 'Tire Type',
      type: 'text',
      placeholder: 'e.g. Type A',
    },
    {
      key: 'description1',
      label: 'Description 1',
      type: 'text',
      placeholder: 'e.g. Desc 1',
    },
    {
      key: 'description2',
      label: 'Description 2',
      type: 'text',
      placeholder: 'e.g. Detail Desc 2',
    },
    {
      key: 'testCode1',
      label: 'Test Code 1',
      type: 'text',
      placeholder: 'e.g. TC1-001',
    },
    {
      key: 'testCode2',
      label: 'Test Code 2',
      type: 'text',
      placeholder: 'e.g. TC2-001',
    },
    {
      key: 'minCost',
      label: 'Min Cost (€)',
      type: 'number',
      placeholder: '0.00',
    },
    {
      key: 'maxCost',
      label: 'Max Cost (€)',
      type: 'number',
      placeholder: '999.99',
    },
  ], []);

  // Convert filters object to the format expected by GenericDialog
  const filterValues = useMemo(() => ({
    tireType: filters.tireType || "",
    description1: filters.description1 || "",
    description2: filters.description2 || "",
    testCode1: filters.testCode1 || "",
    testCode2: filters.testCode2 || "",
    minCost: filters.minCost,
    maxCost: filters.maxCost,
  }), [filters]);

  return (
    <GenericDialog
      isOpen={isOpen}
      onClose={handleClose}
      title="Select Processing"
      data={processing}
      columns={columns}
      filters={filterConfigs}
      filterValues={filterValues}
      onFilterChange={handleFilterChange}
      onSearch={handleSearch}
      onResetFilters={handleClearFilters}
      showFilters={true}
      multiSelect={true}
      selectedIds={selectedProcessing}
      onSelectionChange={setSelectedProcessing}
      onAddSelected={handleAddSelected}
      loading={isLoading}
      showQuantityInputs={true}
      onQuantityChange={handleQuantityChange}
      getQuantity={getQuantity}
      calculateTotal={calculateTotalCost}
      maxWidth="max-w-6xl"
      maxHeight="max-h-[90vh]"
    />
  );
}