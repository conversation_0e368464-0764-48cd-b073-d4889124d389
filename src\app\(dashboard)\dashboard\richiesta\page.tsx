
"use client";

import * as React from "react";
import { useRouter, useSearchParams } from 'next/navigation'; // Added useSearchParams
import { RequestTableSection } from "@/components/page/request-table";
import { RequestFiltersSection } from "@/components/page/request-filters";
import { RequestDetailsSection } from "@/components/page/request-details";
import { ActionButtonsSection } from "@/components/page/action-buttons";
import { useToast } from "@/hooks/use-toast";
import { useRequestDetails } from "@/hooks/useRequestDetails";
import type { AppRequest, RequestFormData, AttachmentFile } from "@/types";
import { initialFormData as defaultInitialFormData } from "@/types";
import { AttachmentDialog } from "@/components/request-detail/attachment-dialog";
import { DepartmentSelectionDialog } from "@/components/request-detail/department-selection-dialog";
import { ReportDialog } from "@/components/request-detail/report-dialog";
import { validateRequestForm } from "@/components/page/request-details";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";

import { getRequests, getRequest, updateRequest, createRequest, deleteRequest, sendRequest } from "@/services/requestService";

export default function RichiestaPage() {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Stato per richieste, loading, errori, filtri, paginazione
  const [allRequests, setAllRequests] = React.useState<AppRequest[]>([]);
  const [displayedRequests, setDisplayedRequests] = React.useState<AppRequest[]>([]);
  const [loadingRequests, setLoadingRequests] = React.useState<boolean>(false);
  const [errorRequests, setErrorRequests] = React.useState<string | null>(null);

  // Parametri di filtro/paginazione/ordinamento
  const [filters, setFilters] = React.useState<{ [key: string]: any }>({});
  const [page, setPage] = React.useState<number>(1);
  const [limit, setLimit] = React.useState<number>(10);
  const [sort, setSort] = React.useState<{ field: string; direction: "asc" | "desc" }>({ field: "requestDate", direction: "desc" });

  // Add this state to track if we're in the middle of a save operation
  const [isSaving, setIsSaving] = React.useState<boolean>(false);
  const [requestToKeepSelected, setRequestToKeepSelected] = React.useState<string | null>(null);

  // Fetch iniziale e ogni volta che cambiano filtri/paginazione/ordinamento
  React.useEffect(() => {
    const fetchRequests = async () => {
      setLoadingRequests(true);
      setErrorRequests(null);
      try {
        const params = {
          ...filters,
          page,
          limit,
          sort: sort.field,
          order: sort.direction,
        };
        const res = await getRequests(params);
        // Ensure we always have an array, even if the API returns unexpected data
        const requestsData = Array.isArray(res) ? res : [];
        setAllRequests(requestsData);
        setDisplayedRequests(requestsData);

        // If we're in the middle of a save operation and have a request to keep selected
        if (isSaving && requestToKeepSelected) {
          // Make sure the request we want to keep selected is still in the list
          const requestExists = requestsData.some((req: AppRequest) => req.id === requestToKeepSelected);
          if (requestExists) {
            // If it exists, select it and fetch its details
            setSelectedRequestId(requestToKeepSelected);
            const detailRes = await getRequest(requestToKeepSelected);
            setCurrentFormData(detailRes);
          }
          // Reset the saving state
          setIsSaving(false);
          setRequestToKeepSelected(null);
        }
      } catch (err: any) {
        setErrorRequests(err?.message || "Errore nel caricamento delle richieste.");
        toast({ title: "Errore", description: err?.message || "Errore nel caricamento delle richieste.", variant: "destructive" });
      } finally {
        setLoadingRequests(false);
      }
    };
    fetchRequests();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, page, limit, sort.field, sort.direction, isSaving, requestToKeepSelected]);
  const [selectedRequestId, setSelectedRequestId] = React.useState<string | null>(null);
  const [currentFormData, setCurrentFormData] = React.useState<RequestFormData | null>(null);
  const [loadingRequest, setLoadingRequest] = React.useState<boolean>(false);
  const [errorRequest, setErrorRequest] = React.useState<string | null>(null);
  const [localFormData, setLocalFormData] = React.useState<RequestFormData>(defaultInitialFormData); // Per nuova richiesta
  const [activeFilter, setActiveFilter] = React.useState<string>("DEFAULT");
  const [filterValues, setFilterValues] = React.useState<{ [key: string]: any }>({
    status: "DEFAULT",
    projectNo: "",
    type: "",
  });

  const [isAttachmentDialogOpen, setIsAttachmentDialogOpen] = React.useState<boolean>(false);
  const [isDepartmentDialogOpen, setIsDepartmentDialogOpen] = React.useState<boolean>(false);
  const [isReportDialogOpen, setIsReportDialogOpen] = React.useState<boolean>(false);
  const [isDeleteConfirmationOpen, setIsDeleteConfirmationOpen] = React.useState<boolean>(false);

  // Quando non c'è una richiesta selezionata, usa lo stato locale per la nuova richiesta
  React.useEffect(() => {
    if (!selectedRequestId) {
      setLocalFormData(defaultInitialFormData);
    }
  }, [selectedRequestId]);

  React.useEffect(() => {
    const requestIdFromQuery = searchParams.get('requestId');
    if (requestIdFromQuery && allRequests.find(req => req.id === requestIdFromQuery)) {
      if (requestIdFromQuery !== selectedRequestId) { // Avoid re-selecting if already selected
        handleRowClick(requestIdFromQuery);
        toast({ title: "Richiesta Caricata", description: `Dettagli per la richiesta ${requestIdFromQuery} visualizzati.` });
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, allRequests]); // Rerun if searchParams or allRequests change. handleRowClick is memoized by React.useState setter.

  const handleRowClick = async (requestId: string) => {
    setSelectedRequestId(requestId);
    setLoadingRequest(true);
    setErrorRequest(null);
    try {
      const res = await getRequest(requestId);
      setCurrentFormData(res);
    } catch (err: any) {
      setErrorRequest(err?.message || "Errore nel caricamento dei dettagli richiesta.");
      toast({ title: "Errore", description: err?.message || "Errore nel caricamento dei dettagli richiesta.", variant: "destructive" });
      // Don't set to null, keep the default form data to prevent controlled/uncontrolled switch
      setCurrentFormData(defaultInitialFormData);
    } finally {
      setLoadingRequest(false);
    }
  };

  const handleFormChange = (field: keyof RequestFormData, value: any) => {
    if (selectedRequestId) {
      setCurrentFormData(prev => prev ? { ...prev, [field]: value } : { ...defaultInitialFormData, [field]: value });
    } else {
      setLocalFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleFilterChange = (newFilter: string) => {
    setActiveFilter(newFilter);
    setFilterValues(prev => ({ ...prev, status: newFilter }));
  };

  const handleInputChange = (field: string, value: any) => {
    setFilterValues(prev => ({ ...prev, [field]: value }));
  };

  const handleSearch = () => {
    // Costruisci oggetto filtri da filterValues, escludendo DEFAULT
    const apiFilters: { [key: string]: any } = {};
    if (filterValues.status && filterValues.status !== "DEFAULT") apiFilters.status = filterValues.status;
    if (filterValues.projectNo) apiFilters.projectNo = filterValues.projectNo;
    if (filterValues.type) apiFilters.type = filterValues.type;
    setFilters(apiFilters);
    setPage(1); // resetta la paginazione
  };

  const handleReset = () => {
    setActiveFilter("DEFAULT");
    setFilterValues({ status: "DEFAULT", projectNo: "", type: "" });
    setFilters({});
    setPage(1);
  };

  const resetFormToDefault = () => {
    setLocalFormData({
      ...defaultInitialFormData,
      requestDate: new Date(),
      status: "DRAFT",
      attachments: [],
    });
    setSelectedRequestId(null);
  }

  const handleActionClick = async (action: "new" | "delete" | "save" | "detail" | "send" | "home") => {
    switch (action) {
      case "home":
        router.push('/dashboard');
        toast({ title: "Navigating Home", description: "Returning to dashboard." });
        break;
      case "new":
        resetFormToDefault();
        toast({ title: "Form Cleared", description: "Ready for new request." });
        break;
      case "delete":
        if (selectedRequestId) {
          setIsDeleteConfirmationOpen(true);
        } else {
          toast({ title: "No Request Selected", description: "Please select a request to delete.", variant: "destructive" });
        }
        break;
      case "save":
        const formToValidate = selectedRequestId ? currentFormData : localFormData;
        const errors = formToValidate ? validateRequestForm(formToValidate) : {};

        if (Object.keys(errors).length > 0) {
          setFormErrors(errors);
          toast({
            title: "Validation Error",
            description: "Please fill in all required fields",
            variant: "destructive"
          });
          return;
        }

        try {
          let savedRequestId = selectedRequestId;

          if (selectedRequestId && currentFormData) {
            await updateRequest(selectedRequestId, currentFormData);
            toast({ title: "Request Saved", description: `Request ${currentFormData.id} updated.` });
          } else {
            const res = await createRequest(localFormData);
            savedRequestId = res.id || null;
            // Immediately set the selectedRequestId for new requests
            if (savedRequestId) {
              setSelectedRequestId(savedRequestId);
            }
            toast({ title: "Request Created", description: `New request created.` });
          }

          // Set the request to keep selected and trigger a refetch
          if (savedRequestId) {
            // Update the URL to keep the correct request selected after save
            router.replace(`?requestId=${savedRequestId}`, { scroll: false });
            setRequestToKeepSelected(savedRequestId);
            setIsSaving(true);
            // Trigger a refetch by updating one of the dependencies
            setFilters({...filters});
          }
        } catch (err: any) {
          toast({ title: "Save Failed", description: err?.message || "Error saving request.", variant: "destructive" });
        }
        break;
      case "detail":
        if (selectedRequestId) {
          router.push(`/dashboard/dettaglio?requestId=${selectedRequestId}`);
          toast({ title: "Navigating to Detail", description: `Opening details for request ${selectedRequestId}.` });
        } else {
          toast({ title: "No Request Selected", description: "Please select a request to view details.", variant: "destructive" });
        }
        break;
      case "send":
        if (selectedRequestId) {
          try {
            await sendRequest(selectedRequestId);
            toast({ title: "Request Sent", description: `Request ${selectedRequestId} sent successfully.` });
            // Refetch requests
            const res = await getRequests({ ...filters, page, limit, sort: sort.field, order: sort.direction });
            setAllRequests(res);
            setDisplayedRequests(res);
          } catch (err: any) {
            toast({ title: "Send Failed", description: err?.message || "Error sending request.", variant: "destructive" });
          }
        } else {
          toast({ title: "No Request Selected", description: "Please select a request to send.", variant: "destructive" });
        }
        break;
    }
  };

  const handleIconClick = (action: "report" | "inChargeOf" | "attach") => {
    switch (action) {
      case "report":
        if (selectedRequestId || (currentFormData && currentFormData.id)) {
          setIsReportDialogOpen(true);
        } else {
          toast({ title: "Nessuna Richiesta Selezionata", description: "Seleziona o crea una richiesta per generare il report.", variant: "destructive" });
        }
        break;
      case "inChargeOf":
        setIsDepartmentDialogOpen(true);
        break;
      case "attach":
        if (!selectedRequestId && !localFormData.id) {
          setLocalFormData(prev => ({ ...prev, attachments: prev.attachments || [] }));
        } else if (!selectedRequestId && localFormData.id) {
          setLocalFormData(prev => ({ ...prev, attachments: prev.attachments || [] }));
        }
        setIsAttachmentDialogOpen(true);
        break;
    }
  };

  const handleAttachmentUploadComplete = (reqId: string | null, newFiles: AttachmentFile[]) => {
    if (selectedRequestId && currentFormData) {
      // For existing requests, update the currentFormData
      setCurrentFormData(prev => ({
        ...prev,
        attachments: [...(prev?.attachments || []), ...newFiles.map(file => ({
          ...file,
          request_id: selectedRequestId
        }))]
      }));
    } else {
      // For new requests, update the localFormData
      setLocalFormData(prev => ({
        ...prev,
        attachments: [...(prev.attachments || []), ...newFiles.map(file => ({
          ...file,
          request_id: prev.id || undefined
        }))]
      }));
    }
    toast({ title: "Allegati Aggiunti", description: `${newFiles.length} file aggiunti al form corrente.` });
  };

  const handleAttachmentDelete = (reqId: string | null, fileId: string) => {
    if (selectedRequestId && currentFormData) {
      // For existing requests, update the currentFormData
      setCurrentFormData(prev => ({
        ...prev,
        attachments: (prev?.attachments || []).filter(att => att.id !== fileId)
      }));
    } else {
      // For new requests, update the localFormData
      setLocalFormData(prev => ({
        ...prev,
        attachments: (prev.attachments || []).filter(att => att.id !== fileId)
      }));
    }
  };

  const handleSelectDepartment = (department: string) => {
    handleFormChange('inChargeOf', department);
    setIsDepartmentDialogOpen(false);
    toast({ title: "Department Selected", description: `${department} assigned to 'In Charge Of'.`});
  };

  const handleConfirmDelete = async () => {
    if (!selectedRequestId) return;

    try {
      await deleteRequest(selectedRequestId);
      toast({ title: "Request Deleted", description: `Request ${selectedRequestId} has been deleted.` });
      resetFormToDefault();
      // Refetch requests
      const res = await getRequests({ ...filters, page, limit, sort: sort.field, order: sort.direction });
      setAllRequests(res);
      setDisplayedRequests(res);
    } catch (err: any) {
      toast({ title: "Delete Failed", description: err?.message || "Error deleting request.", variant: "destructive" });
    } finally {
      setIsDeleteConfirmationOpen(false);
    }
  };

  const handleDeleteRequest = async (requestId: string) => {
    try {
      await deleteRequest(requestId);
      toast({ title: "Request Deleted", description: `Request ${requestId} has been deleted.` });

      // Refetch requests
      const res = await getRequests({ ...filters, page, limit, sort: sort.field, order: sort.direction });
      setAllRequests(res);
      setDisplayedRequests(res);

      // If the deleted request was selected, reset the form
      if (selectedRequestId === requestId) {
        resetFormToDefault();
      }
    } catch (err: any) {
      toast({ title: "Delete Failed", description: err?.message || "Error deleting request.", variant: "destructive" });
    }
  };

  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({});

  return (
    <div className="space-y-8 bg-background">
      <RequestTableSection
        key={`request-table-${selectedRequestId}`}
        requests={displayedRequests}
        selectedRequestId={selectedRequestId}
        onRowClick={handleRowClick}
        onSortChange={(field, direction) => setSort({ field: String(field), direction })}
        sortField={sort.field}
        sortDirection={sort.direction}
        onDeleteRequest={handleDeleteRequest}
      />
      <RequestFiltersSection
        activeFilter={activeFilter}
        onFilterChange={handleFilterChange}
        filterValues={filterValues}
        onInputChange={handleInputChange}
        onSearch={handleSearch}
        onReset={handleReset}
      />
      <RequestDetailsSection
        formData={selectedRequestId ? (currentFormData ?? defaultInitialFormData) : localFormData}
        onFormChange={handleFormChange}
        onIconClick={handleIconClick}
        errors={formErrors}
        setErrors={setFormErrors}
      />
      <ActionButtonsSection onActionClick={handleActionClick} />

      <AttachmentDialog
        isOpen={isAttachmentDialogOpen}
        onClose={() => setIsAttachmentDialogOpen(false)}
        requestId={selectedRequestId ? (currentFormData && currentFormData.id) || null : localFormData.id || null}
        initialAttachments={selectedRequestId ? (currentFormData && currentFormData.attachments) || [] : localFormData.attachments || []}
        onUploadComplete={handleAttachmentUploadComplete}
        onDeleteAttachment={handleAttachmentDelete}
      />
      <DepartmentSelectionDialog
        isOpen={isDepartmentDialogOpen}
        onClose={() => setIsDepartmentDialogOpen(false)}
        onSelectDepartment={handleSelectDepartment}
      />
      <ReportDialog
        isOpen={isReportDialogOpen}
        onClose={() => setIsReportDialogOpen(false)}
        reportData={selectedRequestId ? (currentFormData || defaultInitialFormData) : localFormData}
      />
      <DeleteConfirmationDialog
        isOpen={isDeleteConfirmationOpen}
        onClose={() => setIsDeleteConfirmationOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Confirm Request Deletion"
        description={`Are you sure you want to delete request ${selectedRequestId}? This action cannot be undone.`}
        itemType="request"
        itemName={selectedRequestId || undefined}
      />
    </div>
  );
}
