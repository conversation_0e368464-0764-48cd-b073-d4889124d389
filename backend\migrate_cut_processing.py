import sqlite3
from datetime import datetime

def migrate_database():
    """
    Aggiunge la tabella cut_processing per gestire la relazione N:N
    tra REQUEST_DETAIL_CUT e processing
    """
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    # Crea la tabella cut_processing se non esiste
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cut_processing (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cut_id INTEGER NOT NULL,
            processing_id VARCHAR NOT NULL,
            quantity INTEGER DEFAULT 1,
            notes TEXT,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cut_id) REFERENCES REQUEST_DETAIL_CUT(ID),
            FOREIGN KEY (processing_id) REFERENCES processing(id)
        )
    ''')

    print("Tabella cut_processing creata con successo!")

    # Verifica la struttura
    cursor.execute("PRAGMA table_info(cut_processing);")
    columns = cursor.fetchall()
    print("\nColonne in cut_processing:")
    for col in columns:
        print(f"- {col[1]} ({col[2]}) - Primary Key: {bool(col[5])}, Not Null: {bool(col[3])}")

    conn.commit()
    conn.close()

if __name__ == "__main__":
    migrate_database()
