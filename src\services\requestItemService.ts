import axiosInstance from "@/lib/axiosInstance";
import { RequestItem, RequestItemFormData, CutOperation } from "@/types";

// Request Items API paths
const REQUEST_ITEMS_API_PATH = "/request-items";

// Interface for request item summary
export interface RequestItemSummary {
  item: RequestItem;
  cut_operations: CutOperation[];
  summary: {
    total_cuts: number;
    total_cost: number;
    status_breakdown: Record<string, number>;
  };
}

/**
 * Request Items Service - New Simplified Structure
 * 
 * This service handles request items (simplified replacement for request_details).
 * Request items reference the tire catalog and contain only request-specific data.
 */

// Get all request items for a specific request
export const getRequestItemsByRequest = async (requestId: string): Promise<RequestItem[]> => {
  try {
    const response = await axiosInstance.get<RequestItem[]>(`${REQUEST_ITEMS_API_PATH}/request/${requestId}`);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error fetching request items for request ${requestId}:`, error);
    throw error;
  }
};

// Get a specific request item by ID
export const getRequestItem = async (itemId: string): Promise<RequestItem> => {
  try {
    const response = await axiosInstance.get<RequestItem>(`${REQUEST_ITEMS_API_PATH}/${itemId}`);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error fetching request item with ID ${itemId}:`, error);
    throw error;
  }
};

// Create a new request item
export const createRequestItem = async (data: RequestItemFormData): Promise<RequestItem> => {
  try {
    const response = await axiosInstance.post<RequestItem>(REQUEST_ITEMS_API_PATH, data);
    return response.data;
  } catch (error) {
    console.error("[requestItemService] Error creating request item:", error);
    throw error;
  }
};

// Update an existing request item
export const updateRequestItem = async (itemId: string, data: Partial<RequestItemFormData>): Promise<RequestItem> => {
  try {
    const response = await axiosInstance.put<RequestItem>(`${REQUEST_ITEMS_API_PATH}/${itemId}`, data);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error updating request item with ID ${itemId}:`, error);
    throw error;
  }
};

// Delete a request item
export const deleteRequestItem = async (itemId: string): Promise<RequestItem> => {
  try {
    const response = await axiosInstance.delete<RequestItem>(`${REQUEST_ITEMS_API_PATH}/${itemId}`);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error deleting request item with ID ${itemId}:`, error);
    throw error;
  }
};

// Get all cut operations for a specific request item
export const getCutOperationsForItem = async (itemId: string): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.get<CutOperation[]>(`${REQUEST_ITEMS_API_PATH}/${itemId}/cut-operations`);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error fetching cut operations for item ${itemId}:`, error);
    throw error;
  }
};

// Create a new cut operation for a request item
export const createCutOperationForItem = async (itemId: string, operationData: any): Promise<CutOperation> => {
  try {
    const response = await axiosInstance.post<CutOperation>(`${REQUEST_ITEMS_API_PATH}/${itemId}/cut-operations`, operationData);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error creating cut operation for item ${itemId}:`, error);
    throw error;
  }
};

// Get a comprehensive summary of a request item
export const getRequestItemSummary = async (itemId: string): Promise<RequestItemSummary> => {
  try {
    const response = await axiosInstance.get<RequestItemSummary>(`${REQUEST_ITEMS_API_PATH}/${itemId}/summary`);
    return response.data;
  } catch (error) {
    console.error(`[requestItemService] Error fetching request item summary for ${itemId}:`, error);
    throw error;
  }
};

// Validate request item data before submission
export const validateRequestItemData = (data: RequestItemFormData): string[] => {
  const errors: string[] = [];

  if (!data.requestId?.trim()) {
    errors.push("Request ID is required");
  }

  if (!data.tireId?.trim()) {
    errors.push("Tire ID is required");
  }

  if (!data.quantity || data.quantity <= 0) {
    errors.push("Quantity must be greater than 0");
  }

  if (!data.disposition?.trim()) {
    errors.push("Disposition is required");
  }

  const validDispositions = ["AVAILABLE", "SCRAP", "TESTED", "REPAIR", "RESERVED", "COMPLETED"];
  if (data.disposition && !validDispositions.includes(data.disposition)) {
    errors.push("Invalid disposition value");
  }

  if (data.unitPrice !== undefined && data.unitPrice < 0) {
    errors.push("Unit price cannot be negative");
  }

  return errors;
};

// Transform legacy request detail to request item format
export const transformLegacyToRequestItem = (legacyDetail: any, requestId: string): RequestItemFormData => {
  return {
    id: legacyDetail.id,
    requestId: requestId,
    tireId: legacyDetail.tireId || legacyDetail.tire_id || "",
    quantity: legacyDetail.quantity || 1,
    disposition: legacyDetail.disposition || "AVAILABLE",
    notes: legacyDetail.notes || legacyDetail.note || "",
    unitPrice: legacyDetail.unitPrice || legacyDetail.unit_price || 0,
    section: legacyDetail.section || "",
  };
};

// Transform request item to legacy format for backward compatibility
export const transformRequestItemToLegacy = (requestItem: RequestItem): any => {
  return {
    id: requestItem.id,
    request_id: requestItem.requestId,
    tire_id: requestItem.tireId,
    quantity: requestItem.quantity,
    disposition: requestItem.disposition,
    notes: requestItem.notes,
    unit_price: requestItem.unitPrice,
    section: requestItem.section,
    // Legacy fields for compatibility
    tugNo: requestItem.tire?.tugNo || "",
    specNo: requestItem.tire?.specNo || "",
    tireSize: requestItem.tire?.size || "",
    pattern: requestItem.tire?.pattern || "",
    projectNo: requestItem.tire?.projectNo || "",
    note: requestItem.notes,
  };
};

// Batch operations for multiple request items
export const batchCreateRequestItems = async (items: RequestItemFormData[]): Promise<RequestItem[]> => {
  try {
    const createPromises = items.map(item => createRequestItem(item));
    return await Promise.all(createPromises);
  } catch (error) {
    console.error("[requestItemService] Error in batch create request items:", error);
    throw error;
  }
};

export const batchUpdateRequestItems = async (
  updates: Array<{ id: string; data: Partial<RequestItemFormData> }>
): Promise<RequestItem[]> => {
  try {
    const updatePromises = updates.map(({ id, data }) => updateRequestItem(id, data));
    return await Promise.all(updatePromises);
  } catch (error) {
    console.error("[requestItemService] Error in batch update request items:", error);
    throw error;
  }
};

export const batchDeleteRequestItems = async (itemIds: string[]): Promise<void> => {
  try {
    const deletePromises = itemIds.map(id => deleteRequestItem(id));
    await Promise.all(deletePromises);
  } catch (error) {
    console.error("[requestItemService] Error in batch delete request items:", error);
    throw error;
  }
};

// Calculate totals for request items
export const calculateRequestItemTotals = (items: RequestItem[]): {
  totalQuantity: number;
  totalValue: number;
  itemsByDisposition: Record<string, number>;
} => {
  const totals = {
    totalQuantity: 0,
    totalValue: 0,
    itemsByDisposition: {} as Record<string, number>,
  };

  items.forEach(item => {
    totals.totalQuantity += item.quantity;
    totals.totalValue += (item.unitPrice || 0) * item.quantity;
    
    const disposition = item.disposition;
    totals.itemsByDisposition[disposition] = (totals.itemsByDisposition[disposition] || 0) + 1;
  });

  return totals;
};

// Generate unique request item ID
export const generateRequestItemId = (requestId: string, sequence: number): string => {
  return `${requestId}_ITEM_${sequence.toString().padStart(3, '0')}`;
};
