"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import type { DialogProcessing, ProcessingSearchFilters } from "@/types";
import { initialProcessingSearchFilters } from "@/types";
import { Star, StarOff, ArrowUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

const sampleProcessingData: DialogProcessing[] = Array.from({ length: 15 }, (_, i) => ({
  id: `PROC${i + 1}`,
  tireType: `Type ${String.fromCharCode(65 + (i % 5))}`,
  description1: `Desc 1 - Variant ${i + 1}`,
  description2: `Detail Desc 2 - Option ${i + 1}`,
  testCode1: `TC1-${String(i + 1).padStart(3, '0')}`,
  testCode2: `TC2-${String(i + 1).padStart(3, '0')}`,
  cost: `${(Math.random() * 100 + 50).toFixed(2)} €`,
}));

const SortableHeader = ({ children, columnId, onSort, currentSortKey, currentSortDirection }: { children: React.ReactNode, columnId: keyof DialogProcessing, onSort: (columnId: keyof DialogProcessing) => void, currentSortKey: keyof DialogProcessing | null, currentSortDirection: 'ascending' | 'descending' }) => (
    <Button
      variant="ghost"
      onClick={() => onSort(columnId)}
      className="px-1 py-1 h-auto hover:bg-transparent group"
    >
      <span className="group-hover:opacity-100">{children}</span>
      <ArrowUpDown className={cn("ml-2 h-4 w-4 opacity-50 group-hover:opacity-100 transition-opacity", {
        "opacity-100": currentSortKey === columnId,
        "rotate-180": currentSortKey === columnId && currentSortDirection === 'descending'
      })} />
    </Button>
  );


interface ProcessingSearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSelectedProcessing: (selectedProcessing: DialogProcessing[]) => void;
}

export function ProcessingSearchDialog({ isOpen, onClose, onAddSelectedProcessing }: ProcessingSearchDialogProps) {
  const { toast } = useToast();
  const [searchFilters, setSearchFilters] = React.useState<ProcessingSearchFilters>(initialProcessingSearchFilters);
  const [displayedProcessing, setDisplayedProcessing] = React.useState<DialogProcessing[]>(sampleProcessingData);
  const [selectedProcessingIds, setSelectedProcessingIds] = React.useState<Set<string>>(new Set());
  const [sortConfig, setSortConfig] = React.useState<{ key: keyof DialogProcessing | null; direction: 'ascending' | 'descending' }>({ key: null, direction: 'ascending' });


  const handleSort = (key: keyof DialogProcessing) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });

     const sortedData = [...displayedProcessing].sort((a, b) => {
        const valA = a[key];
        const valB = b[key];

        if (typeof valA === 'string' && typeof valB === 'string') {
          return direction === 'ascending' ? valA.localeCompare(valB) : valB.localeCompare(valA);
        }
        // Fallback for non-string types or mixed types
        if (valA < valB) return direction === 'ascending' ? -1 : 1;
        if (valA > valB) return direction === 'ascending' ? 1 : -1;
        return 0;
      });
    setDisplayedProcessing(sortedData);
  };


  const handleFilterChange = (field: keyof ProcessingSearchFilters, value: string | boolean) => {
    setSearchFilters(prev => ({ ...prev, [field]: value }));
  };

  const handleSearch = () => {
    let filtered = sampleProcessingData.filter(proc => {
        let matches = true;
        if (searchFilters.tireType && proc.tireType && !proc.tireType.toLowerCase().includes(searchFilters.tireType.toLowerCase())) matches = false;
        if (searchFilters.description1 && proc.description1 && !proc.description1.toLowerCase().includes(searchFilters.description1.toLowerCase())) matches = false;
        if (searchFilters.description2 && proc.description2 && !proc.description2.toLowerCase().includes(searchFilters.description2.toLowerCase())) matches = false;
        if (searchFilters.testCode1 && proc.testCode1 && !proc.testCode1.toLowerCase().includes(searchFilters.testCode1.toLowerCase())) matches = false;
        if (searchFilters.testCode2 && proc.testCode2 && !proc.testCode2.toLowerCase().includes(searchFilters.testCode2.toLowerCase())) matches = false;
        return matches;
    });

    if (sortConfig.key) {
        const key = sortConfig.key;
        const direction = sortConfig.direction;
        filtered.sort((a, b) => {
            const valA = a[key];
            const valB = b[key];
            if (typeof valA === 'string' && typeof valB === 'string') {
              return direction === 'ascending' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            }
            if (valA < valB) return direction === 'ascending' ? -1 : 1;
            if (valA > valB) return direction === 'ascending' ? 1 : -1;
            return 0;
        });
    }

    setDisplayedProcessing(filtered);
    setSelectedProcessingIds(new Set());
    toast({ title: "Search Completed", description: `${filtered.length} processing types found.`});
  };

  const handleReset = () => {
    setSearchFilters(initialProcessingSearchFilters);
    setDisplayedProcessing(sampleProcessingData);
    setSelectedProcessingIds(new Set());
    setSortConfig({ key: null, direction: 'ascending' });
    toast({ title: "Filters Reset", description: "Processing search filters cleared." });
  };

  const handleToggleProcessingSelection = (processingId: string) => {
    setSelectedProcessingIds(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(processingId)) {
        newSelection.delete(processingId);
      } else {
        newSelection.add(processingId);
      }
      return newSelection;
    });
  };

  const handleAddSelected = () => {
    const processingToAdd = displayedProcessing.filter(proc => selectedProcessingIds.has(proc.id));
    if (processingToAdd.length === 0) {
      toast({ title: "No Processing Selected", description: "Please select processing types to add.", variant: "destructive" });
      return;
    }
    onAddSelectedProcessing(processingToAdd);
    toast({ title: "Processings Added", description: `${processingToAdd.length} processings added.`});
    // onClose();
  };

  React.useEffect(() => {
    // Reset filters and selections if the dialog is closed externally or re-opened
    if (!isOpen) {
      handleReset();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
      <DialogContent className="max-w-5xl h-[90vh] flex flex-col p-0">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className="text-lg font-semibold text-center">FIND PROCESSING</DialogTitle>
        </DialogHeader>

        <div className="flex-grow overflow-y-auto p-4 space-y-6">
          <section className="bg-primary/10 p-4 rounded-lg shadow-sm">
            <h3 className="text-md font-semibold mb-1">SEARCH for RODAGGIO</h3>
            <p className="text-xs text-muted-foreground mb-3">ATTENTION: SHORE will be added by default while DL1 is recommended</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 items-end">
              <div>
                <Label htmlFor="procTireType">Tire Type</Label>
                <Select value={searchFilters.tireType || "ANY_TYPE"} onValueChange={val => handleFilterChange('tireType', val === "ANY_TYPE" ? "" : val)}>
                  <SelectTrigger id="procTireType"><SelectValue placeholder="Select Tire Type" /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ANY_TYPE">Any Type</SelectItem>
                    <SelectItem value="Type A">Type A</SelectItem>
                    <SelectItem value="Type B">Type B</SelectItem>
                    <SelectItem value="Type C">Type C</SelectItem>
                    <SelectItem value="Type D">Type D</SelectItem>
                    <SelectItem value="Type E">Type E</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2 pt-6">
                <Checkbox id="procPicture" checked={searchFilters.picture} onCheckedChange={c => handleFilterChange('picture', !!c)} />
                <Label htmlFor="procPicture">Picture</Label>
              </div>
              <div><Label htmlFor="procDesc1">Description 1</Label><Input id="procDesc1" value={searchFilters.description1} onChange={e => handleFilterChange('description1', e.target.value)} /></div>
              <div><Label htmlFor="procDesc2">Description 2</Label><Input id="procDesc2" value={searchFilters.description2} onChange={e => handleFilterChange('description2', e.target.value)} /></div>
              <div><Label htmlFor="procTestCode1">T.E.S.T. Code1</Label><Input id="procTestCode1" value={searchFilters.testCode1} onChange={e => handleFilterChange('testCode1', e.target.value)} /></div>
              <div><Label htmlFor="procTestCode2">T.E.S.T. Code2</Label><Input id="procTestCode2" value={searchFilters.testCode2} onChange={e => handleFilterChange('testCode2', e.target.value)} /></div>
            </div>
            <div className="mt-6 flex justify-end space-x-2">
              <Button variant="default" onClick={handleSearch}>Search</Button>
              <Button variant="outline" onClick={handleReset}>Reset</Button>
            </div>
          </section>

          <section className={cn("flex-grow flex flex-col min-h-0", "bg-accent/10 p-4 rounded-lg shadow-sm")}>
            <h3 className="text-lg font-semibold mb-2">Cut Type</h3>
            <ScrollArea className="rounded-md flex-grow bg-card"> {/* Changed bg-background to bg-card */}
              <Table>
                <TableHeader className="sticky top-0 bg-card z-10">
                  <TableRow>
                    <TableHead className="w-[50px]"></TableHead>
                    <TableHead><SortableHeader columnId="tireType" onSort={handleSort} currentSortKey={sortConfig.key} currentSortDirection={sortConfig.direction}>Tire Type#</SortableHeader></TableHead>
                    <TableHead><SortableHeader columnId="description1" onSort={handleSort} currentSortKey={sortConfig.key} currentSortDirection={sortConfig.direction}>Description 1</SortableHeader></TableHead>
                    <TableHead><SortableHeader columnId="description2" onSort={handleSort} currentSortKey={sortConfig.key} currentSortDirection={sortConfig.direction}>Description 2</SortableHeader></TableHead>
                    <TableHead><SortableHeader columnId="testCode1" onSort={handleSort} currentSortKey={sortConfig.key} currentSortDirection={sortConfig.direction}>T.E.S.T. Code1</SortableHeader></TableHead>
                    <TableHead><SortableHeader columnId="testCode2" onSort={handleSort} currentSortKey={sortConfig.key} currentSortDirection={sortConfig.direction}>T.E.S.T. Code2</SortableHeader></TableHead>
                    {/* <TableHead><SortableHeader columnId="cost" onSort={handleSort} currentSortKey={sortConfig.key} currentSortDirection={sortConfig.direction}>Cost</SortableHeader></TableHead> */}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayedProcessing.map((proc) => (
                    <TableRow key={proc.id} data-state={selectedProcessingIds.has(proc.id) ? "selected" : ""}>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleToggleProcessingSelection(proc.id)}
                          aria-label={selectedProcessingIds.has(proc.id) ? "Deselect processing" : "Select processing"}
                          className={cn("hover:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0", selectedProcessingIds.has(proc.id) ? "text-yellow-400 hover:text-yellow-500" : "text-muted-foreground hover:text-foreground")}
                        >
                          {selectedProcessingIds.has(proc.id) ? <Star className="h-5 w-5 fill-current" /> : <StarOff className="h-5 w-5" />}
                        </Button>
                      </TableCell>
                      <TableCell>{proc.tireType}</TableCell>
                      <TableCell><Input value={proc.description1} readOnly className="bg-transparent border-none h-8 text-xs focus-visible:ring-0 focus-visible:ring-offset-0" /></TableCell>
                      <TableCell><Input value={proc.description2} readOnly className="bg-transparent border-none h-8 text-xs focus-visible:ring-0 focus-visible:ring-offset-0" /></TableCell>
                      <TableCell><Input value={proc.testCode1} readOnly className="bg-transparent border-none h-8 text-xs focus-visible:ring-0 focus-visible:ring-offset-0" /></TableCell>
                      <TableCell><Input value={proc.testCode2} readOnly className="bg-transparent border-none h-8 text-xs focus-visible:ring-0 focus-visible:ring-offset-0" /></TableCell>
                      {/* <TableCell><Input value={proc.cost} readOnly className="bg-transparent border-none h-8 text-xs text-right focus-visible:ring-0 focus-visible:ring-offset-0" /></TableCell> */}
                    </TableRow>
                  ))}
                  {displayedProcessing.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center h-24">
                        No processing types found matching your criteria.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </section>
        </div>

        <DialogFooter className="p-4 border-t flex-shrink-0 justify-end">
            <div className="flex space-x-2">
                <Button variant="outline" onClick={handleAddSelected}>Add({selectedProcessingIds.size})</Button>
                <DialogClose asChild>
                <Button variant="default">CLOSE</Button>
                </DialogClose>
            </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
