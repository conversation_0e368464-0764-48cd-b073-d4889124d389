/**
 * VALIDATION SCHEMA UNIFICATION - PRIORITÀ 3
 *
 * Consolida le regole di validazione duplicate presenti in:
 * - src/components/page/request-details.tsx:67 vs backend/schemas.py
 * - src/components/tire-management/tire-form.tsx:49 vs Backend validation
 * - Validazione email (frontend + backend)
 * - Validazione date ranges
 * - Validazione numeric constraints
 * - Validazione required fields
 *
 * Integrazione con Universal Form Hook di PRIORITÀ 1
 * Supporto per validazione real-time e batch
 * Messaggi di errore localizzabili
 * Validazione cross-field e business rules
 * Performance ottimizzata per form complessi
 */

import { ValidationRule } from '@/hooks/useUniversalForm';
import { z } from 'zod';

// ============================================================================
// CORE VALIDATION TYPES
// ============================================================================

interface ValidationSchema<T = any> {
  /** Schema name for debugging */
  name: string;
  /** Zod schema for type validation */
  zodSchema?: z.ZodSchema<T>;
  /** Custom validation rules for Universal Form Hook */
  formRules?: Partial<Record<keyof T, ValidationRule<T>[]>>;
  /** Cross-field validation rules */
  crossFieldRules?: Array<(data: T) => string | undefined>;
  /** Business logic validation rules */
  businessRules?: Array<(data: T) => string | undefined>;
}

interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings?: Record<string, string>;
}

interface ValidationOptions {
  /** Skip certain validation types */
  skip?: ('required' | 'format' | 'business' | 'crossField')[];
  /** Validate only specific fields */
  fields?: string[];
  /** Include warnings in result */
  includeWarnings?: boolean;
  /** Locale for error messages */
  locale?: 'en' | 'it';
}

// ============================================================================
// SHARED VALIDATION RULES
// ============================================================================

/**
 * Shared validation rules utilizzabili sia frontend che backend
 */
class SharedValidationRules {

  // EMAIL VALIDATION
  static email = {
    zodSchema: z.string().email("Invalid email format"),
    formRule: <T>(): ValidationRule<T> => ({
      validate: (value: string) => {
        if (!value) return undefined;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value) ? undefined : 'Please enter a valid email address';
      }
    }),
    backendPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  };

  // REQUIRED FIELDS
  static required = {
    zodSchema: z.string().min(1, "This field is required"),
    formRule: <T>(message?: string): ValidationRule<T> => ({
      validate: (value: any) => {
        if (value === undefined || value === null || value === '') {
          return message || 'This field is required';
        }
        return undefined;
      }
    })
  };

  // NUMERIC CONSTRAINTS
  static positiveNumber = {
    zodSchema: z.number().positive("Must be a positive number"),
    formRule: <T>(): ValidationRule<T> => ({
      validate: (value: number) => {
        if (value === undefined || value === null) return undefined;
        return value > 0 ? undefined : 'Must be a positive number';
      }
    })
  };

  static numberRange = (min: number, max: number) => ({
    zodSchema: z.number().min(min, `Must be at least ${min}`).max(max, `Must be at most ${max}`),
    formRule: <T>(): ValidationRule<T> => ({
      validate: (value: number) => {
        if (value === undefined || value === null) return undefined;
        if (value < min || value > max) {
          return `Must be between ${min} and ${max}`;
        }
        return undefined;
      }
    })
  });

  // DATE RANGES
  static dateRange = (minDate?: Date, maxDate?: Date) => ({
    zodSchema: z.date()
      .refine(date => !minDate || date >= minDate, `Date must be after ${minDate?.toLocaleDateString()}`)
      .refine(date => !maxDate || date <= maxDate, `Date must be before ${maxDate?.toLocaleDateString()}`),
    formRule: <T>(): ValidationRule<T> => ({
      validate: (value: string | Date) => {
        if (!value) return undefined;
        const date = new Date(value);
        if (isNaN(date.getTime())) return 'Invalid date';

        if (minDate && date < minDate) {
          return `Date must be after ${minDate.toLocaleDateString()}`;
        }
        if (maxDate && date > maxDate) {
          return `Date must be before ${maxDate.toLocaleDateString()}`;
        }
        return undefined;
      }
    })
  });

  // STRING LENGTH
  static stringLength = (min?: number, max?: number) => {
    let schema = z.string();
    if (min !== undefined) schema = schema.min(min, `Must be at least ${min} characters`);
    if (max !== undefined) schema = schema.max(max, `Must be at most ${max} characters`);

    return {
      zodSchema: schema,
      formRule: <T>(): ValidationRule<T> => ({
        validate: (value: string) => {
          if (!value) return undefined;
          if (min !== undefined && value.length < min) {
            return `Must be at least ${min} characters`;
          }
          if (max !== undefined && value.length > max) {
            return `Must be at most ${max} characters`;
          }
          return undefined;
        }
      })
    };
  };
}

// ============================================================================
// DOMAIN-SPECIFIC VALIDATION SCHEMAS
// ============================================================================

/**
 * Request Form Validation Schema
 * Consolida validazioni da request-details.tsx:67 e backend/schemas.py
 */
interface RequestFormData {
  id?: string;
  requestBy: string;
  projectNo: string;
  pidProject?: string;
  tireSize: string;
  requestDate: Date;
  targetDate: Date;
  type: string;
  status?: string;
  note?: string;
}

const RequestValidationSchema: ValidationSchema<RequestFormData> = {
  name: 'RequestValidation',
  zodSchema: z.object({
    id: z.string().optional(),
    requestBy: SharedValidationRules.required.zodSchema,
    projectNo: SharedValidationRules.required.zodSchema,
    pidProject: z.string().optional(),
    tireSize: SharedValidationRules.required.zodSchema,
    requestDate: z.date(),
    targetDate: z.date(),
    type: SharedValidationRules.required.zodSchema,
    status: z.string().optional(),
    note: z.string().optional()
  }),
  formRules: {
    requestBy: [SharedValidationRules.required.formRule('Request by is required')],
    projectNo: [SharedValidationRules.required.formRule('Project number is required')],
    tireSize: [SharedValidationRules.required.formRule('Tire size is required')],
    type: [SharedValidationRules.required.formRule('Type is required')]
  },
  crossFieldRules: [
    (data) => {
      if (data.requestDate && data.targetDate && data.targetDate <= data.requestDate) {
        return 'Target date must be after request date';
      }
      return undefined;
    }
  ]
};

// ============================================================================
// NEW SIMPLIFIED VALIDATION SCHEMAS - MIGRATION TARGET
// ============================================================================

/**
 * Enhanced Tire Form Validation Schema
 * For the new enhanced tire catalog (master data)
 */
interface EnhancedTireFormData {
  id?: string;
  tugNo: string;
  specNo: string;
  size: string;
  owner: string;
  loadIndex: string;
  pattern: string;
  projectNo: string;
  location: string;
  description?: string;
  isActive?: boolean;
}

export const enhancedTireValidationSchema: ValidationSchema<EnhancedTireFormData> = {
  formRules: {
    tugNo: [SharedValidationRules.required.formRule('TUG number is required')],
    specNo: [SharedValidationRules.required.formRule('Spec number is required')],
    size: [SharedValidationRules.required.formRule('Tire size is required')],
    owner: [SharedValidationRules.required.formRule('Owner is required')],
    loadIndex: [SharedValidationRules.required.formRule('Load index is required')],
    pattern: [SharedValidationRules.required.formRule('Pattern is required')],
    projectNo: [SharedValidationRules.required.formRule('Project number is required')],
    location: [SharedValidationRules.required.formRule('Location is required')]
  },
  crossFieldRules: []
};

/**
 * Request Item Form Validation Schema
 * For the new simplified request items (replacement for request details)
 */
interface RequestItemFormData {
  id?: string;
  requestId: string;
  tireId: string;
  quantity: number;
  disposition: string;
  notes?: string;
  unitPrice?: number;
  section?: string;
}

export const requestItemValidationSchema: ValidationSchema<RequestItemFormData> = {
  formRules: {
    requestId: [SharedValidationRules.required.formRule('Request ID is required')],
    tireId: [SharedValidationRules.required.formRule('Tire ID is required')],
    quantity: [
      SharedValidationRules.required.formRule('Quantity is required'),
      (value) => {
        if (typeof value === 'number' && value <= 0) {
          return 'Quantity must be greater than 0';
        }
        return undefined;
      }
    ],
    disposition: [SharedValidationRules.required.formRule('Disposition is required')],
    unitPrice: [
      (value) => {
        if (value !== undefined && typeof value === 'number' && value < 0) {
          return 'Unit price cannot be negative';
        }
        return undefined;
      }
    ]
  },
  crossFieldRules: []
};

/**
 * Cut Operation Form Validation Schema
 * For the new unified cut operations (replacement for RequestDetailCut + CutProcessing)
 */
interface CutOperationFormData {
  requestItemId: string;
  processingId: string;
  quantity: number;
  cutPrice?: number;
  status?: string;
  notes?: string;
}

export const cutOperationValidationSchema: ValidationSchema<CutOperationFormData> = {
  formRules: {
    requestItemId: [SharedValidationRules.required.formRule('Request item ID is required')],
    processingId: [SharedValidationRules.required.formRule('Processing ID is required')],
    quantity: [
      SharedValidationRules.required.formRule('Quantity is required'),
      (value) => {
        if (typeof value === 'number' && value <= 0) {
          return 'Quantity must be greater than 0';
        }
        return undefined;
      }
    ],
    cutPrice: [
      (value) => {
        if (value !== undefined && typeof value === 'number' && value < 0) {
          return 'Cut price cannot be negative';
        }
        return undefined;
      }
    ],
    status: [
      (value) => {
        if (value && !['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'].includes(value)) {
          return 'Invalid status value';
        }
        return undefined;
      }
    ]
  },
  crossFieldRules: []
};

// ============================================================================
// LEGACY VALIDATION SCHEMAS - FOR BACKWARD COMPATIBILITY
// ============================================================================

/**
 * Tire Form Validation Schema
 * Consolida validazioni da tire-form.tsx:49 e backend validation
 */
interface TireFormData {
  id?: string;
  tugNo: string;
  projectNo: string;
  tireSize: string;
  quantity: number;
  note?: string;
  disposition?: string;
}

const TireValidationSchema: ValidationSchema<TireFormData> = {
  name: 'TireValidation',
  zodSchema: z.object({
    id: z.string().optional(),
    tugNo: SharedValidationRules.required.zodSchema,
    projectNo: SharedValidationRules.required.zodSchema,
    tireSize: SharedValidationRules.required.zodSchema,
    quantity: SharedValidationRules.positiveNumber.zodSchema,
    note: z.string().optional(),
    disposition: z.string().optional()
  }),
  formRules: {
    tugNo: [SharedValidationRules.required.formRule('TUG number is required')],
    projectNo: [SharedValidationRules.required.formRule('Project number is required')],
    tireSize: [SharedValidationRules.required.formRule('Tire size is required')],
    quantity: [
      SharedValidationRules.required.formRule('Quantity is required'),
      SharedValidationRules.positiveNumber.formRule()
    ]
  }
};

/**
 * User Form Validation Schema
 * Consolida validazioni email frontend + backend
 */
interface UserFormData {
  id?: number;
  email: string;
  fullName?: string;
  role?: string;
  password?: string;
  isActive?: boolean;
}

const UserValidationSchema: ValidationSchema<UserFormData> = {
  name: 'UserValidation',
  zodSchema: z.object({
    id: z.number().optional(),
    email: SharedValidationRules.email.zodSchema,
    fullName: z.string().optional(),
    role: z.string().optional(),
    password: z.string().min(6, 'Password must be at least 6 characters').optional(),
    isActive: z.boolean().optional()
  }),
  formRules: {
    email: [
      SharedValidationRules.required.formRule('Email is required'),
      SharedValidationRules.email.formRule()
    ],
    password: [SharedValidationRules.stringLength(6).formRule()]
  }
};

/**
 * Processing Form Validation Schema
 */
interface ProcessingFormData {
  id: string;
  tireType: string;
  description1: string;
  description2: string;
  testCode1: string;
  testCode2: string;
  cost: string;
  picture?: boolean;
}

const ProcessingValidationSchema: ValidationSchema<ProcessingFormData> = {
  name: 'ProcessingValidation',
  zodSchema: z.object({
    id: SharedValidationRules.required.zodSchema,
    tireType: SharedValidationRules.required.zodSchema,
    description1: SharedValidationRules.required.zodSchema,
    description2: SharedValidationRules.required.zodSchema,
    testCode1: SharedValidationRules.required.zodSchema,
    testCode2: SharedValidationRules.required.zodSchema,
    cost: SharedValidationRules.required.zodSchema,
    picture: z.boolean().optional()
  }),
  formRules: {
    id: [SharedValidationRules.required.formRule('ID is required')],
    tireType: [SharedValidationRules.required.formRule('Tire type is required')],
    description1: [SharedValidationRules.required.formRule('Description 1 is required')],
    description2: [SharedValidationRules.required.formRule('Description 2 is required')],
    testCode1: [SharedValidationRules.required.formRule('Test code 1 is required')],
    testCode2: [SharedValidationRules.required.formRule('Test code 2 is required')],
    cost: [SharedValidationRules.required.formRule('Cost is required')]
  }
};

// ============================================================================
// VALIDATION ENGINE
// ============================================================================

/**
 * Unified Validation Engine
 * Supporta validazione real-time, batch e cross-field
 */
class ValidationEngine {

  /**
   * Validate data using a validation schema
   */
  static validate<T>(
    data: T,
    schema: ValidationSchema<T>,
    options: ValidationOptions = {}
  ): ValidationResult {
    const errors: Record<string, string> = {};
    const warnings: Record<string, string> = {};
    const { skip = [], fields, includeWarnings = false } = options;

    try {
      // Zod schema validation
      if (schema.zodSchema && !skip.includes('format')) {
        try {
          schema.zodSchema.parse(data);
        } catch (zodError: any) {
          if (zodError.errors) {
            zodError.errors.forEach((error: any) => {
              const field = error.path.join('.');
              if (!fields || fields.includes(field)) {
                errors[field] = error.message;
              }
            });
          }
        }
      }

      // Form rules validation (for Universal Form Hook integration)
      if (schema.formRules && !skip.includes('required')) {
        Object.entries(schema.formRules).forEach(([field, rules]) => {
          if (fields && !fields.includes(field)) return;

          if (Array.isArray(rules)) {
            rules.forEach((rule: ValidationRule<T>) => {
              const error = rule.validate((data as any)[field], data);
              if (error && !errors[field]) {
                errors[field] = error;
              }
            });
          }
        });
      }

      // Cross-field validation
      if (schema.crossFieldRules && !skip.includes('crossField')) {
        schema.crossFieldRules.forEach(rule => {
          const error = rule(data);
          if (error) {
            errors['_crossField'] = error;
          }
        });
      }

      // Business rules validation
      if (schema.businessRules && !skip.includes('business')) {
        schema.businessRules.forEach(rule => {
          const error = rule(data);
          if (error) {
            errors['_business'] = error;
          }
        });
      }

      return {
        isValid: Object.keys(errors).length === 0,
        errors,
        ...(includeWarnings && { warnings })
      };

    } catch (error) {
      console.error(`Validation error in schema ${schema.name}:`, error);
      return {
        isValid: false,
        errors: { _system: 'Validation system error' }
      };
    }
  }

  /**
   * Validate multiple data objects in batch
   */
  static validateBatch<T>(
    dataArray: T[],
    schema: ValidationSchema<T>,
    options: ValidationOptions = {}
  ): ValidationResult[] {
    return dataArray.map(data => this.validate(data, schema, options));
  }

  /**
   * Validate specific field only
   */
  static validateField<T>(
    data: T,
    field: keyof T,
    schema: ValidationSchema<T>,
    options: ValidationOptions = {}
  ): ValidationResult {
    return this.validate(data, schema, {
      ...options,
      fields: [field as string]
    });
  }

  /**
   * Get validation rules for Universal Form Hook
   */
  static getFormValidationConfig<T>(
    schema: ValidationSchema<T>
  ): Partial<Record<keyof T, { required?: boolean; rules?: ValidationRule<T>[] }>> {
    const config: any = {};

    if (schema.formRules) {
      Object.entries(schema.formRules).forEach(([field, rules]) => {
        if (Array.isArray(rules)) {
          config[field] = {
            required: rules.some((rule: ValidationRule<T>) =>
              rule.validate('', {} as T) === 'This field is required' ||
              rule.validate('', {} as T)?.includes('required')
            ),
            rules: rules
          };
        }
      });
    }

    return config;
  }
}

// ============================================================================
// INTEGRATION UTILITIES
// ============================================================================

/**
 * Utility functions for integration with existing components
 */
class ValidationIntegration {

  /**
   * Convert validation result to Universal Form Hook format
   */
  static toFormErrors(result: ValidationResult): Record<string, string> {
    const formErrors: Record<string, string> = {};

    Object.entries(result.errors).forEach(([field, error]) => {
      if (!field.startsWith('_')) {
        formErrors[field] = error;
      }
    });

    return formErrors;
  }

  /**
   * Create validation function for Universal Form Hook
   */
  static createFormValidator<T>(
    schema: ValidationSchema<T>,
    options: ValidationOptions = {}
  ) {
    return (data: T): Record<string, string> => {
      const result = ValidationEngine.validate(data, schema, options);
      return this.toFormErrors(result);
    };
  }

  /**
   * Create real-time field validator
   */
  static createFieldValidator<T>(
    schema: ValidationSchema<T>,
    field: keyof T
  ) {
    return (value: any, formData: T): string | undefined => {
      const result = ValidationEngine.validateField(
        { ...formData, [field]: value },
        field,
        schema
      );
      return result.errors[field as string];
    };
  }
}

// ============================================================================
// PRESET CONFIGURATIONS
// ============================================================================

/**
 * Preset configurations for common validation scenarios
 */
const ValidationPresets = {

  /**
   * Standard form validation with real-time feedback
   */
  STANDARD_FORM: {
    includeWarnings: false,
    skip: []
  } as ValidationOptions,

  /**
   * Lightweight validation for performance-critical scenarios
   */
  LIGHTWEIGHT: {
    includeWarnings: false,
    skip: ['business', 'crossField']
  } as ValidationOptions,

  /**
   * Comprehensive validation for final submission
   */
  COMPREHENSIVE: {
    includeWarnings: true,
    skip: []
  } as ValidationOptions,

  /**
   * Backend-compatible validation
   */
  BACKEND_COMPATIBLE: {
    includeWarnings: false,
    skip: ['crossField'] // Backend handles cross-field validation separately
  } as ValidationOptions
};

// ============================================================================
// EXPORTS
// ============================================================================

export {
  // Core types
  type ValidationSchema,
  type ValidationResult,
  type ValidationOptions,

  // Domain schemas
  RequestValidationSchema,
  TireValidationSchema,
  UserValidationSchema,
  ProcessingValidationSchema,

  // Domain types
  type RequestFormData,
  type TireFormData,
  type UserFormData,
  type ProcessingFormData,

  // Engine and utilities
  ValidationEngine,
  ValidationIntegration,
  ValidationPresets,
  SharedValidationRules
};