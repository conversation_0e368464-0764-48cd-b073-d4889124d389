"use client";

import type { ComponentProps } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";

interface DashboardAppBarProps extends ComponentProps<"header"> {
  title: string;
}

export function DashboardAppBar({ title, className, ...props }: DashboardAppBarProps) {
  const { isMobile } = useSidebar(); // Get isMobile from context
  const { logout, user } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <header
      className={cn(
        "bg-primary text-primary-foreground h-16 flex items-center px-4 sm:px-6 lg:px-8 sticky top-0 z-30 w-full shadow-md",
        className
      )}
      {...props}
    >
      {isMobile && ( // Conditionally render SidebarTrigger based on isMobile
        <SidebarTrigger className="mr-2 text-primary-foreground" />
      )}
      <h1 className="text-xl font-medium tracking-wide">{title}</h1>
      <div className="ml-auto flex items-center space-x-4">
        <span className="text-sm hidden sm:inline font-medium">{user?.full_name || user?.email || 'User'}</span>
        <Button variant="ghost" className="text-primary-foreground hover:bg-primary-foreground/10" onClick={handleLogout}>
          Logout
        </Button>
      </div>
    </header>
  );
}
