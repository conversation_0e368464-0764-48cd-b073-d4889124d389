
"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { FileText, Paperclip, Users, Calendar as CalendarIcon, AlertCircle } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge"; // Added Badge import
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import type { RequestFormData } from "@/types";
import { filterOptions } from "./request-filters";

interface FormFieldProps {
  id: string;
  label: string;
  children: React.ReactNode;
  className?: string;
  required?: boolean;
  error?: string;
}

const FormField: React.FC<FormFieldProps> = ({ id, label, children, className, required, error }) => (
  <div className={cn("space-y-1.5", className)}>
    <div className="flex items-center">
      <Label htmlFor={id}>{label}</Label>
      {required && <span className="text-red-500 ml-1">*</span>}
    </div>
    {children}
    {error && (
      <div className="flex items-center text-red-500 text-xs mt-1">
        <AlertCircle className="h-3 w-3 mr-1" />
        <span>{error}</span>
      </div>
    )}
  </div>
);

interface RequestDetailsProps {
  formData: RequestFormData;
  onFormChange: (field: keyof RequestFormData, value: any) => void;
  onIconClick: (action: "report" | "inChargeOf" | "attach") => void;
  errors?: Record<string, string>;
  setErrors?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
}

export function validateRequestForm(formData: RequestFormData): Record<string, string> {
  const errors: Record<string, string> = {};

  // Required fields based on backend schema
  if (!formData.requestBy?.trim()) errors.requestBy = "Required";
  if (!formData.tireSize?.trim()) errors.tireSize = "Required";
  if (!formData.requestDate) errors.requestDate = "Required";
  if (!formData.targetDate) errors.targetDate = "Required";
  if (!formData.type?.trim()) errors.type = "Required";

  return errors;
}

export function RequestDetailsSection({
  formData,
  onFormChange,
  onIconClick,
  errors = {},
  setErrors
}: RequestDetailsProps) {
  // Ensure formData is never null/undefined to prevent controlled/uncontrolled input issues
  const safeFormData = React.useMemo(() => {
    console.log('RequestDetailsSection: Processing formData:', formData);

    if (!formData) {
      console.warn('RequestDetailsSection: formData is null/undefined, using default values');
      return {
        id: undefined,
        requestBy: "",
        projectNo: "",
        pidProject: "",
        tireSize: "",
        requestDate: undefined,
        targetDate: undefined,
        status: "DRAFT",
        type: "NEWDEV",
        totalN: undefined,
        destination: "PLANT_A",
        internal: false,
        wishDate: undefined,
        numPneusSet: undefined,
        numPneusSetUnit: "SET" as const,
        inChargeOf: "",
        note: "",
        attachments: [],
      };
    }

    // Log original values for debugging
    console.log('RequestDetailsSection: Original formData values:', {
      requestBy: formData.requestBy,
      projectNo: formData.projectNo,
      tireSize: formData.tireSize,
      status: formData.status,
      type: formData.type,
      requestDate: formData.requestDate,
      targetDate: formData.targetDate
    });

    // Sanitize the formData to ensure no null values that could cause input issues
    const sanitized = {
      ...formData,
      requestBy: formData.requestBy ?? "",
      projectNo: formData.projectNo ?? "",
      pidProject: formData.pidProject ?? "",
      tireSize: formData.tireSize ?? "",
      status: formData.status ?? "DRAFT",
      type: formData.type ?? "NEWDEV",
      destination: formData.destination ?? "PLANT_A",
      inChargeOf: formData.inChargeOf ?? "",
      note: formData.note ?? "",
      numPneusSetUnit: formData.numPneusSetUnit ?? "SET",
      attachments: formData.attachments ?? [],
      // Keep dates as they are (can be null/undefined for Calendar component)
      requestDate: formData.requestDate,
      targetDate: formData.targetDate,
      wishDate: formData.wishDate,
    };

    console.log('RequestDetailsSection: Sanitized formData values:', {
      requestBy: sanitized.requestBy,
      projectNo: sanitized.projectNo,
      tireSize: sanitized.tireSize,
      status: sanitized.status,
      type: sanitized.type,
      requestDate: sanitized.requestDate,
      targetDate: sanitized.targetDate
    });

    return sanitized;
  }, [formData]);

  // Utility function to safely handle any value for input elements
  const safeInputValue = (value: any): string => {
    if (value === null || value === undefined) return "";
    return String(value);
  };

  const handleInputChange = (field: keyof RequestFormData, value: string | number | boolean | Date | null | undefined) => {
    onFormChange(field, value);

    // Clear error for this field if it exists
    if (errors[field] && setErrors) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleNumericInputChange = (field: 'totalN' | 'numPneusSet', value: string) => {
    const numValue = value === '' ? undefined : parseInt(value, 10);
    if (value === '' || (!isNaN(numValue!) && numValue! >= 0) ) { // Allow empty or valid numbers
      onFormChange(field, value === '' ? undefined : numValue); // Don't fallback to 0, use undefined for empty
    }
  };

  return (
    <section aria-labelledby="request-details-heading">
      <Card>
        <CardHeader>
          <CardTitle id="request-details-heading" className="text-xl">REQUEST:</CardTitle>
          {Object.keys(errors).length > 0 && (
            <div className="text-red-500 text-sm flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span>Please fill in all required fields</span>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-4">
            <FormField id="request" label="Request:">
              <Input id="request" value={safeInputValue(safeFormData.id) || "NEW"} readOnly />
            </FormField>
            <FormField id="status" label="Status:">
              <Select
                value={safeFormData.status || "DRAFT"}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.map((option) => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>
            <FormField id="totalN" label="Total N°:">
              <Input
                id="totalN"
                type="number"
                value={safeInputValue(safeFormData.totalN)}
                onChange={(e) => handleNumericInputChange('totalN', e.target.value)}
                min="0"
              />
            </FormField>
            <FormField
              id="requestBy"
              label="Request By:"
              required
              error={errors.requestBy}
            >
              <Input
                id="requestBy"
                value={safeInputValue(safeFormData.requestBy)}
                onChange={(e) => handleInputChange('requestBy', e.target.value)}
                className={errors.requestBy ? "border-red-500" : ""}
              />
            </FormField>

            <FormField
              id="type"
              label="Type:"
              required
              error={errors.type}
            >
              <Select
                value={safeFormData.type ?? "NEWDEV"}
                onValueChange={(value) => handleInputChange('type', value)}
              >
                <SelectTrigger
                  id="type"
                  className={errors.type ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NEWDEV">NEWDEV</SelectItem>
                  <SelectItem value="STANDARD">STANDARD</SelectItem>
                  <SelectItem value="URGENT">URGENT</SelectItem>
                </SelectContent>
              </Select>
            </FormField>
            <FormField id="destination" label="Destination:">
              <Select
                value={safeFormData.destination ?? "PLANT_A"}
                onValueChange={(value) => handleInputChange('destination', value)}
              >
                <SelectTrigger id="destination">
                  <SelectValue placeholder="Select destination" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PLANT_A">Plant A</SelectItem>
                  <SelectItem value="PLANT_B">Plant B</SelectItem>
                  <SelectItem value="WAREHOUSE_C">Warehouse C</SelectItem>
                </SelectContent>
              </Select>
            </FormField>

            <FormField
              id="requestDate"
              label="Request Date:"
              required
              error={errors.requestDate}
            >
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !safeFormData.requestDate && "text-muted-foreground",
                      errors.requestDate && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {safeFormData.requestDate ? format(new Date(safeFormData.requestDate), "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={safeFormData.requestDate ? new Date(safeFormData.requestDate) : undefined}
                    onSelect={(date) => handleInputChange('requestDate', date || undefined)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </FormField>

            <FormField
              id="targetDate"
              label="Target Date:"
              required
              error={errors.targetDate}
            >
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !safeFormData.targetDate && "text-muted-foreground",
                      errors.targetDate && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {safeFormData.targetDate ? format(new Date(safeFormData.targetDate), "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={safeFormData.targetDate ? new Date(safeFormData.targetDate) : undefined}
                    onSelect={(date) => handleInputChange('targetDate', date || undefined)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </FormField>

            <div className="flex items-center space-x-2 pt-7">
              <Checkbox
                id="internal"
                checked={safeFormData.internal || false}
                onCheckedChange={(checked) => handleInputChange('internal', checked as boolean)}
              />
              <Label htmlFor="internal" className="font-normal">Internal</Label>
            </div>

            <FormField id="wishDate" label="Wish Date:">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !safeFormData.wishDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {safeFormData.wishDate ? format(new Date(safeFormData.wishDate), "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={safeFormData.wishDate ? new Date(safeFormData.wishDate) : undefined}
                    onSelect={(date) => handleInputChange('wishDate', date || undefined)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </FormField>

            <FormField id="report" label="Report:">
              <Button variant="outline" size="icon" className="w-full" aria-label="View Report" onClick={() => onIconClick("report")}>
                <FileText className="h-5 w-5" />
              </Button>
            </FormField>

            <FormField id="pidProject" label="Pid/Project#:">
              <Input
                id="pidProject"
                value={safeInputValue(safeFormData.pidProject)}
                onChange={(e) => handleInputChange('pidProject', e.target.value)}
              />
            </FormField>
            <FormField
              id="tireSize"
              label="Tire size:"
              required
              error={errors.tireSize}
            >
              <Input
                id="tireSize"
                value={safeInputValue(safeFormData.tireSize)}
                onChange={(e) => handleInputChange('tireSize', e.target.value)}
                className={errors.tireSize ? "border-red-500" : ""}
              />
            </FormField>

            <FormField id="numPneusSet" label="Num. pneus x set:">
              <div className="flex items-center gap-2">
                <Input
                  id="numPneusSet"
                  type="number"
                  value={safeInputValue(safeFormData.numPneusSet)}
                  onChange={(e) => handleNumericInputChange('numPneusSet', e.target.value)}
                  className="w-20"
                  min="0"
                />
                <Select
                  value={safeFormData.numPneusSetUnit ?? "SET"}
                  onValueChange={(value) => handleInputChange('numPneusSetUnit', value as "SET" | "PCS")}
                >
                  <SelectTrigger className="flex-grow min-w-[80px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SET">SET</SelectItem>
                    <SelectItem value="PCS">PCS</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </FormField>

            <FormField id="inChargeOf" label="In Charge Of:" className="xl:col-span-2">
              <div className="flex items-center gap-2">
                <Input
                  id="inChargeOf"
                  value={safeInputValue(safeFormData.inChargeOf)}
                  onChange={(e) => handleInputChange('inChargeOf', e.target.value)}
                  className="flex-grow"
                />
                <Button variant="outline" size="icon" aria-label="Select Person In Charge" onClick={() => onIconClick("inChargeOf")}>
                  <Users className="h-5 w-5" />
                </Button>
              </div>
            </FormField>

            <FormField id="attach" label="Attach:">
              <div className="relative w-full">
                <Button variant="outline" size="icon" className="w-full" aria-label="Attach File" onClick={() => onIconClick("attach")}>
                  <Paperclip className="h-5 w-5" />
                </Button>
                {safeFormData.attachments && safeFormData.attachments.length > 0 && (
                  <Badge
                    variant="secondary"
                    className="absolute -top-1 -right-1 px-1.5 py-0.5 text-xs font-semibold rounded-full pointer-events-none"
                  >
                    {safeFormData.attachments.length}
                  </Badge>
                )}
              </div>
            </FormField>

            <FormField id="note" label="Note:" className="sm:col-span-2 lg:col-span-3 xl:col-span-4">
              <Textarea
                id="note"
                placeholder="Enter notes here..."
                rows={3}
                value={safeInputValue(safeFormData.note)}
                onChange={(e) => handleInputChange('note', e.target.value)}
              />
            </FormField>
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
