from sqlalchemy.orm import Session
from . import models, schemas
from typing import Dict, Any, Optional, List # Added for type hinting
import uuid
from datetime import datetime

# Removed redundant pwd_context, get_password_hash, verify_password as they are in auth.py

def get_user(db: Session, user_id: int):
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_email(db: Session, email: str):
    return db.query(models.User).filter(models.User.email == email).first()

def get_users(db: Session, skip: int = 0, limit: int = 10):
    return db.query(models.User).offset(skip).limit(limit).all()

def create_user(db: Session, user_data: Dict[str, Any], hashed_password: str, role: str):
    db_user = models.User(
        **user_data,  # Unpack the dictionary
        hashed_password=hashed_password,
        role=role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def authenticate_user(db: Session, email: str, password: str):
    """Authenticate a user by email and password"""
    from . import auth_simple as auth

    user = get_user_by_email(db, email)
    if not user:
        print(f"User not found: {email}")
        return False
    if not user.is_active:
        print(f"User not active: {email}")
        return False
    if not auth.verify_password(password, user.hashed_password):
        print(f"Password verification failed for: {email}")
        return False
    return user

# --- CRUD for Requests ---

def create_request(db: Session, request: schemas.RequestCreate):
    db_request = models.Request(
        id=request.id,
        request_by=request.request_by,
        project_no=request.project_no,
        pid_project=request.pid_project,
        tire_size=request.tire_size,
        request_date=request.request_date,
        target_date=request.target_date,
        status=request.status,
        type=request.type,
        total_n=request.total_n,
        destination=request.destination,
        internal=request.internal,
        wish_date=request.wish_date,
        num_pneus_set=request.num_pneus_set,
        num_pneus_set_unit=request.num_pneus_set_unit,
        in_charge_of=request.in_charge_of,
        note=request.note,
    )
    # Handle attachments if present
    if request.attachments:
        for att in request.attachments:
            db_attachment = models.Attachment(
                id=att.id,
                name=att.name,
                size=att.size,
                type=att.type,
                upload_date=att.upload_date,
                status=att.status,
                request_id=db_request.id  # Explicitly set the request_id
            )
            db_request.attachments.append(db_attachment)
    db.add(db_request)
    db.commit()
    db.refresh(db_request)
    return db_request

def get_request(db: Session, request_id: str):
    return db.query(models.Request).filter(models.Request.id == request_id).first()

from typing import Optional

def get_requests(db: Session, skip: int = 0, limit: int = 100, status: Optional[str] = None):
    query = db.query(models.Request)
    if status:
        query = query.filter(models.Request.status == status)
    return query.offset(skip).limit(limit).all()

def get_requests_summary(db: Session, skip: int = 0, limit: int = 100, status: Optional[str] = None):
    """
    Get requests without loading nested relationships for better performance in lists.
    """
    from sqlalchemy.orm import noload
    query = db.query(models.Request).options(
        noload(models.Request.attachments),
        noload(models.Request.request_details)
    )
    if status:
        query = query.filter(models.Request.status == status)
    return query.offset(skip).limit(limit).all()

def update_request(db: Session, request_id: str, request: schemas.RequestUpdate):
    db_request = db.query(models.Request).filter(models.Request.id == request_id).first()
    if not db_request:
        return None
    for field, value in request.dict(exclude_unset=True).items():
        if field in ("attachments", "request_details"):
            continue  # Attachments and request_details handled separately
        setattr(db_request, field, value)
    # Optionally update attachments (simple replace)
    if request.attachments is not None:
        db_request.attachments.clear()
        for att in request.attachments:
            db_attachment = models.Attachment(
                id=att.id,
                name=att.name,
                size=att.size,
                type=att.type,
                upload_date=att.upload_date,
                status=att.status,
                request=db_request
            )
            db_request.attachments.append(db_attachment)
    db.commit()
    db.refresh(db_request)
    return db_request

def delete_request(db: Session, request_id: str):
    db_request = db.query(models.Request).filter(models.Request.id == request_id).first()
    if not db_request:
        return None
    db.delete(db_request)
    db.commit()
    return db_request

# --- CRUD for RequestDetail (Tire) ---
def update_or_create_request_detail(db: Session, detail_data: schemas.RequestDetailBase):
    db_detail = db.query(models.RequestDetail).filter_by(id=detail_data.id).first()
    if db_detail:
        for field, value in detail_data.dict().items():
            setattr(db_detail, field, value)
    else:
        db_detail = models.RequestDetail(**detail_data.dict())
        db.add(db_detail)
    db.commit()
    db.refresh(db_detail)
    return db_detail

def update_request_detail(db: Session, detail_id: str, detail: schemas.RequestDetailUpdate):
    """
    Update an existing request detail record.

    Args:
        db: Database session
        detail_id: ID of the request detail to update
        detail: Updated request detail data

    Returns:
        The updated request detail object if found, None otherwise
    """
    db_detail = db.query(models.RequestDetail).filter(models.RequestDetail.id == detail_id).first()
    if not db_detail:
        return None

    # Map RequestDetailUpdate fields to RequestDetail model fields
    field_mapping = {
        'tug_no': 'tug_number',
        'project_no': 'project_number',
        'tire_size': 'tire_size',
        'note': 'note',
        'disposition': 'disposition',
        'quantity': 'process_number'  # quantity maps to process_number in the model
    }

    # Update only the fields that are provided
    update_data = detail.dict(exclude_unset=True, exclude={'id'})  # Exclude id from updates
    for field, value in update_data.items():
        if field in field_mapping:
            model_field = field_mapping[field]
            setattr(db_detail, model_field, value)

    db.commit()
    db.refresh(db_detail)
    return db_detail

def get_request_detail(db: Session, detail_id: str):
    """
    Retrieve a specific request detail by ID.

    Args:
        db: Database session
        detail_id: ID of the request detail to retrieve

    Returns:
        The request detail object if found, None otherwise
    """
    return db.query(models.RequestDetail).filter(models.RequestDetail.id == detail_id).first()

def delete_request_detail(db: Session, detail_id: str):
    """
    Delete a request detail record.

    Args:
        db: Database session
        detail_id: ID of the request detail to delete

    Returns:
        The deleted request detail object if found, None otherwise
    """
    db_detail = db.query(models.RequestDetail).filter(models.RequestDetail.id == detail_id).first()
    if not db_detail:
        return None

    db.delete(db_detail)
    db.commit()
    return db_detail

# --- CRUD for Tires ---

def get_tire(db: Session, tire_id: str):
    """
    Retrieve a specific tire by ID.

    Args:
        db: Database session
        tire_id: ID of the tire to retrieve

    Returns:
        The tire object if found, None otherwise
    """
    return db.query(models.Tire).filter(models.Tire.id == tire_id).first()

def get_tires(db: Session, skip: int = 0, limit: int = 100,
              owner: Optional[str] = None, pattern: Optional[str] = None,
              size: Optional[str] = None, project_no: Optional[str] = None):
    """
    Retrieve all tires with optional filtering.

    Args:
        db: Database session
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)
        owner: Filter by tire owner
        pattern: Filter by tire pattern
        size: Filter by tire size
        project_no: Filter by project number

    Returns:
        List of tire objects matching the criteria
    """
    query = db.query(models.Tire)

    if owner:
        query = query.filter(models.Tire.owner == owner)
    if pattern:
        query = query.filter(models.Tire.pattern == pattern)
    if size:
        query = query.filter(models.Tire.size == size)
    if project_no:
        query = query.filter(models.Tire.project_no == project_no)

    return query.offset(skip).limit(limit).all()

def create_tire(db: Session, tire: schemas.TireCreate):
    """
    Create a new tire record.

    Args:
        db: Database session
        tire: Tire data to create

    Returns:
        The created tire object
    """
    db_tire = models.Tire(**tire.dict())
    db.add(db_tire)
    db.commit()
    db.refresh(db_tire)
    return db_tire

def update_tire(db: Session, tire_id: str, tire: schemas.TireUpdate):
    """
    Update an existing tire record.

    Args:
        db: Database session
        tire_id: ID of the tire to update
        tire: Updated tire data

    Returns:
        The updated tire object if found, None otherwise
    """
    db_tire = db.query(models.Tire).filter(models.Tire.id == tire_id).first()
    if not db_tire:
        return None

    # Update only the fields that are provided
    update_data = tire.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_tire, field, value)

    db.commit()
    db.refresh(db_tire)
    return db_tire

def delete_tire(db: Session, tire_id: str):
    """
    Delete a tire record.

    Args:
        db: Database session
        tire_id: ID of the tire to delete

    Returns:
        The deleted tire object if found, None otherwise
    """
    db_tire = db.query(models.Tire).filter(models.Tire.id == tire_id).first()
    if not db_tire:
        return None

    db.delete(db_tire)
    db.commit()
    return db_tire

# --- CRUD for RequestDetailCut ---

def get_request_detail_cut(db: Session, cut_id: int):
    """
    Retrieve a specific request detail cut by ID.

    Args:
        db: Database session
        cut_id: ID of the cut to retrieve

    Returns:
        The cut object if found, None otherwise
    """
    return db.query(models.RequestDetailCut).filter(models.RequestDetailCut.id == cut_id).first()

def get_request_detail_cuts(db: Session, request_detail_id: Optional[str] = None, skip: int = 0, limit: int = 100):
    """
    Retrieve all cuts with optional filtering by request detail.

    Args:
        db: Database session
        request_detail_id: Filter by request detail ID
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)

    Returns:
        List of cut objects matching the criteria
    """
    query = db.query(models.RequestDetailCut)

    if request_detail_id:
        query = query.filter(models.RequestDetailCut.id_request_details == request_detail_id)

    return query.offset(skip).limit(limit).all()

def create_request_detail_cut(db: Session, cut: schemas.RequestDetailCutCreate):
    """
    Create a new request detail cut record.

    Args:
        db: Database session
        cut: Cut data to create

    Returns:
        The created cut object
    """
    db_cut = models.RequestDetailCut(**cut.dict(exclude_unset=True))
    db.add(db_cut)
    db.commit()
    db.refresh(db_cut)
    return db_cut

def update_request_detail_cut(db: Session, cut_id: int, cut: schemas.RequestDetailCutUpdate):
    """
    Update an existing request detail cut record.

    Args:
        db: Database session
        cut_id: ID of the cut to update
        cut: Updated cut data

    Returns:
        The updated cut object if found, None otherwise
    """
    db_cut = db.query(models.RequestDetailCut).filter(models.RequestDetailCut.id == cut_id).first()
    if not db_cut:
        return None

    # Update only the fields that are provided
    update_data = cut.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_cut, field, value)

    db.commit()
    db.refresh(db_cut)
    return db_cut

def delete_request_detail_cut(db: Session, cut_id: int):
    """
    Delete a request detail cut record.

    Args:
        db: Database session
        cut_id: ID of the cut to delete

    Returns:
        The deleted cut object if found, None otherwise
    """
    db_cut = db.query(models.RequestDetailCut).filter(models.RequestDetailCut.id == cut_id).first()
    if not db_cut:
        return None

    db.delete(db_cut)
    db.commit()
    return db_cut

# --- CRUD for CutProcessing ---

def get_cut_processing(db: Session, cut_processing_id: int):
    """
    Retrieve a specific cut processing by ID.

    Args:
        db: Database session
        cut_processing_id: ID of the cut processing to retrieve

    Returns:
        The cut processing object if found, None otherwise
    """
    return db.query(models.CutProcessing).filter(models.CutProcessing.id == cut_processing_id).first()

def get_cut_processing_by_cut(db: Session, cut_id: int):
    """
    Retrieve all processing for a specific cut.

    Args:
        db: Database session
        cut_id: ID of the cut

    Returns:
        List of cut processing objects
    """
    return db.query(models.CutProcessing).filter(models.CutProcessing.cut_id == cut_id).all()

def get_cut_processing_list(db: Session, skip: int = 0, limit: int = 100):
    """
    Retrieve all cut processing with pagination.

    Args:
        db: Database session
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)

    Returns:
        List of cut processing objects
    """
    return db.query(models.CutProcessing).offset(skip).limit(limit).all()

def create_cut_processing(db: Session, cut_processing: schemas.CutProcessingCreate):
    """
    Create a new cut processing record.

    Args:
        db: Database session
        cut_processing: Cut processing data to create

    Returns:
        The created cut processing object
    """
    db_cut_processing = models.CutProcessing(**cut_processing.dict())
    db.add(db_cut_processing)
    db.commit()
    db.refresh(db_cut_processing)
    return db_cut_processing

def update_cut_processing(db: Session, cut_processing_id: int, cut_processing: schemas.CutProcessingUpdate):
    """
    Update an existing cut processing record.

    Args:
        db: Database session
        cut_processing_id: ID of the cut processing to update
        cut_processing: Updated cut processing data

    Returns:
        The updated cut processing object if found, None otherwise
    """
    db_cut_processing = db.query(models.CutProcessing).filter(models.CutProcessing.id == cut_processing_id).first()
    if not db_cut_processing:
        return None

    # Update only the fields that are provided
    update_data = cut_processing.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_cut_processing, field, value)

    db.commit()
    db.refresh(db_cut_processing)
    return db_cut_processing

def delete_cut_processing(db: Session, cut_processing_id: int):
    """
    Delete a cut processing record.

    Args:
        db: Database session
        cut_processing_id: ID of the cut processing to delete

    Returns:
        The deleted cut processing object if found, None otherwise
    """
    db_cut_processing = db.query(models.CutProcessing).filter(models.CutProcessing.id == cut_processing_id).first()
    if not db_cut_processing:
        return None

    db.delete(db_cut_processing)
    db.commit()
    return db_cut_processing

def delete_cut_processing_by_cut_and_processing(db: Session, cut_id: int, processing_id: str):
    """
    Delete a specific cut processing by cut_id and processing_id.

    Args:
        db: Database session
        cut_id: ID of the cut
        processing_id: ID of the processing

    Returns:
        The deleted cut processing object if found, None otherwise
    """
    db_cut_processing = db.query(models.CutProcessing).filter(
        models.CutProcessing.cut_id == cut_id,
        models.CutProcessing.processing_id == processing_id
    ).first()

    if not db_cut_processing:
        return None

    db.delete(db_cut_processing)
    db.commit()
    return db_cut_processing

# --- Enhanced Processing CRUD for search ---

def search_processing(db: Session, filters: schemas.ProcessingSearchFilters, skip: int = 0, limit: int = 100):
    """
    Search processing with advanced filters.

    Args:
        db: Database session
        filters: Search filters
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)

    Returns:
        List of processing objects matching the criteria
    """
    query = db.query(models.Processing)

    if filters.tire_type:
        query = query.filter(models.Processing.tire_type.ilike(f"%{filters.tire_type}%"))
    if filters.description1:
        query = query.filter(models.Processing.description1.ilike(f"%{filters.description1}%"))
    if filters.description2:
        query = query.filter(models.Processing.description2.ilike(f"%{filters.description2}%"))
    if filters.test_code1:
        query = query.filter(models.Processing.test_code1.ilike(f"%{filters.test_code1}%"))
    if filters.test_code2:
        query = query.filter(models.Processing.test_code2.ilike(f"%{filters.test_code2}%"))
    if filters.picture is not None:
        query = query.filter(models.Processing.picture == filters.picture)

    # Cost filtering (assuming cost is stored as string like "52.41 €")
    if filters.min_cost is not None or filters.max_cost is not None:
        # This is a simplified approach - in production you might want to store cost as float
        if filters.min_cost is not None:
            query = query.filter(models.Processing.cost.regexp(r"^[0-9]*\.?[0-9]+"))
        if filters.max_cost is not None:
            query = query.filter(models.Processing.cost.regexp(r"^[0-9]*\.?[0-9]+"))

    return query.offset(skip).limit(limit).all()

def get_tire_processing_by_request_detail(db: Session, request_detail_id: str):
    """
    Get all processing data for a specific tire (request detail).

    This function aggregates all processing associated with cuts for a given tire.
    Handles cases where processing records might be missing (orphaned cut_processing records).

    Args:
        db: Database session
        request_detail_id: ID of the request detail (tire)

    Returns:
        List of cut processing with processing details for the specified tire
    """
    from sqlalchemy.orm import joinedload

    try:
        # Query CutProcessing with LEFT JOIN to handle missing processing records
        result = db.query(models.CutProcessing).options(
            joinedload(models.CutProcessing.processing)
        ).join(
            models.RequestDetailCut, models.CutProcessing.cut_id == models.RequestDetailCut.id
        ).outerjoin(
            models.Processing, models.CutProcessing.processing_id == models.Processing.id
        ).filter(
            models.RequestDetailCut.id_request_details == request_detail_id
        ).all()

        return result
    except Exception as e:
        # Fallback: if the above fails, try a simpler query without eager loading
        print(f"Warning: Failed to load processing with eager loading: {e}")

        # Get cut processing records without eager loading
        result = db.query(models.CutProcessing).join(
            models.RequestDetailCut, models.CutProcessing.cut_id == models.RequestDetailCut.id
        ).filter(
            models.RequestDetailCut.id_request_details == request_detail_id
        ).all()

        # Manually load processing for each record that has a valid processing_id
        for cut_processing in result:
            try:
                processing = db.query(models.Processing).filter(
                    models.Processing.id == cut_processing.processing_id
                ).first()
                cut_processing.processing = processing
            except Exception:
                cut_processing.processing = None

        return result

# ============================================================================
# NEW SIMPLIFIED CRUD OPERATIONS - MIGRATION TARGET
# ============================================================================

# --- Enhanced Tire CRUD (Master Catalog) ---

def get_tire_enhanced(db: Session, tire_id: str):
    """Get a tire from the enhanced catalog by ID"""
    return db.query(models.Tire).filter(models.Tire.id == tire_id).first()

def get_tires_enhanced(db: Session, skip: int = 0, limit: int = 100,
                      owner: Optional[str] = None, pattern: Optional[str] = None,
                      size: Optional[str] = None, project_no: Optional[str] = None,
                      is_active: Optional[bool] = True):
    """Get tires from enhanced catalog with filtering"""
    query = db.query(models.Tire)

    if is_active is not None:
        query = query.filter(models.Tire.is_active == is_active)
    if owner:
        query = query.filter(models.Tire.owner.ilike(f"%{owner}%"))
    if pattern:
        query = query.filter(models.Tire.pattern.ilike(f"%{pattern}%"))
    if size:
        query = query.filter(models.Tire.size.ilike(f"%{size}%"))
    if project_no:
        query = query.filter(models.Tire.project_no.ilike(f"%{project_no}%"))

    return query.offset(skip).limit(limit).all()

def create_tire_enhanced(db: Session, tire: schemas.TireEnhancedCreate):
    """Create a new tire in the enhanced catalog"""
    db_tire = models.Tire(**tire.dict())
    db.add(db_tire)
    db.commit()
    db.refresh(db_tire)
    return db_tire

def update_tire_enhanced(db: Session, tire_id: str, tire: schemas.TireEnhancedUpdate):
    """Update a tire in the enhanced catalog"""
    db_tire = db.query(models.Tire).filter(models.Tire.id == tire_id).first()
    if not db_tire:
        return None

    update_data = tire.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_tire, field, value)

    db.commit()
    db.refresh(db_tire)
    return db_tire

def delete_tire_enhanced(db: Session, tire_id: str):
    """Soft delete a tire (set is_active = False)"""
    db_tire = db.query(models.Tire).filter(models.Tire.id == tire_id).first()
    if not db_tire:
        return None

    db_tire.is_active = False
    db.commit()
    db.refresh(db_tire)
    return db_tire

# --- RequestItem CRUD (Simplified replacement for RequestDetail) ---

def get_request_item(db: Session, item_id: str):
    """Get a request item by ID with tire details"""
    from sqlalchemy.orm import joinedload
    return db.query(models.RequestItem).options(
        joinedload(models.RequestItem.tire)
    ).filter(models.RequestItem.id == item_id).first()

def get_request_items_by_request(db: Session, request_id: str):
    """Get all request items for a specific request"""
    from sqlalchemy.orm import joinedload
    return db.query(models.RequestItem).options(
        joinedload(models.RequestItem.tire)
    ).filter(models.RequestItem.request_id == request_id).all()

def create_request_item(db: Session, item: schemas.RequestItemCreate):
    """Create a new request item"""
    db_item = models.RequestItem(**item.dict())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

def update_request_item(db: Session, item_id: str, item: schemas.RequestItemUpdate):
    """Update a request item"""
    db_item = db.query(models.RequestItem).filter(models.RequestItem.id == item_id).first()
    if not db_item:
        return None

    update_data = item.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_item, field, value)

    db.commit()
    db.refresh(db_item)
    return db_item

def delete_request_item(db: Session, item_id: str):
    """Delete a request item"""
    db_item = db.query(models.RequestItem).filter(models.RequestItem.id == item_id).first()
    if not db_item:
        return None

    db.delete(db_item)
    db.commit()
    return db_item

# --- CutOperation CRUD (Simplified replacement for RequestDetailCut + CutProcessing) ---

def get_cut_operation(db: Session, operation_id: int):
    """Get a cut operation by ID with processing details"""
    from sqlalchemy.orm import joinedload
    return db.query(models.CutOperation).options(
        joinedload(models.CutOperation.processing)
    ).filter(models.CutOperation.id == operation_id).first()

def get_cut_operations_by_request_item(db: Session, request_item_id: str):
    """Get all cut operations for a specific request item"""
    from sqlalchemy.orm import joinedload
    return db.query(models.CutOperation).options(
        joinedload(models.CutOperation.processing)
    ).filter(models.CutOperation.request_item_id == request_item_id).all()

def get_cut_operations_by_request(db: Session, request_id: str):
    """Get all cut operations for a specific request (across all items)"""
    from sqlalchemy.orm import joinedload
    return db.query(models.CutOperation).options(
        joinedload(models.CutOperation.processing),
        joinedload(models.CutOperation.request_item).joinedload(models.RequestItem.tire)
    ).join(models.RequestItem).filter(
        models.RequestItem.request_id == request_id
    ).all()

def create_cut_operation(db: Session, operation: schemas.CutOperationCreate):
    """Create a new cut operation"""
    operation_data = operation.dict()
    if 'created_date' not in operation_data or operation_data['created_date'] is None:
        operation_data['created_date'] = datetime.utcnow()

    db_operation = models.CutOperation(**operation_data)
    db.add(db_operation)
    db.commit()
    db.refresh(db_operation)
    return db_operation

def update_cut_operation(db: Session, operation_id: int, operation: schemas.CutOperationUpdate):
    """Update a cut operation"""
    db_operation = db.query(models.CutOperation).filter(models.CutOperation.id == operation_id).first()
    if not db_operation:
        return None

    update_data = operation.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_operation, field, value)

    db.commit()
    db.refresh(db_operation)
    return db_operation

def delete_cut_operation(db: Session, operation_id: int):
    """Delete a cut operation"""
    db_operation = db.query(models.CutOperation).filter(models.CutOperation.id == operation_id).first()
    if not db_operation:
        return None

    db.delete(db_operation)
    db.commit()
    return db_operation

# --- Enhanced Request CRUD with simplified structure ---

def get_request_simplified(db: Session, request_id: str):
    """Get a request with simplified structure (request_items instead of request_details)"""
    from sqlalchemy.orm import joinedload
    return db.query(models.Request).options(
        joinedload(models.Request.attachments),
        joinedload(models.Request.request_items).joinedload(models.RequestItem.tire)
    ).filter(models.Request.id == request_id).first()

def get_requests_simplified_summary(db: Session, skip: int = 0, limit: int = 100, status: Optional[str] = None):
    """Get requests summary using simplified structure"""
    from sqlalchemy.orm import noload
    query = db.query(models.Request).options(
        noload(models.Request.attachments),
        noload(models.Request.request_details),  # Don't load legacy structure
        noload(models.Request.request_items)     # Don't load items for summary
    )
    if status:
        query = query.filter(models.Request.status == status)
    return query.offset(skip).limit(limit).all()