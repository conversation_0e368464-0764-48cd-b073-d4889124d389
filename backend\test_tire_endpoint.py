#!/usr/bin/env python3
"""
Test script to verify the tire endpoint logic works correctly
"""

import sys
import os
import sqlite3

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import backend.models as models
import backend.schemas as schemas
from backend.database import SessionLocal

def test_tire_endpoint_logic():
    """Test the logic for the /requests/{request_id}/tires endpoint"""
    
    # Create a database session
    db = SessionLocal()
    
    try:
        request_id = "REQ001"
        
        print(f"Testing tire endpoint logic for request: {request_id}")
        
        # Get request items with tire details (simulating the endpoint logic)
        request_items_with_tires = db.query(models.RequestItem).join(
            models.Tire, models.RequestItem.tire_id == models.Tire.id
        ).filter(models.RequestItem.request_id == request_id).all()
        
        print(f"Found {len(request_items_with_tires)} request items with tires")
        
        # Convert to legacy RequestDetailRead format
        legacy_tire_details = []
        for item in request_items_with_tires:
            tire = item.tire
            print(f"\nProcessing item {item.id}:")
            print(f"  Tire ID: {tire.id}")
            print(f"  Tug Number: {tire.tug_no}")
            print(f"  Spec Number: {tire.spec_no}")
            print(f"  Project Number: {tire.project_no}")
            print(f"  Load Index: {tire.load_index}")
            print(f"  Pattern: {tire.pattern}")
            print(f"  Size: {tire.size}")
            print(f"  Disposition: {item.disposition}")
            print(f"  Quantity: {item.quantity}")
            print(f"  Section: {item.section}")
            print(f"  Notes: {item.notes}")
            
            # Create the legacy format object
            legacy_detail = {
                "id": item.id,
                "request_id": item.request_id,
                "tug_number": tire.tug_no,
                "section": item.section or "SECTION_A",
                "project_number": tire.project_no,
                "spec_number": tire.spec_no,
                "tire_size": tire.size,
                "pattern": tire.pattern,
                "note": item.notes or "",
                "disposition": item.disposition,
                "process_number": item.quantity
            }
            legacy_tire_details.append(legacy_detail)
        
        print(f"\n=== LEGACY FORMAT OUTPUT ===")
        for detail in legacy_tire_details:
            print(detail)
        
        print(f"\n=== SUCCESS: Found {len(legacy_tire_details)} tire details ===")
        
        # Verify all required fields are populated
        for detail in legacy_tire_details:
            assert detail["tug_number"], f"tug_number is empty for item {detail['id']}"
            assert detail["spec_number"], f"spec_number is empty for item {detail['id']}"
            assert detail["project_number"], f"project_number is empty for item {detail['id']}"
            assert detail["tire_size"], f"tire_size is empty for item {detail['id']}"
            print(f"✅ All required fields populated for item {detail['id']}")
        
        return legacy_tire_details
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    test_tire_endpoint_logic()
