from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from .. import crud, models, schemas, auth
from ..database import get_db

router = APIRouter(
    prefix="/tires",
    tags=["tires"],
)

@router.get("/", response_model=List[schemas.TireRead], dependencies=[Depends(auth.require_viewer_role)])
def read_tires(
    skip: int = 0,
    limit: int = 100,
    owner: Optional[str] = Query(None, description="Filter by tire owner"),
    pattern: Optional[str] = Query(None, description="Filter by tire pattern"),
    size: Optional[str] = Query(None, description="Filter by tire size"),
    project_no: Optional[str] = Query(None, description="Filter by project number"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Retrieve all tire records with optional filtering.

    Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (pagination)
    - owner: Filter by tire owner
    - pattern: Filter by tire pattern
    - size: Filter by tire size
    - project_no: Filter by project number

    Returns:
    - List of tire objects matching the criteria
    """
    tires = crud.get_tires(
        db,
        skip=skip,
        limit=limit,
        owner=owner,
        pattern=pattern,
        size=size,
        project_no=project_no
    )
    return tires

@router.get("/{tire_id}", response_model=schemas.TireRead, dependencies=[Depends(auth.require_viewer_role)])
def read_tire(tire_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Retrieve a specific tire by ID.

    Parameters:
    - tire_id: ID of the tire to retrieve

    Returns:
    - The tire object if found

    Raises:
    - 404 Not Found: If the tire with the specified ID does not exist
    """
    db_tire = crud.get_tire(db, tire_id=tire_id)
    if db_tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return db_tire

@router.post("/", response_model=schemas.TireRead, dependencies=[Depends(auth.require_editor_role)])
def create_tire(tire: schemas.TireCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Create a new tire record.

    Parameters:
    - tire: Tire data to create

    Returns:
    - The created tire object
    """
    db_tire = crud.create_tire(db, tire)
    return db_tire

@router.put("/{tire_id}", response_model=schemas.TireRead, dependencies=[Depends(auth.require_editor_role)])
def update_tire(tire_id: str, tire: schemas.TireUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Update an existing tire record.

    Parameters:
    - tire_id: ID of the tire to update
    - tire: Updated tire data

    Returns:
    - The updated tire object

    Raises:
    - 404 Not Found: If the tire with the specified ID does not exist
    """
    db_tire = crud.update_tire(db, tire_id, tire)
    if db_tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return db_tire

@router.delete("/{tire_id}", response_model=schemas.TireRead, dependencies=[Depends(auth.require_admin_role)])
def delete_tire(tire_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Delete a tire record.

    Parameters:
    - tire_id: ID of the tire to delete

    Returns:
    - The deleted tire object

    Raises:
    - 404 Not Found: If the tire with the specified ID does not exist
    """
    db_tire = crud.delete_tire(db, tire_id)
    if db_tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return db_tire
