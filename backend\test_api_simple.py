#!/usr/bin/env python3
"""
Simple test API to verify the tire endpoint logic
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import sqlite3
import uvicorn
from typing import List, Dict, Any

app = FastAPI(title="Test API for Tire Endpoint")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:9002", "http://127.0.0.1:9002"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def health_check():
    return {"status": "ok"}

@app.get("/api/v1/requests/{request_id}/tires")
def get_request_tires(request_id: str) -> List[Dict[str, Any]]:
    """
    Get tires for a request by joining request_items with tires table.
    Returns data in the legacy RequestDetailRead format for backward compatibility.
    """

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Get request items with tire details
        cursor.execute('''
            SELECT
                ri.id as item_id,
                ri.request_id,
                ri.quantity,
                ri.disposition,
                ri.section,
                ri.notes,
                t.tug_no,
                t.spec_no,
                t.project_no,
                t.load_index,
                t.pattern,
                t.size as tire_size
            FROM request_items ri
            JOIN tires t ON ri.tire_id = t.id
            WHERE ri.request_id = ?
        ''', (request_id,))

        rows = cursor.fetchall()

        if not rows:
            # Check if request exists
            cursor.execute('SELECT id FROM requests WHERE id = ?', (request_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="Request not found")
            return []  # Request exists but has no tires

        # Convert to legacy RequestDetailRead format
        legacy_tire_details = []
        for row in rows:
            legacy_detail = {
                "id": row[0],
                "request_id": row[1],
                "tug_number": row[6],
                "section": row[4] or "SECTION_A",
                "project_number": row[8],
                "spec_number": row[7],
                "tire_size": row[11],
                "pattern": row[10],
                "note": row[5] or "",
                "disposition": row[3],
                "process_number": row[2]
            }
            legacy_tire_details.append(legacy_detail)

        return legacy_tire_details

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@app.get("/api/v1/requests/{request_id}")
def get_request(request_id: str) -> Dict[str, Any]:
    """Get a request by ID"""

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT * FROM requests WHERE id = ?', (request_id,))
        row = cursor.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail="Request not found")

        # Get column names
        cursor.execute('PRAGMA table_info(requests)')
        columns = [col[1] for col in cursor.fetchall()]

        # Convert to dict
        request_data = dict(zip(columns, row))

        return request_data

    except Exception as e:
        print(f"Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8001)
