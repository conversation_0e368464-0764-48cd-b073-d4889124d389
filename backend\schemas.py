from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime

# --- Processing Schemas ---

class ProcessingBase(BaseModel):
    id: str
    tire_type: str
    description1: str
    description2: str
    test_code1: str
    test_code2: str
    cost: str
    picture: Optional[bool] = False

class ProcessingCreate(ProcessingBase):
    pass

class ProcessingRead(ProcessingBase):
    class Config:
        orm_mode = True

# ============================================================================
# NEW SIMPLIFIED SCHEMAS - MIGRATION TARGET
# ============================================================================

# --- Enhanced Tire Schemas (Master Catalog) ---

class TireEnhancedBase(BaseModel):
    id: str
    tug_no: str
    spec_no: str
    size: str
    owner: str
    load_index: str
    pattern: str
    project_no: str
    location: str
    description: Optional[str] = None
    is_active: Optional[bool] = True

class TireEnhancedCreate(TireEnhancedBase):
    pass

class TireEnhancedUpdate(BaseModel):
    tug_no: Optional[str] = None
    spec_no: Optional[str] = None
    size: Optional[str] = None
    owner: Optional[str] = None
    load_index: Optional[str] = None
    pattern: Optional[str] = None
    project_no: Optional[str] = None
    location: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class TireEnhancedRead(TireEnhancedBase):
    class Config:
        orm_mode = True

# --- RequestItem Schemas (Simplified replacement for RequestDetail) ---

class RequestItemBase(BaseModel):
    id: str
    request_id: str
    tire_id: str
    quantity: int = 1
    disposition: str
    notes: Optional[str] = None
    unit_price: Optional[float] = None
    section: Optional[str] = None

class RequestItemCreate(RequestItemBase):
    pass

class RequestItemUpdate(BaseModel):
    tire_id: Optional[str] = None
    quantity: Optional[int] = None
    disposition: Optional[str] = None
    notes: Optional[str] = None
    unit_price: Optional[float] = None
    section: Optional[str] = None

class RequestItemRead(RequestItemBase):
    tire: Optional[TireEnhancedRead] = None  # Include tire details

    class Config:
        orm_mode = True

# --- CutOperation Schemas (Simplified replacement for RequestDetailCut + CutProcessing) ---

class CutOperationBase(BaseModel):
    id: Optional[int] = None
    request_item_id: str
    processing_id: str
    quantity: int = 1
    cut_price: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None
    created_date: Optional[datetime] = None

class CutOperationCreate(BaseModel):
    request_item_id: str
    processing_id: str
    quantity: int = 1
    cut_price: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class CutOperationUpdate(BaseModel):
    quantity: Optional[int] = None
    cut_price: Optional[float] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class CutOperationRead(CutOperationBase):
    id: int
    processing: Optional[ProcessingRead] = None

    class Config:
        orm_mode = True

# ============================================================================
# LEGACY SCHEMAS - FOR BACKWARD COMPATIBILITY
# ============================================================================

# --- RequestDetail Schemas ---

class RequestDetailBase(BaseModel):
    id: str
    request_id: str
    tug_number: str
    section: str
    project_number: str
    spec_number: str
    tire_size: str
    pattern: str
    note: Optional[str] = None
    disposition: str
    process_number: int

class RequestDetailCreate(RequestDetailBase):
    pass

class RequestDetailRead(RequestDetailBase):
    class Config:
        orm_mode = True

class RequestDetailUpdate(BaseModel):
    id: str
    tug_no: Optional[str] = None
    project_no: Optional[str] = None
    tire_size: Optional[str] = None
    note: Optional[str] = None
    disposition: Optional[str] = None
    quantity: Optional[int] = None

class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    role: Optional[str] = "viewer" # Added role field
    is_active: Optional[bool] = True

class UserCreate(UserBase):
    password: str

class UserRead(UserBase):
    id: int

    class Config:
        orm_mode = True

# --- Attachment Schemas ---

class AttachmentBase(BaseModel):
    id: str
    name: str
    size: int
    type: str
    upload_date: datetime
    status: str
    request_id: Optional[str] = None  # Make request_id optional in the base schema

class AttachmentCreate(AttachmentBase):
    pass

class AttachmentRead(AttachmentBase):
    request_id: str  # Make request_id required in the read schema

    class Config:
        orm_mode = True

# --- Request Schemas ---

class RequestBase(BaseModel):
    id: str
    request_by: str
    project_no: str
    pid_project: Optional[str] = None
    tire_size: str
    request_date: datetime
    target_date: datetime
    status: str
    type: str
    total_n: Optional[int] = None
    destination: Optional[str] = None
    internal: Optional[bool] = None
    wish_date: Optional[datetime] = None
    num_pneus_set: Optional[int] = None
    num_pneus_set_unit: Optional[str] = None
    in_charge_of: Optional[str] = None
    note: Optional[str] = None

    class Config:
        orm_mode = True
        allow_population_by_field_name = True
        fields = {
            "request_by": "requestBy",
            "project_no": "projectNo",
            "pid_project": "pidProject",
            "tire_size": "tireSize",
            "request_date": "requestDate",
            "target_date": "targetDate",
            "total_n": "totalN",
            "destination": "destination",
            "internal": "internal",
            "wish_date": "wishDate",
            "num_pneus_set": "numPneusSet",
            "num_pneus_set_unit": "numPneusSetUnit",
            "in_charge_of": "inChargeOf",
            "note": "note",
            "status": "status",
            "type": "type",
            "id": "id",
            "attachments": "attachments"
        }

class RequestCreate(RequestBase):
    attachments: Optional[List[AttachmentCreate]] = []

class RequestUpdate(RequestBase):
    attachments: Optional[List[AttachmentCreate]] = []
    request_details: Optional[List[RequestDetailBase]] = []  # Legacy support
    request_items: Optional[List[RequestItemBase]] = []      # New simplified structure

# --- Simplified schema for request lists (without nested relations) ---
class RequestSummary(RequestBase):
    class Config:
        orm_mode = True

# --- Enhanced Request Read Schema with both legacy and new structure ---
class RequestRead(RequestBase):
    attachments: List[AttachmentRead] = []
    request_details: List[RequestDetailRead] = []  # Legacy support
    request_items: List[RequestItemRead] = []       # New simplified structure

    class Config:
        orm_mode = True

# --- New Simplified Request Read Schema (migration target) ---
class RequestSimplifiedRead(RequestBase):
    attachments: List[AttachmentRead] = []
    request_items: List[RequestItemRead] = []

    class Config:
        orm_mode = True

# --- Tire Schemas ---

class TireBase(BaseModel):
    id: str
    tug_no: str
    spec_no: str
    size: str
    owner: str
    load_index: str
    pattern: str
    project_no: str
    location: str

class TireCreate(TireBase):
    pass

class TireUpdate(BaseModel):
    tug_no: Optional[str] = None
    spec_no: Optional[str] = None
    size: Optional[str] = None
    owner: Optional[str] = None
    load_index: Optional[str] = None
    pattern: Optional[str] = None
    project_no: Optional[str] = None
    location: Optional[str] = None

class TireRead(TireBase):
    class Config:
        orm_mode = True

# --- CutProcessing Schemas ---

class CutProcessingBase(BaseModel):
    id: Optional[int] = None
    cut_id: Optional[int] = None
    processing_id: Optional[str] = None
    quantity: Optional[int] = 1
    notes: Optional[str] = None
    created_date: Optional[datetime] = None

class CutProcessingCreate(BaseModel):
    cut_id: int
    processing_id: str
    quantity: Optional[int] = 1
    notes: Optional[str] = None

class CutProcessingUpdate(BaseModel):
    quantity: Optional[int] = None
    notes: Optional[str] = None

class CutProcessingRead(CutProcessingBase):
    id: int
    processing: Optional["ProcessingRead"] = None

    class Config:
        orm_mode = True

class CutProcessingWithProcessingRead(BaseModel):
    id: int
    cut_id: int
    processing_id: str
    quantity: int
    notes: Optional[str] = None
    created_date: Optional[datetime] = None
    processing: Optional["ProcessingRead"] = None

    class Config:
        orm_mode = True

# --- RequestDetailCut Schemas ---

class RequestDetailCutBase(BaseModel):
    id: Optional[int] = None
    id_request_details: Optional[str] = None
    notes: Optional[str] = None
    cut_price: Optional[float] = None
    status: Optional[str] = None

class RequestDetailCutCreate(RequestDetailCutBase):
    pass

class RequestDetailCutUpdate(BaseModel):
    notes: Optional[str] = None
    cut_price: Optional[float] = None
    status: Optional[str] = None

class RequestDetailCutRead(RequestDetailCutBase):
    id: int
    cut_processing: List["CutProcessingRead"] = []

    class Config:
        orm_mode = True

# --- Processing Search Schemas ---

class ProcessingSearchFilters(BaseModel):
    tire_type: Optional[str] = None
    description1: Optional[str] = None
    description2: Optional[str] = None
    test_code1: Optional[str] = None
    test_code2: Optional[str] = None
    min_cost: Optional[float] = None
    max_cost: Optional[float] = None
    picture: Optional[bool] = None

# --- Token Schemas ---

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# Forward references will be resolved automatically in Pydantic v2
