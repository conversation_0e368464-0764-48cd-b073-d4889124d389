import { useState, useEffect } from "react";
import { getRequest, createRequest, updateRequest, deleteRequest } from "@/services/requestService";
import { RequestFormData } from "@/types";

export function useRequestDetails(id?: string) {
  console.log("[useRequestDetails] Hook initialized. ID:", id);
  const [data, setData] = useState<RequestFormData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log("[useRequestDetails] useEffect triggered. ID:", id);
    if (id) {
      setLoading(true);
      setError(null); // Clear previous errors
      console.log(`[useRequestDetails] Fetching request with ID: ${id}`);
      getRequest(id)
        .then(responseData => {
          console.log("[useRequestDetails] Request data fetched:", responseData);

          // Validate the response data structure
          if (!responseData) {
            console.warn("[useRequestDetails] Response data is null or undefined");
            setData(null);
            return;
          }

          // Check for required fields
          const requiredFields = ['requestBy', 'tireSize', 'requestDate', 'targetDate', 'type'];
          const missingFields = requiredFields.filter(field =>
            !responseData[field as keyof RequestFormData] &&
            responseData[field as keyof RequestFormData] !== 0 &&
            responseData[field as keyof RequestFormData] !== false
          );

          if (missingFields.length > 0) {
            console.warn("[useRequestDetails] Response missing required fields:", missingFields);
            console.warn("[useRequestDetails] Field values:", missingFields.map(field => ({
              field,
              value: responseData[field as keyof RequestFormData],
              type: typeof responseData[field as keyof RequestFormData]
            })));
          }

          setData(responseData);
        })
        .catch(err => {
          console.error("[useRequestDetails] Error fetching request:", err);
          console.error("[useRequestDetails] Error details:", {
            message: err.message,
            status: err.response?.status,
            statusText: err.response?.statusText,
            data: err.response?.data
          });
          setError(err.message);
        })
        .finally(() => setLoading(false));
    } else {
      console.log("[useRequestDetails] No ID provided, clearing data.");
      setData(null); // Clear data if ID is not present or removed
      setError(null); // Clear errors when no ID
    }
  }, [id]);

  const save = (form: RequestFormData) => {
    console.log("[useRequestDetails] save called. ID:", id, "Form data:", form);
    setLoading(true);
    const req = id ? updateRequest(id, form) : createRequest(form);
    return req
      .then(responseData => {
        console.log("[useRequestDetails] Save successful. Response data:", responseData);
        setData(responseData);
        return responseData; // Ensure the promise chain resolves with the response data
      })
      .catch(err => {
        console.error("[useRequestDetails] Error saving request:", err.message);
        setError(err.message);
        throw err; // Re-throw to allow calling components to handle
      })
      .finally(() => setLoading(false));
  };

  const remove = () => {
    if (!id) {
      console.warn("[useRequestDetails] remove called without ID.");
      return Promise.reject("No id");
    }
    console.log(`[useRequestDetails] remove called. ID: ${id}`);
    setLoading(true);
    return deleteRequest(id)
      .then(() => {
        console.log("[useRequestDetails] Remove successful. Setting data to null.");
        setData(null);
      })
      .catch(err => {
        console.error("[useRequestDetails] Error removing request:", err.message);
        setError(err.message);
        throw err; // Re-throw to allow calling components to handle
      })
      .finally(() => setLoading(false));
  };

  // Log data changes
  useEffect(() => {
    console.log("[useRequestDetails] Data state changed:", data);
  }, [data]);

  return { data, loading, error, save, remove };
}
