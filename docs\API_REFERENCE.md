# CutRequestStudio API Reference

This document provides comprehensive documentation for the CutRequestStudio REST API.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

All API endpoints (except login) require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

```http
POST /users/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=yourpassword
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

## User Management

### Create User
```http
POST /users/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "full_name": "John Doe",
  "role": "viewer"
}
```

### Get Current User
```http
GET /users/me
Authorization: Bearer <token>
```

### List Users (Admin Only)
```http
GET /users/?skip=0&limit=10
Authorization: Bearer <admin-token>
```

## Request Management

### List Requests
```http
GET /requests/?skip=0&limit=100&status=pending
Authorization: Bearer <token>
```

**Query Parameters:**
- `skip` (int): Number of records to skip (pagination)
- `limit` (int): Maximum number of records to return
- `status` (string): Filter by request status

**Response:**
```json
[
  {
    "id": "REQ001",
    "request_by": "John Doe",
    "project_no": "PRJ001",
    "tire_size": "225/60R16",
    "status": "pending",
    "request_date": "2024-01-15T10:00:00Z",
    "target_date": "2024-01-30T10:00:00Z"
  }
]
```

### Get Specific Request
```http
GET /requests/{request_id}
Authorization: Bearer <token>
```

### Create Request
```http
POST /requests/
Content-Type: application/json
Authorization: Bearer <token>

{
  "id": "REQ002",
  "request_by": "Jane Smith",
  "project_no": "PRJ002",
  "pid_project": "PID002",
  "tire_size": "205/55R16",
  "request_date": "2024-01-15T10:00:00Z",
  "target_date": "2024-02-15T10:00:00Z",
  "status": "pending",
  "type": "development",
  "total_n": 4,
  "destination": "Test Lab",
  "internal": true,
  "note": "Urgent request for testing"
}
```

### Update Request
```http
PUT /requests/{request_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "in_progress",
  "note": "Updated status to in progress"
}
```

### Delete Request
```http
DELETE /requests/{request_id}
Authorization: Bearer <token>
```

### Get Request Tires
```http
GET /requests/{request_id}/tires
Authorization: Bearer <token>
```

## Request Details (Tires)

### Get Tire Detail
```http
GET /requests/details/{detail_id}
Authorization: Bearer <token>
```

### Update Tire Detail
```http
PUT /requests/details/{detail_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "tug_no": "TUG001",
  "project_no": "PRJ001",
  "tire_size": "225/60R16",
  "note": "Updated tire specifications",
  "disposition": "approved",
  "quantity": 2
}
```

### Delete Tire Detail
```http
DELETE /requests/details/{detail_id}
Authorization: Bearer <token>
```

## Tire Management

### List Tires
```http
GET /tires/?skip=0&limit=100&owner=bridgestone&pattern=eco&size=225/60R16
Authorization: Bearer <token>
```

**Query Parameters:**
- `owner` (string): Filter by tire owner
- `pattern` (string): Filter by tire pattern
- `size` (string): Filter by tire size
- `project_no` (string): Filter by project number

### Get Specific Tire
```http
GET /tires/{tire_id}
Authorization: Bearer <token>
```

### Create Tire
```http
POST /tires/
Content-Type: application/json
Authorization: Bearer <token>

{
  "id": "TIRE001",
  "tug_no": "TUG001",
  "spec_no": "SPEC001",
  "size": "225/60R16",
  "owner": "Bridgestone",
  "load_index": "95",
  "pattern": "Ecopia",
  "project_no": "PRJ001",
  "location": "Warehouse A"
}
```

### Update Tire
```http
PUT /tires/{tire_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "location": "Warehouse B",
  "owner": "Bridgestone Europe"
}
```

### Delete Tire
```http
DELETE /tires/{tire_id}
Authorization: Bearer <token>
```

## Cut Processing

### List Cut Processing
```http
GET /cut-processing/?skip=0&limit=100
Authorization: Bearer <token>
```

### Get Processing by Cut
```http
GET /cut-processing/by-cut/{cut_id}
Authorization: Bearer <token>
```

### Create Cut Processing
```http
POST /cut-processing/
Content-Type: application/json
Authorization: Bearer <token>

{
  "cut_id": 1,
  "processing_id": "PROC001",
  "quantity": 2,
  "notes": "Standard processing"
}
```

### Update Cut Processing
```http
PUT /cut-processing/{cut_processing_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "quantity": 3,
  "notes": "Updated processing quantity"
}
```

**Note**: Only `quantity` and `notes` fields can be updated. Processing details (descriptions, codes, costs) are read-only as they come from the master Processing catalog.

### Get Tire Processing Data
```http
GET /cut-processing/by-tire/{request_detail_id}
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "cut_id": 1,
    "processing_id": "PROC001",
    "quantity": 2,
    "notes": "Standard processing",
    "created_date": "2024-01-15T10:00:00Z",
    "processing": {
      "id": "PROC001",
      "tire_type": "passenger",
      "description1": "Standard Test",
      "description2": "Quality Check",
      "test_code1": "TC001",
      "test_code2": "TC002",
      "cost": "150.00 €",
      "picture": true
    }
  }
]
```

This endpoint aggregates all processing data associated with cuts for a specific tire (request detail).

### Search Processing
```http
GET /cut-processing/processing/search?tire_type=passenger&description1=test&min_cost=100&max_cost=500
Authorization: Bearer <token>
```

**Query Parameters:**
- `tire_type` (string): Filter by tire type
- `description1` (string): Filter by first description
- `description2` (string): Filter by second description
- `test_code1` (string): Filter by first test code
- `test_code2` (string): Filter by second test code
- `min_cost` (float): Minimum cost filter
- `max_cost` (float): Maximum cost filter
- `picture` (boolean): Filter by picture availability

**Response:**
```json
[
  {
    "id": "PROC001",
    "tire_type": "passenger",
    "description1": "Standard Test",
    "description2": "Quality Check",
    "test_code1": "TC001",
    "test_code2": "TC002",
    "cost": "150.00 €",
    "picture": true
  }
]
```

## Request Detail Cuts

### List Cuts
```http
GET /request-detail-cuts/?skip=0&limit=100
Authorization: Bearer <token>
```

### Get Specific Cut
```http
GET /request-detail-cuts/{cut_id}
Authorization: Bearer <token>
```

### Create Cut
```http
POST /request-detail-cuts/
Content-Type: application/json
Authorization: Bearer <token>

{
  "request_detail_id": "DET001",
  "set": "1/4",
  "direction": 0,
  "note": "Standard cut specification"
}
```

### Get Cuts by Request Detail
```http
GET /request-detail-cuts/by-request-detail/{request_detail_id}
Authorization: Bearer <token>
```

## Error Responses

### Standard Error Format
```json
{
  "detail": "Error message description"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Validation Error Example
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## Rate Limiting

Currently, no rate limiting is implemented. For production use, consider implementing rate limiting based on your requirements.

## Pagination

Most list endpoints support pagination using `skip` and `limit` parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100, max: 1000)

## Data Types

### Date/Time Format
All date/time fields use ISO 8601 format:
```
2024-01-15T10:00:00Z
```

### ID Formats
- Request IDs: String format (e.g., "REQ001", "RFQ3")
- Tire Detail IDs: String format with prefixes (e.g., "DET-", "MGT-")
- Processing IDs: String format
- Cut IDs: Integer format

## WebSocket Support

Currently, the API does not support WebSocket connections. All communication is through standard HTTP requests.
