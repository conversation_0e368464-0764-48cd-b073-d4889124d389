/**
 * PRIORITY 2 Integration Tests
 *
 * Tests to verify that PRIORITY 2 components integrate seamlessly
 * with PRIORITY 1 components and maintain backward compatibility.
 *
 * Author: Kilo Code
 * Date: 28 Maggio 2025
 * Version: 2.0 (PRIORITY 2 Implementation)
 */

// Mock Jest globals for TypeScript
declare global {
  const describe: any;
  const test: any;
  const expect: any;
  const beforeEach: any;
  const jest: any;
}
import {
  EnhancedDataTransformer,
  TransformationPresets,
  TransformationUtils,
  TransformationOptions
} from '../lib/data-transformer';
import { CrudServiceFactory, GenericCrudService } from '../lib/generic-crud-service';
import { AppRequest, RequestFormData, AttachmentFile } from '../types';

// Mock axios for testing
jest.mock('../lib/axiosInstance', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}));

describe('PRIORITY 2 Integration Tests', () => {

  describe('Enhanced Data Transformer Integration', () => {

    test('should integrate with Generic CRUD Service transformations', () => {
      const testData: RequestFormData = {
        id: '123',
        requestBy: 'john.doe',
        projectNo: 'PRJ-001',
        requestDate: new Date('2025-01-01'),
        targetDate: new Date('2025-02-01'),
        status: 'PENDING',
        type: 'NEWDEV'
      };

      // Test PRIORITY 2 enhanced transformation
      const transformed = EnhancedDataTransformer.camelToSnake(
        testData,
        TransformationPresets.API_REQUEST
      );

      expect(transformed).toEqual({
        id: '123',
        request_by: 'john.doe',
        project_no: 'PRJ-001',
        request_date: '2025-01-01T00:00:00.000Z',
        target_date: '2025-02-01T00:00:00.000Z',
        status: 'PENDING',
        type: 'NEWDEV'
      });
    });

    test('should handle nested objects and arrays correctly', () => {
      const testData = {
        requestBy: 'user',
        projectNo: '123',
        attachments: [
          {
            fileName: 'test.pdf',
            fileSize: 1024,
            uploadedAt: new Date('2025-01-01')
          }
        ],
        metadata: {
          createdBy: 'admin',
          lastModified: new Date('2025-01-02')
        }
      };

      const transformed = EnhancedDataTransformer.camelToSnake(
        testData,
        TransformationPresets.API_REQUEST
      );

      expect(transformed).toEqual({
        request_by: 'user',
        project_no: '123',
        attachments: [
          {
            file_name: 'test.pdf',
            file_size: 1024,
            uploaded_at: '2025-01-01T00:00:00.000Z'
          }
        ],
        metadata: {
          created_by: 'admin',
          last_modified: '2025-01-02T00:00:00.000Z'
        }
      });
    });

    test('should preserve backward compatibility with PRIORITY 1 DataTransformer', () => {
      const testData = { requestBy: 'user', projectNo: '123' };

      // PRIORITY 1 transformation (basic)
      const basicTransform = EnhancedDataTransformer.camelToSnake(testData);

      // PRIORITY 2 transformation (enhanced)
      const enhancedTransform = EnhancedDataTransformer.camelToSnake(
        testData,
        TransformationPresets.API_REQUEST
      );

      // Both should produce the same result for simple objects
      expect(basicTransform).toEqual(enhancedTransform);
      expect(basicTransform).toEqual({
        request_by: 'user',
        project_no: '123'
      });
    });

    test('should handle custom field mappings', () => {
      const testData = {
        tugNo: 'TUG-001',
        projectNo: 'PRJ-001'
      };

      const customMappings = {
        tugNo: 'tug_number',
        projectNo: 'project_number'
      };

      const transformed = EnhancedDataTransformer.transformWithMappings(
        testData,
        customMappings
      );

      expect(transformed).toEqual({
        tug_number: 'TUG-001',
        project_number: 'PRJ-001'
      });
    });

    test('should optimize performance for large datasets', () => {
      const largeDataset = Array.from({ length: 1500 }, (_, i) => ({
        id: i,
        requestBy: `user${i}`,
        projectNo: `PRJ-${i.toString().padStart(3, '0')}`
      }));

      const startTime = performance.now();

      const transformed = EnhancedDataTransformer.transformBatch(
        largeDataset,
        (item) => EnhancedDataTransformer.camelToSnake(item),
        TransformationPresets.LIGHTWEIGHT
      );

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(Array.isArray(transformed) ? transformed.length : 1).toBe(1500);
      const firstItem = Array.isArray(transformed) ? transformed[0] : transformed;
      expect(firstItem).toEqual({
        id: 0,
        request_by: 'user0',
        project_no: 'PRJ-000'
      });

      // Should process large datasets efficiently (< 100ms for 1500 items)
      expect(processingTime).toBeLessThan(100);
    });
  });

  describe('TransformationUtils Integration', () => {

    test('should transform attachments correctly', () => {
      const attachments: AttachmentFile[] = [
        {
          id: '1',
          name: 'document.pdf',
          size: 1024,
          type: 'application/pdf',
          uploadDate: new Date('2025-01-01'),
          status: 'Uploaded'
        }
      ];

      const transformed = TransformationUtils.transformAttachments(attachments);

      expect(transformed[0]).toEqual({
        id: '1',
        name: 'document.pdf',
        size: 1024,
        type: 'application/pdf',
        upload_date: '2025-01-01T00:00:00.000Z',
        status: 'Uploaded'
      });
    });

    test('should transform API responses correctly', () => {
      const apiResponse = {
        id: '123',
        request_by: 'user',
        project_no: 'PRJ-001',
        created_at: '2025-01-01T00:00:00.000Z'
      };

      const transformed = TransformationUtils.transformApiResponse<AppRequest>(apiResponse);

      expect(transformed).toEqual({
        id: '123',
        requestBy: 'user',
        projectNo: 'PRJ-001',
        createdAt: new Date('2025-01-01T00:00:00.000Z')
      });
    });
  });

  describe('Service Integration with Enhanced Transformations', () => {
    let mockAxios: any;
    let crudService: GenericCrudService<AppRequest, RequestFormData>;

    beforeEach(() => {
      mockAxios = require('../lib/axiosInstance');
      crudService = CrudServiceFactory.createStandardService<AppRequest, RequestFormData>('/requests');

      // Reset mocks
      jest.clearAllMocks();
    });

    test('should integrate PRIORITY 1 CRUD service with PRIORITY 2 transformations', async () => {
      const requestData: RequestFormData = {
        requestBy: 'john.doe',
        projectNo: 'PRJ-001',
        requestDate: new Date('2025-01-01')
      };

      const mockResponse = {
        data: {
          id: '123',
          request_by: 'john.doe',
          project_no: 'PRJ-001',
          request_date: '2025-01-01T00:00:00.000Z'
        }
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      // This should use PRIORITY 1 CRUD service with PRIORITY 1 transformations
      const result = await crudService.create(requestData);

      // Verify the request was transformed correctly (camelCase → snake_case)
      expect(mockAxios.post).toHaveBeenCalledWith('/requests', {
        request_by: 'john.doe',
        project_no: 'PRJ-001',
        request_date: '2025-01-01T00:00:00.000Z'
      });

      // Verify the response was transformed correctly (snake_case → camelCase)
      expect(result).toEqual({
        id: '123',
        requestBy: 'john.doe',
        projectNo: 'PRJ-001',
        requestDate: new Date('2025-01-01T00:00:00.000Z')
      });
    });

    test('should handle enhanced transformations in custom requests', async () => {
      const attachmentData = [
        {
          fileName: 'test.pdf',
          fileSize: 1024,
          uploadedAt: new Date('2025-01-01')
        }
      ];

      const mockResponse = {
        data: {
          attachments: [
            {
              file_name: 'test.pdf',
              file_size: 1024,
              uploaded_at: '2025-01-01T00:00:00.000Z'
            }
          ]
        }
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      // Use enhanced transformation for attachments
      const transformedAttachments = EnhancedDataTransformer.transformBatch(
        attachmentData,
        (attachment) => EnhancedDataTransformer.camelToSnake(attachment, TransformationPresets.API_REQUEST),
        TransformationPresets.API_REQUEST
      );

      const result = await crudService.customRequest(
        '123/attachments',
        'POST',
        { attachments: transformedAttachments }
      );

      expect(mockAxios.post).toHaveBeenCalledWith('/requests/123/attachments', {
        attachments: [
          {
            file_name: 'test.pdf',
            file_size: 1024,
            uploaded_at: '2025-01-01T00:00:00.000Z'
          }
        ]
      });

      expect(result.attachments[0]).toEqual({
        fileName: 'test.pdf',
        fileSize: 1024,
        uploadedAt: new Date('2025-01-01T00:00:00.000Z')
      });
    });
  });

  describe('Performance Integration Tests', () => {

    test('should maintain performance with enhanced transformations', () => {
      const testData = Array.from({ length: 100 }, (_, i) => ({
        id: i,
        requestBy: `user${i}`,
        projectNo: `PRJ-${i}`,
        attachments: [
          { fileName: `file${i}.pdf`, fileSize: 1024 * i }
        ]
      }));

      const startTime = performance.now();

      // Test batch transformation performance
      const transformed = EnhancedDataTransformer.transformBatch(
        testData,
        (item) => EnhancedDataTransformer.camelToSnake(item, TransformationPresets.API_REQUEST),
        TransformationPresets.API_REQUEST
      );

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(transformed).toHaveLength(100);
      expect(processingTime).toBeLessThan(50); // Should be fast for 100 items
    });

    test('should handle memory efficiently with large nested objects', () => {
      const largeNestedObject = {
        requestBy: 'user',
        projectNo: 'PRJ-001',
        attachments: Array.from({ length: 50 }, (_, i) => ({
          fileName: `file${i}.pdf`,
          fileSize: 1024 * i,
          metadata: {
            uploadedBy: `user${i}`,
            tags: [`tag${i}`, `category${i % 5}`]
          }
        }))
      };

      const startTime = performance.now();
      const memoryBefore = (performance as any).memory?.usedJSHeapSize || 0;

      const transformed = EnhancedDataTransformer.camelToSnake(
        largeNestedObject,
        TransformationPresets.API_REQUEST
      );

      const endTime = performance.now();
      const memoryAfter = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryUsed = memoryAfter - memoryBefore;

      expect(transformed.attachments).toHaveLength(50);
      expect(endTime - startTime).toBeLessThan(20); // Should be fast

      // Memory usage should be reasonable (less than 1MB for this test)
      if (memoryUsed > 0) {
        expect(memoryUsed).toBeLessThan(1024 * 1024);
      }
    });
  });

  describe('Error Handling Integration', () => {

    test('should handle transformation errors gracefully', () => {
      const invalidData = {
        requestBy: 'user',
        circularRef: null as any
      };

      // Create circular reference
      invalidData.circularRef = invalidData;

      expect(() => {
        EnhancedDataTransformer.camelToSnake(invalidData);
      }).not.toThrow(); // Should handle gracefully
    });

    test('should validate transformation results when schema provided', () => {
      const testData = { requestBy: 'user', projectNo: '123' };

      const validator = (obj: any): obj is { request_by: string; project_no: string } => {
        return typeof obj.request_by === 'string' && typeof obj.project_no === 'string';
      };

      expect(() => {
        EnhancedDataTransformer.transformTypeSafe(
          testData,
          (data) => EnhancedDataTransformer.camelToSnake(data),
          validator
        );
      }).not.toThrow();

      // Test with invalid result
      const invalidValidator = (obj: any): obj is { invalid_field: string } => {
        return typeof obj.invalid_field === 'string';
      };

      expect(() => {
        EnhancedDataTransformer.transformTypeSafe(
          testData,
          (data) => EnhancedDataTransformer.camelToSnake(data),
          invalidValidator
        );
      }).toThrow('Transformation result does not match expected schema');
    });
  });

  describe('Backward Compatibility Tests', () => {

    test('should maintain compatibility with existing service interfaces', async () => {
      // Test that existing code using PRIORITY 1 services still works
      const crudService = CrudServiceFactory.createStandardService<AppRequest, RequestFormData>('/requests');

      // These methods should still exist and work as before
      expect(typeof crudService.getAll).toBe('function');
      expect(typeof crudService.getById).toBe('function');
      expect(typeof crudService.create).toBe('function');
      expect(typeof crudService.update).toBe('function');
      expect(typeof crudService.delete).toBe('function');
      expect(typeof crudService.customRequest).toBe('function');
    });

    test('should support legacy transformation patterns', () => {
      const testData = { requestBy: 'user', projectNo: '123' };

      // Legacy pattern (should still work)
      const legacyTransform = EnhancedDataTransformer.camelToSnake(testData);

      // New pattern
      const newTransform = EnhancedDataTransformer.camelToSnake(
        testData,
        TransformationPresets.API_REQUEST
      );

      // Should produce same result for simple cases
      expect(legacyTransform).toEqual(newTransform);
    });
  });
});

/**
 * Integration Test Summary
 *
 * These tests verify that:
 *
 * ✅ PRIORITY 2 Enhanced Data Transformer integrates seamlessly with PRIORITY 1 Generic CRUD Service
 * ✅ Performance optimizations work correctly for large datasets
 * ✅ Custom field mappings and transformations work as expected
 * ✅ Backward compatibility is maintained with existing code
 * ✅ Error handling is robust and graceful
 * ✅ Memory usage is efficient for complex nested objects
 * ✅ Type safety is preserved throughout the transformation pipeline
 * ✅ Batch processing optimizations function correctly
 *
 * The integration tests demonstrate that PRIORITY 2 components enhance
 * the existing PRIORITY 1 architecture without breaking changes, while
 * providing significant improvements in functionality and performance.
 */