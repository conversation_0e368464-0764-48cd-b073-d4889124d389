# CutRequestStudio Backend

FastAPI-based REST API for tire request management with SQLAlchemy ORM and Pydantic validation.

## 🚀 Features

- **RESTful API**: Complete CRUD operations for all entities
- **Authentication**: JWT-based authentication with role-based access control
- **Database ORM**: SQLAlchemy with support for SQLite and PostgreSQL
- **Data Validation**: Pydantic schemas for request/response validation
- **Auto Documentation**: Swagger/OpenAPI documentation generation
- **CORS Support**: Configurable CORS for frontend integration

## 🛠 Tech Stack

- **Framework**: FastAPI
- **ORM**: SQLAlchemy
- **Validation**: Pydantic
- **Authentication**: JWT with bcrypt password hashing
- **Database**: SQLite (development) / PostgreSQL (production)
- **Documentation**: Auto-generated Swagger/OpenAPI

## 📁 Project Structure

```
backend/
├── routers/                  # API route handlers
│   ├── user.py              # User management endpoints
│   ├── request.py           # Request management endpoints
│   ├── tire.py              # Tire management endpoints
│   ├── request_detail_cut.py # Cut management endpoints
│   └── cut_processing.py    # Processing management endpoints
├── models.py                # SQLAlchemy database models
├── schemas.py               # Pydantic validation schemas
├── crud.py                  # Database operations (CRUD)
├── auth.py                  # Authentication logic
├── database.py              # Database configuration
├── main.py                  # FastAPI application entry point
├── requirements.txt         # Python dependencies
├── create_db.py            # Database initialization script
└── README.md               # This file
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv

   # Activate virtual environment
   # On Windows:
   venv\Scripts\activate

   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Initialize database**
   ```bash
   python create_db.py
   ```

5. **Start the server**
   ```bash
   uvicorn main:app --reload --port 8000
   ```

### Access Points
- **API Server**: http://localhost:8000
- **Interactive Documentation**: http://localhost:8000/docs
- **Alternative Documentation**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# Database Configuration
DATABASE_URL=sqlite:///./app.db

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS (for production)
ALLOWED_ORIGINS=http://localhost:9002,https://yourdomain.com

# Optional
DEBUG=false
LOG_LEVEL=INFO
```

### Database Configuration

#### SQLite (Development)
```python
# Default configuration in database.py
DATABASE_URL = "sqlite:///./app.db"
```

#### PostgreSQL (Production)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/cutrequestdb
```

## 📊 API Endpoints

### Authentication
- `POST /api/v1/users/login` - User login
- `GET /api/v1/users/me` - Get current user
- `POST /api/v1/users/` - Create user (admin only)

### Request Management
- `GET /api/v1/requests/` - List requests
- `GET /api/v1/requests/{id}` - Get specific request
- `POST /api/v1/requests/` - Create request
- `PUT /api/v1/requests/{id}` - Update request
- `DELETE /api/v1/requests/{id}` - Delete request

### Tire Management
- `GET /api/v1/tires/` - List tires
- `GET /api/v1/tires/{id}` - Get specific tire
- `POST /api/v1/tires/` - Create tire
- `PUT /api/v1/tires/{id}` - Update tire
- `DELETE /api/v1/tires/{id}` - Delete tire

### Processing Management
- `GET /api/v1/cut-processing/` - List processing operations
- `GET /api/v1/cut-processing/processing/search` - Search processing
- `POST /api/v1/cut-processing/` - Create processing association

For complete API documentation, visit `/docs` when the server is running.

## 🗄️ Database Models

### Core Entities
- **User**: User accounts with role-based permissions
- **Request**: Main tire request entity
- **RequestDetail**: Individual tire specifications
- **Attachment**: File attachments for requests
- **Tire**: General tire catalog
- **Processing**: Available processing operations
- **RequestDetailCut**: Cut specifications
- **CutProcessing**: Processing associations

### Relationships
```
Request (1) → (N) RequestDetail
Request (1) → (N) Attachment
RequestDetail (1) → (N) RequestDetailCut
RequestDetailCut (N) ↔ (N) Processing (via CutProcessing)
```

## 🔐 Authentication & Authorization

### User Roles
- **admin**: Full access to all features and user management
- **editor**: Can create, edit, and delete requests and related data
- **viewer**: Read-only access to all data

### JWT Token Authentication
```python
# Example: Protected endpoint
@router.get("/", dependencies=[Depends(auth.require_viewer_role)])
def get_requests(current_user: User = Depends(auth.get_current_user)):
    # Endpoint logic
```

### Creating Users
```python
# Create admin user
from crud import create_user
from schemas import UserCreate
from database import SessionLocal

db = SessionLocal()
admin_user = UserCreate(
    email="<EMAIL>",
    password="secure_password",
    full_name="Admin User",
    role="admin"
)
create_user(db, admin_user)
```

## 🧪 Testing

### Manual Testing
```bash
# Health check
curl http://localhost:8000/health

# Login
curl -X POST http://localhost:8000/api/v1/users/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=secure_password"

# Get requests (with token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/requests/
```

### Automated Testing
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

## 🚀 Deployment

### Production Setup
1. **Use PostgreSQL database**
2. **Set strong SECRET_KEY**
3. **Configure proper CORS origins**
4. **Use environment variables for all configuration**
5. **Set up proper logging**

### Using Gunicorn
```bash
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000"]
```

## 🐛 Troubleshooting

### Common Issues

1. **Database locked error**
   ```bash
   rm app.db-wal app.db-shm
   ```

2. **Import errors**
   ```bash
   pip install -r requirements.txt
   ```

3. **CORS errors**
   - Check `origins` list in `main.py`
   - Verify frontend URL is included

4. **Authentication errors**
   - Check SECRET_KEY configuration
   - Verify user exists in database
   - Check token expiry settings

### Debug Mode
```bash
# Enable debug logging
export FASTAPI_DEBUG=true
uvicorn main:app --reload --log-level debug
```

## 📝 Development

### Adding New Endpoints
1. Define Pydantic schema in `schemas.py`
2. Add database model in `models.py` (if needed)
3. Implement CRUD operations in `crud.py`
4. Create router endpoint in appropriate router file
5. Include router in `main.py`

### Code Style
- Follow PEP 8 guidelines
- Use type hints for all functions
- Add docstrings for public functions
- Implement proper error handling

### Recent Improvements (v1.1.0)
- ✅ Fixed tire update 404 errors with proper CRUD implementation
- ✅ Enhanced field mapping between camelCase and snake_case
- ✅ Improved error handling and validation
- ✅ Added comprehensive API documentation
- ✅ Enhanced authentication and authorization

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/)
- [JWT Authentication Guide](https://fastapi.tiangolo.com/tutorial/security/oauth2-jwt/)

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Follow semantic versioning for releases

## 📄 License

This project is part of the CutRequestStudio application suite.
