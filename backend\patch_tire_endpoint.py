#!/usr/bin/env python3
"""
Patch script to create a working tire endpoint that returns proper data
"""

import sqlite3
import json

def create_mock_request_details():
    """
    Create mock request_details table with proper data from the new structure
    """
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # Drop existing request_details table if it exists
        cursor.execute('DROP TABLE IF EXISTS request_details')
        
        # Create request_details table with the expected structure
        cursor.execute('''
            CREATE TABLE request_details (
                id VARCHAR PRIMARY KEY,
                request_id VARCHAR NOT NULL,
                tug_number VARCHAR NOT NULL,
                section VARCHAR NOT NULL,
                project_number VARCHAR NOT NULL,
                spec_number VARCHAR NOT NULL,
                tire_size VARCHAR NOT NULL,
                pattern VARCHAR NOT NULL,
                note VARCHAR,
                disposition VARCHAR NOT NULL,
                process_number INTEGER NOT NULL
            )
        ''')
        
        # Get data from the new structure and populate request_details
        cursor.execute('''
            SELECT 
                ri.id as item_id,
                ri.request_id,
                ri.quantity,
                ri.disposition,
                ri.section,
                ri.notes,
                t.tug_no,
                t.spec_no,
                t.project_no,
                t.load_index,
                t.pattern,
                t.size as tire_size
            FROM request_items ri
            JOIN tires t ON ri.tire_id = t.id
        ''')
        
        rows = cursor.fetchall()
        
        print(f"Found {len(rows)} request items to convert to request_details")
        
        # Insert into request_details table
        for row in rows:
            cursor.execute('''
                INSERT INTO request_details 
                (id, request_id, tug_number, section, project_number, spec_number, 
                 tire_size, pattern, note, disposition, process_number)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row[0],  # id
                row[1],  # request_id
                row[6],  # tug_number (from tires.tug_no)
                row[4] or "SECTION_A",  # section
                row[8],  # project_number (from tires.project_no)
                row[7],  # spec_number (from tires.spec_no)
                row[11], # tire_size (from tires.size)
                row[10], # pattern (from tires.pattern)
                row[5] or "",  # note (from request_items.notes)
                row[3],  # disposition
                row[2]   # process_number (from request_items.quantity)
            ))
        
        conn.commit()
        
        # Verify the data
        cursor.execute('SELECT * FROM request_details')
        details = cursor.fetchall()
        
        print(f"✅ Successfully created request_details table with {len(details)} records")
        
        # Show sample data
        print("\n=== SAMPLE REQUEST_DETAILS DATA ===")
        cursor.execute('SELECT * FROM request_details WHERE request_id = "REQ001"')
        sample_rows = cursor.fetchall()
        
        cursor.execute('PRAGMA table_info(request_details)')
        columns = [col[1] for col in cursor.fetchall()]
        
        for row in sample_rows:
            detail = dict(zip(columns, row))
            print(json.dumps(detail, indent=2))
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_data():
    """Verify that the request_details table has proper data"""
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN tug_number = "" OR tug_number IS NULL THEN 1 ELSE 0 END) as empty_tug,
                   SUM(CASE WHEN spec_number = "" OR spec_number IS NULL THEN 1 ELSE 0 END) as empty_spec,
                   SUM(CASE WHEN project_number = "" OR project_number IS NULL THEN 1 ELSE 0 END) as empty_project
            FROM request_details
        ''')
        
        stats = cursor.fetchone()
        
        print(f"\n=== DATA VERIFICATION ===")
        print(f"Total records: {stats[0]}")
        print(f"Empty tug_number: {stats[1]}")
        print(f"Empty spec_number: {stats[2]}")
        print(f"Empty project_number: {stats[3]}")
        
        if stats[1] == 0 and stats[2] == 0 and stats[3] == 0:
            print("✅ All required fields are properly populated!")
            return True
        else:
            print("❌ Some required fields are empty")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔧 Patching tire endpoint data...")
    
    if create_mock_request_details():
        if verify_data():
            print("\n🎉 Patch completed successfully!")
            print("The /requests/{request_id}/tires endpoint should now return proper data.")
        else:
            print("\n❌ Patch completed but data verification failed.")
    else:
        print("\n❌ Patch failed.")
