#!/usr/bin/env node

/**
 * MCP Playwright Server Connection Test
 *
 * This script tests the connection to the MCP Playwright server
 * and verifies that it's working correctly.
 */

const { spawn } = require('child_process');

async function testMCPConnection() {
  console.log('🧪 Testing MCP Playwright Server Connection...\n');

  try {
    // Test 1: Check if the package is available
    console.log('1️⃣ Checking if @executeautomation/playwright-mcp-server is available...');

    const testProcess = spawn('npx', ['-y', '@executeautomation/playwright-mcp-server', '--version'], {
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let errorOutput = '';

    testProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    testProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Package is available and working');
        console.log('📦 Version info:', output.trim());
      } else {
        console.log('❌ Package test failed');
        console.log('Error:', errorOutput);
      }
    });

    // Test 2: Verify MCP protocol support
    setTimeout(() => {
      console.log('\n2️⃣ Testing MCP protocol support...');

      const mcpTest = spawn('npx', ['-y', '@executeautomation/playwright-mcp-server'], {
        stdio: 'pipe',
        shell: true
      });

      // Send MCP initialization message
      const initMessage = JSON.stringify({
        jsonrpc: "2.0",
        id: 1,
        method: "initialize",
        params: {
          protocolVersion: "2024-11-05",
          capabilities: {},
          clientInfo: {
            name: "test-client",
            version: "1.0.0"
          }
        }
      }) + '\n';

      mcpTest.stdin.write(initMessage);

      let mcpOutput = '';
      mcpTest.stdout.on('data', (data) => {
        mcpOutput += data.toString();
      });

      setTimeout(() => {
        mcpTest.kill();
        if (mcpOutput.includes('result')) {
          console.log('✅ MCP protocol is working');
          console.log('🔧 Available capabilities detected');
        } else {
          console.log('⚠️  MCP protocol test inconclusive');
          console.log('Output:', mcpOutput);
        }

        console.log('\n🎉 MCP Playwright Server test completed!');
        console.log('\n📋 Next steps:');
        console.log('   1. Restart VS Code to load MCP configuration');
        console.log('   2. Check VS Code MCP extension is enabled');
        console.log('   3. Use MCP tools through VS Code interface');
        console.log('   4. Run demo tests with the CutRequestStudio application');
      }, 3000);

    }, 2000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Configuration summary
function showConfiguration() {
  console.log('📋 MCP Playwright Server Configuration Summary:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('🏷️  Server Name: github.com/executeautomation/mcp-playwright');
  console.log('📦 Package: @executeautomation/playwright-mcp-server');
  console.log('🔧 Command: npx -y @executeautomation/playwright-mcp-server');
  console.log('📁 Config Location: .vscode/settings.json');
  console.log('📚 Documentation: .mcp/README.md');
  console.log('🧪 Usage Guide: .mcp/USAGE_GUIDE.md');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
}

// Run the test
showConfiguration();
testMCPConnection();