# CutRequestStudio Database Schema

This document describes the database schema and relationships for the CutRequestStudio application.

## Overview

The application uses SQLAlchemy ORM with SQLite for development and PostgreSQL for production. The schema is designed to support tire request management with detailed processing workflows.

## Entity Relationship Diagram

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    User     │    │    Request      │    │   Attachment    │
│             │    │                 │    │                 │
│ id (PK)     │    │ id (PK)         │    │ id (PK)         │
│ email       │    │ request_by      │    │ name            │
│ password    │    │ project_no      │    │ size            │
│ full_name   │    │ tire_size       │    │ type            │
│ role        │    │ status          │    │ upload_date     │
│ is_active   │    │ request_date    │    │ status          │
└─────────────┘    │ target_date     │    │ request_id (FK) │
                   │ ...             │    └─────────────────┘
                   └─────────────────┘              │
                            │                       │
                            │ 1:N                   │ N:1
                            ▼                       ▼
                   ┌─────────────────┐    ┌─────────────────┐
                   │ RequestDetail   │    │                 │
                   │                 │    │                 │
                   │ id (PK)         │    │                 │
                   │ request_id (FK) │────┘                 │
                   │ tug_number      │                      │
                   │ project_number  │                      │
                   │ tire_size       │                      │
                   │ disposition     │                      │
                   │ process_number  │                      │
                   │ ...             │                      │
                   └─────────────────┘                      │
                            │                               │
                            │ 1:N                           │
                            ▼                               │
                   ┌─────────────────┐                      │
                   │RequestDetailCut │                      │
                   │                 │                      │
                   │ id (PK)         │                      │
                   │ request_detail_id (FK)                 │
                   │ set             │                      │
                   │ direction       │                      │
                   │ note            │                      │
                   └─────────────────┘                      │
                            │                               │
                            │ 1:N                           │
                            ▼                               │
                   ┌─────────────────┐    ┌─────────────────┐
                   │ CutProcessing   │    │   Processing    │
                   │                 │    │                 │
                   │ id (PK)         │    │ id (PK)         │
                   │ cut_id (FK)     │    │ tire_type       │
                   │ processing_id(FK)────▶│ description1    │
                   │ n               │    │ description2    │
                   │ note            │    │ test_code1      │
                   └─────────────────┘    │ test_code2      │
                                          │ cost            │
                   ┌─────────────────┐    │ picture         │
                   │     Tire        │    └─────────────────┘
                   │                 │
                   │ id (PK)         │
                   │ tug_no          │
                   │ spec_no         │
                   │ size            │
                   │ owner           │
                   │ load_index      │
                   │ pattern         │
                   │ project_no      │
                   │ location        │
                   └─────────────────┘
```

## Table Definitions

### User Table
Stores user account information with role-based access control.

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    hashed_password VARCHAR NOT NULL,
    full_name VARCHAR,
    role VARCHAR DEFAULT 'viewer',
    is_active INTEGER DEFAULT 1
);
```

**Fields:**
- `id`: Primary key, auto-incrementing integer
- `email`: Unique email address for login
- `hashed_password`: Bcrypt hashed password
- `full_name`: User's display name
- `role`: User role (`admin`, `editor`, `viewer`)
- `is_active`: Account status (1=active, 0=inactive)

**Indexes:**
- Primary key on `id`
- Unique index on `email`

### Request Table
Main entity for tire requests.

```sql
CREATE TABLE requests (
    id VARCHAR PRIMARY KEY,
    request_by VARCHAR NOT NULL,
    project_no VARCHAR NOT NULL,
    pid_project VARCHAR,
    tire_size VARCHAR NOT NULL,
    request_date DATETIME NOT NULL,
    target_date DATETIME NOT NULL,
    status VARCHAR NOT NULL,
    type VARCHAR NOT NULL,
    total_n INTEGER,
    destination VARCHAR,
    internal BOOLEAN,
    wish_date DATETIME,
    num_pneus_set INTEGER,
    num_pneus_set_unit VARCHAR,
    in_charge_of VARCHAR,
    note VARCHAR
);
```

**Fields:**
- `id`: Primary key, string format (e.g., "REQ001", "RFQ3")
- `request_by`: Name of person making the request
- `project_no`: Project number
- `pid_project`: PID project identifier
- `tire_size`: Tire size specification
- `request_date`: When request was created
- `target_date`: Desired completion date
- `status`: Request status (`pending`, `in_progress`, `completed`, etc.)
- `type`: Request type (`development`, `production`, etc.)
- `total_n`: Total number of tires
- `destination`: Where tires will be delivered
- `internal`: Whether request is internal
- `wish_date`: Preferred date
- `num_pneus_set`: Number of tires per set
- `num_pneus_set_unit`: Unit for tire sets
- `in_charge_of`: Person responsible
- `note`: Additional notes

**Indexes:**
- Primary key on `id`
- Index on `status` for filtering
- Index on `request_date` for sorting

### RequestDetail Table
Individual tire specifications within a request.

```sql
CREATE TABLE request_details (
    id VARCHAR PRIMARY KEY,
    request_id VARCHAR NOT NULL,
    tug_number VARCHAR NOT NULL,
    section VARCHAR NOT NULL,
    project_number VARCHAR NOT NULL,
    spec_number VARCHAR NOT NULL,
    tire_size VARCHAR NOT NULL,
    pattern VARCHAR NOT NULL,
    note VARCHAR,
    disposition VARCHAR NOT NULL,
    process_number INTEGER NOT NULL,
    FOREIGN KEY (request_id) REFERENCES requests(id)
);
```

**Fields:**
- `id`: Primary key, string format with prefixes ("DET-", "MGT-")
- `request_id`: Foreign key to requests table
- `tug_number`: TUG number identifier
- `section`: Tire section specification
- `project_number`: Project number
- `spec_number`: Specification number
- `tire_size`: Tire size
- `pattern`: Tire pattern
- `note`: Additional notes
- `disposition`: Approval status
- `process_number`: Process/quantity number

**Indexes:**
- Primary key on `id`
- Foreign key index on `request_id`
- Index on `tug_number` for searching

### Attachment Table
File attachments linked to requests.

```sql
CREATE TABLE attachments (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    size INTEGER NOT NULL,
    type VARCHAR NOT NULL,
    upload_date DATETIME NOT NULL,
    status VARCHAR NOT NULL,
    request_id VARCHAR,
    FOREIGN KEY (request_id) REFERENCES requests(id)
);
```

**Fields:**
- `id`: Primary key, string format
- `name`: Original filename
- `size`: File size in bytes
- `type`: MIME type
- `upload_date`: When file was uploaded
- `status`: Upload status
- `request_id`: Foreign key to requests table

### Tire Table
General tire catalog/inventory.

```sql
CREATE TABLE tires (
    id VARCHAR PRIMARY KEY,
    tug_no VARCHAR NOT NULL,
    spec_no VARCHAR NOT NULL,
    size VARCHAR NOT NULL,
    owner VARCHAR NOT NULL,
    load_index VARCHAR NOT NULL,
    pattern VARCHAR NOT NULL,
    project_no VARCHAR NOT NULL,
    location VARCHAR NOT NULL
);
```

**Fields:**
- `id`: Primary key, string format
- `tug_no`: TUG number
- `spec_no`: Specification number
- `size`: Tire size
- `owner`: Tire owner/manufacturer
- `load_index`: Load index rating
- `pattern`: Tire pattern
- `project_no`: Associated project
- `location`: Storage location

**Indexes:**
- Primary key on `id`
- Index on `owner` for filtering
- Index on `pattern` for filtering
- Index on `size` for filtering
- Index on `project_no` for filtering

### Processing Table
Available processing operations.

```sql
CREATE TABLE processing (
    id VARCHAR PRIMARY KEY,
    tire_type VARCHAR NOT NULL,
    description1 VARCHAR NOT NULL,
    description2 VARCHAR NOT NULL,
    test_code1 VARCHAR NOT NULL,
    test_code2 VARCHAR NOT NULL,
    cost VARCHAR NOT NULL,
    picture BOOLEAN DEFAULT FALSE
);
```

**Fields:**
- `id`: Primary key, string format
- `tire_type`: Type of tire this processing applies to
- `description1`: Primary description
- `description2`: Secondary description
- `test_code1`: Primary test code
- `test_code2`: Secondary test code
- `cost`: Processing cost
- `picture`: Whether pictures are available

**Indexes:**
- Primary key on `id`
- Index on `tire_type` for filtering

### RequestDetailCut Table
Cut specifications for tire details.

```sql
CREATE TABLE request_detail_cuts (
    id INTEGER PRIMARY KEY,
    request_detail_id VARCHAR NOT NULL,
    set VARCHAR NOT NULL,
    direction INTEGER NOT NULL,
    note VARCHAR,
    FOREIGN KEY (request_detail_id) REFERENCES request_details(id)
);
```

**Fields:**
- `id`: Primary key, auto-incrementing integer
- `request_detail_id`: Foreign key to request_details table
- `set`: Set specification (e.g., "1/4")
- `direction`: Direction value
- `note`: Additional notes

### CutProcessing Table
Association between cuts and processing operations.

```sql
CREATE TABLE cut_processing (
    id INTEGER PRIMARY KEY,
    cut_id INTEGER NOT NULL,
    processing_id VARCHAR NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    notes VARCHAR,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cut_id) REFERENCES request_detail_cuts(id),
    FOREIGN KEY (processing_id) REFERENCES processing(id)
);
```

**Fields:**
- `id`: Primary key, auto-incrementing integer
- `cut_id`: Foreign key to request_detail_cuts table
- `processing_id`: Foreign key to processing table
- `quantity`: Number/quantity for this processing (editable)
- `notes`: Additional notes (editable)
- `created_date`: Timestamp when the association was created

**Access Control:**
- **Editable Fields**: `quantity`, `notes` - Can be modified through the UI
- **Read-Only Fields**: `cut_id`, `processing_id`, `created_date` - Set during creation
- **Processing Details**: Description, codes, costs come from the Processing table and are read-only

## Relationships

### One-to-Many Relationships
1. **Request → RequestDetail**: One request can have multiple tire details
2. **Request → Attachment**: One request can have multiple file attachments
3. **RequestDetail → RequestDetailCut**: One tire detail can have multiple cuts

### Many-to-Many Relationships
1. **RequestDetailCut ↔ Processing**: Many cuts can use many processing operations (via CutProcessing table)

### Specialized Relationships
1. **Tire Processing Aggregation**: The system provides aggregated views of processing data for specific tires through the `/cut-processing/by-tire/{request_detail_id}` endpoint, which joins:
   - RequestDetail → RequestDetailCut → CutProcessing → Processing
   - This creates a comprehensive view of all processing operations for a specific tire

## Data Integrity

### Foreign Key Constraints
- All foreign keys have proper constraints with CASCADE options where appropriate
- Orphaned records are prevented through proper relationship definitions

### Validation Rules
- Email addresses must be unique in users table
- Request IDs follow specific format patterns
- Date fields must be valid datetime values
- Numeric fields have appropriate range constraints

## Indexing Strategy

### Primary Indexes
- All tables have primary key indexes
- Composite indexes on frequently queried combinations

### Secondary Indexes
- Foreign key columns for join performance
- Frequently filtered columns (status, dates, types)
- Search columns (names, descriptions)

## Migration Strategy

### Schema Changes
1. Create migration scripts for schema modifications
2. Backup data before applying changes
3. Test migrations on development environment first
4. Apply migrations during maintenance windows

### Data Migration
1. Export existing data before schema changes
2. Transform data to match new schema
3. Validate data integrity after migration
4. Rollback plan for failed migrations

## Performance Considerations

### Query Optimization
- Use appropriate indexes for common queries
- Avoid N+1 query problems with proper eager loading
- Implement pagination for large result sets

### Connection Management
- Use connection pooling for better performance
- Monitor connection usage and optimize pool size
- Implement proper connection cleanup

## Backup and Recovery

### Backup Strategy
- Daily automated backups
- Point-in-time recovery capability
- Test restore procedures regularly

### Recovery Procedures
- Document recovery steps
- Test recovery in non-production environment
- Maintain backup retention policy
