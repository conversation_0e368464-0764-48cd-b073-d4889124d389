#!/usr/bin/env python3
"""
Check database tables and content
"""

import sqlite3
import os

def check_database():
    """Check what tables exist in the database"""
    db_path = "app.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"📋 Database contains {len(tables)} tables:")
        for table in tables:
            table_name = table[0]
            print(f"   - {table_name}")
            
            # Get row count for each table
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"     ({count} rows)")
            
            # Show table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"     Columns: {', '.join([col[1] for col in columns])}")
            print()
            
    except Exception as e:
        print(f"❌ Error checking database: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
