import axiosInstance from "@/lib/axiosInstance";
import { EnhancedTire, EnhancedTireFormData } from "@/types";

// Enhanced Tire API paths
const ENHANCED_TIRE_API_PATH = "/tires";

// Utility functions for data transformation
const transformSnakeToCamel = (obj: any): any => {
  if (obj === null || obj === undefined) return obj;
  if (Array.isArray(obj)) return obj.map(transformSnakeToCamel);
  if (typeof obj !== 'object') return obj;

  const transformed: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    transformed[camelKey] = transformSnakeToCamel(value);
  }
  return transformed;
};

const transformCamelToSnake = (obj: any): any => {
  if (obj === null || obj === undefined) return obj;
  if (Array.isArray(obj)) return obj.map(transformCamelToSnake);
  if (typeof obj !== 'object') return obj;

  const transformed: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    transformed[snakeKey] = transformCamelToSnake(value);
  }
  return transformed;
};

// Interface for tire filter parameters
export interface EnhancedTireFilterParams {
  skip?: number;
  limit?: number;
  owner?: string;
  pattern?: string;
  size?: string;
  project_no?: string;
  is_active?: boolean;
}

// Interface for tire statistics
export interface TireStats {
  total_tires: number;
  active_tires: number;
  inactive_tires: number;
  top_owners: Array<{ owner: string; count: number }>;
  top_patterns: Array<{ pattern: string; count: number }>;
}

/**
 * Enhanced Tire Service - New Simplified Structure
 *
 * This service handles the enhanced tire catalog (master data) with the new simplified structure.
 * Replaces the old tire table with enhanced features like search, filtering, and soft delete.
 */

// Get all tires with optional filtering
export const getEnhancedTires = async (params?: EnhancedTireFilterParams): Promise<EnhancedTire[]> => {
  try {
    const response = await axiosInstance.get<any[]>(ENHANCED_TIRE_API_PATH, { params });
    console.log("[enhancedTireService] Raw response from API:", response.data);

    // Transform snake_case to camelCase
    const transformedData = transformSnakeToCamel(response.data);
    console.log("[enhancedTireService] Transformed data:", transformedData);

    return transformedData;
  } catch (error) {
    console.error("[enhancedTireService] Error fetching enhanced tires:", error);
    throw error;
  }
};

// Get a specific tire by ID
export const getEnhancedTire = async (id: string): Promise<EnhancedTire> => {
  try {
    const response = await axiosInstance.get<any>(`${ENHANCED_TIRE_API_PATH}/${id}`);
    return transformSnakeToCamel(response.data);
  } catch (error) {
    console.error(`[enhancedTireService] Error fetching enhanced tire with ID ${id}:`, error);
    throw error;
  }
};

// Create a new tire
export const createEnhancedTire = async (data: EnhancedTireFormData): Promise<EnhancedTire> => {
  try {
    const response = await axiosInstance.post<EnhancedTire>(ENHANCED_TIRE_API_PATH, data);
    return response.data;
  } catch (error) {
    console.error("[enhancedTireService] Error creating enhanced tire:", error);
    throw error;
  }
};

// Update an existing tire
export const updateEnhancedTire = async (id: string, data: Partial<EnhancedTireFormData>): Promise<EnhancedTire> => {
  try {
    const response = await axiosInstance.put<EnhancedTire>(`${ENHANCED_TIRE_API_PATH}/${id}`, data);
    return response.data;
  } catch (error) {
    console.error(`[enhancedTireService] Error updating enhanced tire with ID ${id}:`, error);
    throw error;
  }
};

// Soft delete a tire (set is_active = False)
export const deleteEnhancedTire = async (id: string): Promise<EnhancedTire> => {
  try {
    const response = await axiosInstance.delete<EnhancedTire>(`${ENHANCED_TIRE_API_PATH}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`[enhancedTireService] Error deleting enhanced tire with ID ${id}:`, error);
    throw error;
  }
};

// Get a tire by TUG number (unique identifier)
export const getEnhancedTireByTug = async (tugNo: string): Promise<EnhancedTire> => {
  try {
    const response = await axiosInstance.get<any>(`${ENHANCED_TIRE_API_PATH}/search/by-tug/${tugNo}`);
    return transformSnakeToCamel(response.data);
  } catch (error) {
    console.error(`[enhancedTireService] Error fetching enhanced tire with TUG ${tugNo}:`, error);
    throw error;
  }
};

// Get tire catalog statistics
export const getEnhancedTireStats = async (): Promise<TireStats> => {
  try {
    const response = await axiosInstance.get<TireStats>(`${ENHANCED_TIRE_API_PATH}/stats/summary`);
    return response.data;
  } catch (error) {
    console.error("[enhancedTireService] Error fetching enhanced tire stats:", error);
    throw error;
  }
};

// Search tires with multiple filters
export const searchEnhancedTires = async (searchParams: {
  query?: string;
  owner?: string;
  pattern?: string;
  size?: string;
  projectNo?: string;
  isActive?: boolean;
  limit?: number;
}): Promise<EnhancedTire[]> => {
  try {
    const params: EnhancedTireFilterParams = {
      limit: searchParams.limit || 100,
      owner: searchParams.owner,
      pattern: searchParams.pattern,
      size: searchParams.size,
      project_no: searchParams.projectNo,
      is_active: searchParams.isActive,
    };

    return await getEnhancedTires(params);
  } catch (error) {
    console.error("[enhancedTireService] Error searching enhanced tires:", error);
    throw error;
  }
};

// Batch operations for multiple tires
export const batchUpdateEnhancedTires = async (
  updates: Array<{ id: string; data: Partial<EnhancedTireFormData> }>
): Promise<EnhancedTire[]> => {
  try {
    const updatePromises = updates.map(({ id, data }) => updateEnhancedTire(id, data));
    return await Promise.all(updatePromises);
  } catch (error) {
    console.error("[enhancedTireService] Error in batch update enhanced tires:", error);
    throw error;
  }
};

// Validate tire data before submission
export const validateEnhancedTireData = (data: EnhancedTireFormData): string[] => {
  const errors: string[] = [];

  if (!data.tugNo?.trim()) {
    errors.push("TUG number is required");
  }

  if (!data.specNo?.trim()) {
    errors.push("Spec number is required");
  }

  if (!data.size?.trim()) {
    errors.push("Tire size is required");
  }

  if (!data.owner?.trim()) {
    errors.push("Owner is required");
  }

  if (!data.loadIndex?.trim()) {
    errors.push("Load index is required");
  }

  if (!data.pattern?.trim()) {
    errors.push("Pattern is required");
  }

  if (!data.projectNo?.trim()) {
    errors.push("Project number is required");
  }

  if (!data.location?.trim()) {
    errors.push("Location is required");
  }

  return errors;
};

// Transform legacy tire data to enhanced tire format
export const transformLegacyToEnhanced = (legacyTire: any): EnhancedTireFormData => {
  return {
    id: legacyTire.id,
    tugNo: legacyTire.tugNo || legacyTire.tug_no || "",
    specNo: legacyTire.specNo || legacyTire.spec_no || "",
    size: legacyTire.size || legacyTire.tireSize || "",
    owner: legacyTire.owner || "Unknown",
    loadIndex: legacyTire.loadIndex || legacyTire.load_index || "",
    pattern: legacyTire.pattern || "",
    projectNo: legacyTire.projectNo || legacyTire.project_no || "",
    location: legacyTire.location || "Unknown",
    description: legacyTire.description || legacyTire.note || "",
    isActive: legacyTire.isActive !== undefined ? legacyTire.isActive : true,
  };
};

// Transform enhanced tire to legacy format for backward compatibility
export const transformEnhancedToLegacy = (enhancedTire: EnhancedTire): any => {
  return {
    id: enhancedTire.id,
    tugNo: enhancedTire.tugNo,
    specNo: enhancedTire.specNo,
    size: enhancedTire.size,
    owner: enhancedTire.owner,
    loadIndex: enhancedTire.loadIndex,
    pattern: enhancedTire.pattern,
    projectNo: enhancedTire.projectNo,
    location: enhancedTire.location,
    note: enhancedTire.description,
    tireSize: enhancedTire.size,
    tug_no: enhancedTire.tugNo,
    spec_no: enhancedTire.specNo,
    project_no: enhancedTire.projectNo,
    load_index: enhancedTire.loadIndex,
  };
};
