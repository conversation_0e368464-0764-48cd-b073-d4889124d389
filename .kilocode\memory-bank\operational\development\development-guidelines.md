# Development Guidelines

**Category**: Operational Knowledge
**Type**: Development Standards
**Status**: Active
**Last Updated**: 2025-05-28

## Overview

This document establishes comprehensive development guidelines for the CutRequestStudio project, ensuring consistent code quality, maintainable architecture, and efficient development workflows. These guidelines are based on lessons learned from Priority 1-3 implementations and industry best practices.

## Code Standards

### TypeScript/JavaScript Standards

#### Naming Conventions

```typescript
// Interfaces and Types - PascalCase
interface RequestFormData {
  requestBy: string;
  projectNo: string;
}

// Classes - PascalCase
class GenericCrudService<T> {
  // Methods - camelCase
  async getById(id: string): Promise<T> {
    // Variables - camelCase
    const requestConfig = this.buildConfig();
    return this.executeRequest(requestConfig);
  }
}

// Constants - SCREAMING_SNAKE_CASE
const API_BASE_URL = 'http://localhost:8000/api/v1';
const DEFAULT_TIMEOUT = 5000;

// Files and directories - kebab-case
// generic-crud-service.ts
// request-detail-components/
```

#### Code Organization

```typescript
// File structure template
// 1. Imports (external libraries first, then internal)
import React, { useState, useEffect } from 'react';
import axios from 'axios';

import { GenericCrudService } from '../lib/generic-crud-service';
import { useUniversalForm } from '../hooks/useUniversalForm';
import { RequestFormData } from '../types';

// 2. Types and interfaces
interface ComponentProps {
  requestId: string;
  onSuccess?: (result: any) => void;
}

// 3. Constants
const FORM_VALIDATION_SCHEMA = {
  // validation rules
};

// 4. Main component/function
export const RequestForm: React.FC<ComponentProps> = ({ requestId, onSuccess }) => {
  // Component implementation
};

// 5. Default export (if applicable)
export default RequestForm;
```

#### Function and Method Standards

```typescript
// Function documentation with JSDoc
/**
 * Creates a new request with validation and error handling
 * @param requestData - The request data to create
 * @param options - Optional configuration for the request
 * @returns Promise resolving to the created request
 * @throws {ValidationError} When request data is invalid
 * @throws {ApiError} When API call fails
 *
 * @example
 * ```typescript
 * const request = await createRequest({
 *   requestBy: 'John Doe',
 *   projectNo: 'PRJ-001'
 * });
 * ```
 */
async function createRequest(
  requestData: RequestFormData,
  options?: RequestOptions
): Promise<AppRequest> {
  // Implementation with proper error handling
  try {
    const validatedData = validateRequestData(requestData);
    const result = await requestService.create(validatedData);
    return result;
  } catch (error) {
    throw new ApiError(`Failed to create request: ${error.message}`);
  }
}

// Prefer async/await over Promises
// ✅ Good
async function fetchUserData(id: string): Promise<User> {
  const user = await userService.getById(id);
  return user;
}

// ❌ Avoid
function fetchUserData(id: string): Promise<User> {
  return userService.getById(id).then(user => user);
}
```

### Python Standards

#### Naming Conventions

```python
# Classes - PascalCase
class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Base class for CRUD operations"""

    # Methods - snake_case
    def get_by_id(self, db: Session, id: Any) -> Optional[ModelType]:
        """Get entity by ID"""
        return db.query(self.model).filter(self.model.id == id).first()

    # Private methods - leading underscore
    def _validate_data(self, data: dict) -> bool:
        """Internal validation method"""
        return True

# Constants - SCREAMING_SNAKE_CASE
DATABASE_URL = "sqlite:///./app.db"
DEFAULT_LIMIT = 100

# Variables - snake_case
user_data = {"name": "John", "email": "<EMAIL>"}
```

#### Code Organization

```python
# File structure template
# 1. Standard library imports
from typing import Optional, List, Generic, TypeVar
import logging

# 2. Third-party imports
from sqlalchemy.orm import Session
from fastapi import HTTPException

# 3. Local imports
from .models import User
from .schemas import UserCreate, UserUpdate
from .crud_base import CRUDBase

# 4. Type variables and constants
ModelType = TypeVar("ModelType")
DEFAULT_SKIP = 0
DEFAULT_LIMIT = 100

# 5. Class/function definitions
class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """CRUD operations for User model"""

    def get_by_email(self, db: Session, email: str) -> Optional[User]:
        """Get user by email address"""
        return db.query(User).filter(User.email == email).first()

# 6. Instance creation
crud_user = CRUDUser(User)
```

## Architecture Patterns

### Component Architecture

#### React Component Structure

```typescript
// Component template with all standard patterns
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useUniversalForm } from '../hooks/useUniversalForm';
import { ErrorHandler } from '../lib/error-handler';
import { RequestValidationSchema } from '../lib/validation-schemas';

interface RequestFormProps {
  requestId?: string;
  initialData?: Partial<RequestFormData>;
  onSubmit?: (data: RequestFormData) => Promise<void>;
  onCancel?: () => void;
}

export const RequestForm: React.FC<RequestFormProps> = ({
  requestId,
  initialData,
  onSubmit,
  onCancel
}) => {
  // 1. State management with Universal Form Hook
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit
  } = useUniversalForm<RequestFormData>({
    initialData,
    validationSchema: RequestValidationSchema,
    onSubmit: handleFormSubmit
  });

  // 2. Memoized values
  const isEditMode = useMemo(() => Boolean(requestId), [requestId]);

  // 3. Callback functions
  const handleFormSubmit = useCallback(async (data: RequestFormData) => {
    return ErrorHandler.handleApiCall(
      () => onSubmit?.(data),
      { showToast: true, retryCount: 2 }
    );
  }, [onSubmit]);

  // 4. Effects
  useEffect(() => {
    // Component initialization logic
  }, []);

  // 5. Render
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Form fields */}
    </form>
  );
};

// 6. Default props and display name
RequestForm.displayName = 'RequestForm';
```

#### Service Layer Pattern

```typescript
// Service implementation following established patterns
import { CrudServiceFactory } from '../lib/generic-crud-service';
import { EnhancedDataTransformer } from '../lib/data-transformer';
import { ErrorHandler } from '../lib/error-handler';

class RequestService {
  private crudService = CrudServiceFactory.createEnhancedService<AppRequest, RequestFormData>(
    '/requests',
    {
      requestTransform: EnhancedDataTransformer.camelToSnake,
      responseTransform: EnhancedDataTransformer.snakeToCamel
    }
  );

  async getAll(filters?: RequestFilters): Promise<AppRequest[]> {
    return ErrorHandler.handleApiCall(
      () => this.crudService.getAll(filters),
      { cacheKey: 'requests-list', ttl: 300000 }
    );
  }

  async getById(id: string): Promise<AppRequest> {
    return ErrorHandler.handleApiCall(
      () => this.crudService.getById(id),
      { cacheKey: `request-${id}`, ttl: 600000 }
    );
  }

  // Additional service methods...
}

export const requestService = new RequestService();
```

### Backend Architecture

#### FastAPI Router Pattern

```python
# Router implementation following established patterns
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models import Request
from ..schemas import RequestCreate, RequestUpdate, RequestResponse
from ..crud import crud_request
from ..auth import get_current_user

router = APIRouter(prefix="/requests", tags=["requests"])

@router.get("/", response_model=List[RequestResponse])
async def get_requests(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get all requests with pagination"""
    try:
        requests = crud_request.get_multi(db, skip=skip, limit=limit)
        return requests
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=RequestResponse)
async def create_request(
    request_data: RequestCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new request"""
    try:
        request = crud_request.create(db, obj_in=request_data)
        return request
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
```

## Testing Standards

### Frontend Testing

#### Unit Testing with Jest and React Testing Library

```typescript
// Component testing template
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RequestForm } from '../RequestForm';
import { requestService } from '../../services/requestService';

// Mock dependencies
jest.mock('../../services/requestService');
const mockRequestService = requestService as jest.Mocked<typeof requestService>;

describe('RequestForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render form fields correctly', () => {
    render(<RequestForm />);

    expect(screen.getByLabelText(/request by/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/project number/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
  });

  it('should handle form submission', async () => {
    const mockOnSubmit = jest.fn().mockResolvedValue({ id: '123' });
    mockRequestService.create.mockResolvedValue({ id: '123' } as any);

    render(<RequestForm onSubmit={mockOnSubmit} />);

    const user = userEvent.setup();
    await user.type(screen.getByLabelText(/request by/i), 'John Doe');
    await user.type(screen.getByLabelText(/project number/i), 'PRJ-001');
    await user.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        requestBy: 'John Doe',
        projectNo: 'PRJ-001'
      });
    });
  });

  it('should display validation errors', async () => {
    render(<RequestForm />);

    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText(/request by is required/i)).toBeInTheDocument();
    });
  });
});
```

#### Integration Testing

```typescript
// Integration test template
import { renderHook, act } from '@testing-library/react';
import { useUniversalForm } from '../useUniversalForm';
import { RequestValidationSchema } from '../../lib/validation-schemas';

describe('useUniversalForm Integration', () => {
  it('should integrate with validation schema', async () => {
    const { result } = renderHook(() =>
      useUniversalForm<RequestFormData>({
        validationSchema: RequestValidationSchema
      })
    );

    act(() => {
      result.current.handleChange('requestBy', '');
    });

    const isValid = result.current.validateForm();
    expect(isValid).toBe(false);
    expect(result.current.errors.requestBy).toBeDefined();
  });
});
```

### Backend Testing

#### FastAPI Testing with pytest

```python
# Backend testing template
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from ..main import app
from ..database import get_db
from ..models import Request
from ..schemas import RequestCreate

client = TestClient(app)

def test_create_request(db: Session):
    """Test request creation endpoint"""
    request_data = {
        "request_by": "John Doe",
        "project_no": "PRJ-001",
        "tire_size": "225/60R16",
        "request_date": "2025-05-28",
        "target_date": "2025-06-15"
    }

    response = client.post("/api/v1/requests/", json=request_data)
    assert response.status_code == 200

    data = response.json()
    assert data["request_by"] == "John Doe"
    assert data["project_no"] == "PRJ-001"

def test_get_requests(db: Session):
    """Test request listing endpoint"""
    response = client.get("/api/v1/requests/")
    assert response.status_code == 200

    data = response.json()
    assert isinstance(data, list)

@pytest.fixture
def sample_request(db: Session):
    """Create a sample request for testing"""
    request_data = RequestCreate(
        request_by="Test User",
        project_no="TEST-001",
        tire_size="225/60R16"
    )
    return crud_request.create(db, obj_in=request_data)
```

## Performance Guidelines

### Frontend Performance

#### Component Optimization

```typescript
// Performance optimization patterns
import React, { memo, useMemo, useCallback } from 'react';

// 1. Memoize expensive components
const ExpensiveComponent = memo<ComponentProps>(({ data, onUpdate }) => {
  // 2. Memoize expensive calculations
  const processedData = useMemo(() => {
    return data.map(item => expensiveTransformation(item));
  }, [data]);

  // 3. Memoize callback functions
  const handleUpdate = useCallback((id: string, updates: any) => {
    onUpdate(id, updates);
  }, [onUpdate]);

  return (
    <div>
      {processedData.map(item => (
        <ItemComponent
          key={item.id}
          item={item}
          onUpdate={handleUpdate}
        />
      ))}
    </div>
  );
});

// 4. Use React.lazy for code splitting
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// 5. Implement proper loading states
const ComponentWithLoading = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <LazyComponent />
  </Suspense>
);
```

#### API Optimization

```typescript
// API performance patterns
import { useOptimizedApiCall } from '../lib/performance-optimizer';

const useRequestData = (requestId: string) => {
  // 1. Use optimized API calls with caching
  const { data, loading, error, refetch } = useOptimizedApiCall(
    () => requestService.getById(requestId),
    `request-${requestId}`,
    { ttl: 5 * 60 * 1000 } // 5 minutes cache
  );

  // 2. Implement proper error boundaries
  if (error) {
    throw new Error(`Failed to load request: ${error.message}`);
  }

  return { data, loading, refetch };
};
```

### Backend Performance

#### Database Optimization

```python
# Database performance patterns
from sqlalchemy.orm import joinedload, selectinload

class CRUDRequest(CRUDBase[Request, RequestCreate, RequestUpdate]):
    def get_with_details(self, db: Session, id: str) -> Optional[Request]:
        """Get request with related data using optimized loading"""
        return db.query(Request)\
            .options(
                joinedload(Request.request_details)
                .joinedload(RequestDetail.cuts)
                .joinedload(RequestDetailCut.processing)
            )\
            .filter(Request.id == id)\
            .first()

    def get_multi_optimized(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100
    ) -> List[Request]:
        """Get multiple requests with optimized query"""
        return db.query(Request)\
            .options(selectinload(Request.request_details))\
            .offset(skip)\
            .limit(limit)\
            .all()
```

## Documentation Standards

### Code Documentation

#### JSDoc for TypeScript

```typescript
/**
 * Universal form hook for managing form state and validation
 *
 * @template T - The type of form data
 * @param options - Configuration options for the form
 * @param options.initialData - Initial form data values
 * @param options.validationSchema - Schema for form validation
 * @param options.onSubmit - Callback function for form submission
 * @returns Form state and handlers
 *
 * @example
 * ```typescript
 * const form = useUniversalForm<UserFormData>({
 *   initialData: { name: '', email: '' },
 *   validationSchema: UserValidationSchema,
 *   onSubmit: async (data) => {
 *     await userService.create(data);
 *   }
 * });
 * ```
 */
export function useUniversalForm<T extends Record<string, any>>(
  options: UseUniversalFormOptions<T>
): UseUniversalFormReturn<T> {
  // Implementation
}
```

#### Python Docstrings

```python
def create_request(
    db: Session,
    obj_in: RequestCreate
) -> Request:
    """
    Create a new request in the database.

    Args:
        db: Database session
        obj_in: Request creation data

    Returns:
        Created request object

    Raises:
        ValidationError: If request data is invalid
        DatabaseError: If database operation fails

    Example:
        >>> request_data = RequestCreate(
        ...     request_by="John Doe",
        ...     project_no="PRJ-001"
        ... )
        >>> request = create_request(db, request_data)
        >>> print(request.id)
        'req-123'
    """
    # Implementation
```

### API Documentation

#### OpenAPI/Swagger Standards

```python
@router.post(
    "/",
    response_model=RequestResponse,
    status_code=201,
    summary="Create a new request",
    description="Create a new tire request with validation and processing setup",
    responses={
        201: {"description": "Request created successfully"},
        400: {"description": "Invalid request data"},
        422: {"description": "Validation error"}
    }
)
async def create_request(
    request_data: RequestCreate = Body(
        ...,
        example={
            "request_by": "John Doe",
            "project_no": "PRJ-001",
            "tire_size": "225/60R16",
            "request_date": "2025-05-28",
            "target_date": "2025-06-15"
        }
    ),
    db: Session = Depends(get_db)
):
    """Create a new tire request"""
    # Implementation
```

## Git Workflow

### Commit Standards

#### Conventional Commits

```bash
# Commit message format
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]

# Examples
feat(crud): add generic CRUD service implementation
fix(validation): resolve email validation regex issue
docs(api): update request endpoint documentation
refactor(forms): migrate to universal form hook
test(integration): add priority 2 integration tests
perf(cache): optimize API response caching
```

#### Commit Types

- **feat**: New feature implementation
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring without feature changes
- **test**: Adding or updating tests
- **perf**: Performance improvements
- **chore**: Maintenance tasks

### Branch Strategy

```bash
# Branch naming convention
<type>/<scope>/<description>

# Examples
feature/crud/generic-service-implementation
bugfix/validation/email-regex-fix
hotfix/security/auth-token-validation
refactor/forms/universal-hook-migration
```

### Pull Request Process

1. **Create Feature Branch**: From `develop` branch
2. **Implement Changes**: Following coding standards
3. **Write Tests**: Comprehensive test coverage
4. **Update Documentation**: Code and API documentation
5. **Create Pull Request**: With detailed description
6. **Code Review**: Peer review and approval
7. **Merge**: Squash and merge to `develop`

## Quality Assurance

### Code Quality Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| **Test Coverage** | > 90% | Jest/pytest coverage reports |
| **Code Duplication** | < 5% | SonarQube analysis |
| **Cyclomatic Complexity** | < 10 | ESLint/pylint analysis |
| **Type Coverage** | 100% | TypeScript strict mode |
| **Documentation Coverage** | > 80% | JSDoc/docstring coverage |

### Automated Quality Checks

```yaml
# GitHub Actions workflow example
name: Quality Assurance
on: [push, pull_request]

jobs:
  frontend-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run linting
        run: npm run lint
      - name: Run type checking
        run: npm run type-check
      - name: Run tests
        run: npm run test:coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v1

  backend-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run linting
        run: flake8 .
      - name: Run type checking
        run: mypy .
      - name: Run tests
        run: pytest --cov=.
```

## Related Documentation

- **Architecture Patterns**: [Technical Patterns](../../technical/architecture/patterns/)
- **Testing Guide**: [Testing Strategies](./testing-strategies.md)
- **Performance Guide**: [Performance Optimization](./performance-optimization.md)
- **Security Guide**: [Security Guidelines](./security-guidelines.md)

---

**Guidelines Status**: ✅ Active and Enforced
**Next Review**: 2025-06-28
**Owner**: Development Team
**Priority**: Critical - Development Foundation