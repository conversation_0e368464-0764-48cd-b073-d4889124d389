# MCP Playwright Server Usage Guide

## Overview

The MCP Playwright Server provides browser automation capabilities for the CutRequestStudio project. This guide explains how to use the server's tools and integrate them into your development workflow.

## Available Tools

### 1. Browser Navigation
- **navigate_to**: Navigate to specific URLs
- **go_back**: Navigate back in browser history
- **go_forward**: Navigate forward in browser history
- **reload**: Reload the current page

### 2. Element Interaction
- **click_element**: Click on page elements
- **type_text**: Type text into input fields
- **select_option**: Select options from dropdowns
- **scroll_to**: Scroll to specific elements

### 3. Screenshot Capture
- **take_screenshot**: Capture full page screenshots
- **take_element_screenshot**: Capture specific element screenshots
- **take_viewport_screenshot**: Capture visible area screenshots

### 4. Data Extraction
- **extract_text**: Extract text from elements
- **extract_attributes**: Get element attributes
- **extract_table_data**: Extract data from tables
- **get_page_title**: Get the page title

### 5. JavaScript Execution
- **execute_javascript**: Run custom JavaScript code
- **evaluate_expression**: Evaluate JavaScript expressions
- **inject_script**: Inject scripts into the page

### 6. Page Analysis
- **get_page_source**: Get the HTML source
- **find_elements**: Find elements by selector
- **wait_for_element**: Wait for elements to appear
- **check_element_exists**: Check if elements exist

## CutRequestStudio Integration Examples

### Testing Tire Management Workflow

```javascript
// Navigate to the tire management page
await navigate_to("http://localhost:9002/dashboard");

// Take a screenshot of the dashboard
await take_screenshot("dashboard-overview.png");

// Click on tire management button
await click_element("[data-testid='tire-management-button']");

// Wait for tire dialog to open
await wait_for_element("[data-testid='tire-management-dialog']");

// Take screenshot of tire management interface
await take_element_screenshot("[data-testid='tire-management-dialog']", "tire-management-dialog.png");
```

### Automated Testing Scenarios

```javascript
// Test tire search functionality
await type_text("[data-testid='tire-search-input']", "Michelin");
await click_element("[data-testid='search-button']");
await wait_for_element("[data-testid='search-results']");

// Extract search results
const results = await extract_table_data("[data-testid='tire-results-table']");

// Verify results contain expected data
const hasResults = await execute_javascript(`
  return document.querySelectorAll('[data-testid="tire-result-row"]').length > 0;
`);
```

### Documentation Generation

```javascript
// Generate screenshots for documentation
const pages = [
  { url: "/dashboard", name: "dashboard" },
  { url: "/dashboard/richiesta", name: "request-form" },
  { url: "/dashboard/dettaglio", name: "request-detail" },
  { url: "/dashboard/lavorazioni", name: "processing" }
];

for (const page of pages) {
  await navigate_to(`http://localhost:9002${page.url}`);
  await take_screenshot(`docs/screenshots/${page.name}.png`);
}
```

### Data Validation

```javascript
// Scrape tire data for validation
await navigate_to("http://localhost:9002/dashboard/dettaglio");

// Extract tire specifications
const tireSpecs = await extract_text("[data-testid='tire-specifications']");

// Validate tire codes format
const isValidCode = await execute_javascript(`
  const code = document.querySelector('[data-testid="tire-code"]').textContent;
  return /^[A-Z0-9]{8,12}$/.test(code);
`);
```

## Best Practices

### 1. Error Handling
Always include proper error handling in your automation scripts:

```javascript
try {
  await navigate_to("http://localhost:9002");
  await wait_for_element("[data-testid='main-content']", { timeout: 5000 });
} catch (error) {
  console.error("Navigation failed:", error);
  await take_screenshot("error-state.png");
}
```

### 2. Wait Strategies
Use appropriate wait strategies for dynamic content:

```javascript
// Wait for API data to load
await wait_for_element("[data-testid='tire-list']:not(.loading)");

// Wait for animations to complete
await execute_javascript("return new Promise(resolve => setTimeout(resolve, 500))");
```

### 3. Selector Best Practices
Use stable selectors for reliable automation:

```javascript
// Good: Use data-testid attributes
await click_element("[data-testid='submit-button']");

// Avoid: CSS classes that might change
// await click_element(".btn-primary");
```

### 4. Screenshot Organization
Organize screenshots with descriptive names:

```javascript
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
await take_screenshot(`test-results/dashboard-${timestamp}.png`);
```

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: E2E Tests with MCP Playwright

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Install MCP Playwright Server
        run: npm install -g @executeautomation/playwright-mcp-server

      - name: Start application
        run: npm run dev &

      - name: Run MCP Playwright tests
        run: npx mcp-playwright-test
```

## Troubleshooting

### Common Issues

1. **Server not starting**: Check if the package is installed globally
2. **Connection timeout**: Verify the application is running on the correct port
3. **Element not found**: Use wait strategies and verify selectors
4. **Screenshot failures**: Check file permissions and directory existence

### Debug Mode

Enable debug logging in VS Code settings:

```json
{
  "mcp.enableLogging": true,
  "mcp.logLevel": "debug"
}
```

## Performance Tips

1. **Reuse browser instances** when possible
2. **Use headless mode** for faster execution
3. **Implement smart waiting** instead of fixed delays
4. **Batch operations** to reduce overhead
5. **Clean up resources** after test completion

## Security Considerations

1. **Validate URLs** before navigation
2. **Sanitize input data** before injection
3. **Use secure connections** for external sites
4. **Limit script execution scope**
5. **Monitor resource usage**