#!/usr/bin/env python3
"""
Test the new simplified CRUD operations directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database import SessionLocal
from backend import crud, schemas

def test_simplified_crud():
    """Test all new simplified CRUD operations"""
    print("🚀 Testing Simplified CRUD Operations")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Test Enhanced Tire CRUD
        print("\n🛞 Testing Enhanced Tire CRUD")
        
        # Get all tires
        tires = crud.get_tires_enhanced(db)
        print(f"   ✅ Found {len(tires)} tires")
        
        if tires:
            tire = tires[0]
            print(f"   📋 Sample tire: {tire.id} - {tire.tug_no} ({tire.size})")
            
            # Get specific tire
            specific_tire = crud.get_tire_enhanced(db, tire.id)
            print(f"   ✅ Retrieved specific tire: {specific_tire.tug_no}")
        
        # Test Request Items CRUD
        print("\n📦 Testing Request Items CRUD")
        
        # Get request items for REQ001
        items = crud.get_request_items_by_request(db, "REQ001")
        print(f"   ✅ Found {len(items)} items for REQ001")
        
        if items:
            item = items[0]
            print(f"   📋 Sample item: {item.id} - Tire: {item.tire.tug_no if item.tire else 'N/A'} (qty: {item.quantity})")
            
            # Get specific item
            specific_item = crud.get_request_item(db, item.id)
            print(f"   ✅ Retrieved specific item: {specific_item.id}")
        
        # Test Cut Operations CRUD
        print("\n✂️ Testing Cut Operations CRUD")
        
        # Get cut operations for REQ001
        operations = crud.get_cut_operations_by_request(db, "REQ001")
        print(f"   ✅ Found {len(operations)} cut operations for REQ001")
        
        if operations:
            operation = operations[0]
            print(f"   📋 Sample operation: ID {operation.id} - Processing: {operation.processing.id if operation.processing else 'N/A'} (qty: {operation.quantity})")
            
            # Get specific operation
            specific_operation = crud.get_cut_operation(db, operation.id)
            print(f"   ✅ Retrieved specific operation: {specific_operation.id}")
        
        # Test Enhanced Request CRUD
        print("\n📋 Testing Enhanced Request CRUD")
        
        # Get simplified request
        request = crud.get_request_simplified(db, "REQ001")
        if request:
            print(f"   ✅ Retrieved simplified request: {request.id}")
            print(f"   📋 Request items: {len(request.request_items)}")
            print(f"   📋 Attachments: {len(request.attachments)}")
        
        # Test relationships
        print("\n🔗 Testing Relationships")
        
        if items:
            item_id = items[0].id
            item_operations = crud.get_cut_operations_by_request_item(db, item_id)
            print(f"   ✅ Found {len(item_operations)} operations for item {item_id}")
            
            for op in item_operations:
                processing_name = op.processing.description1 if op.processing else "Unknown"
                print(f"      - Operation {op.id}: {processing_name} (Status: {op.status})")
        
        # Test filtering
        print("\n🔍 Testing Filtering")
        
        # Filter tires by owner
        michelin_tires = crud.get_tires_enhanced(db, owner="Michelin")
        print(f"   ✅ Found {len(michelin_tires)} Michelin tires")
        
        # Filter tires by pattern
        pattern_tires = crud.get_tires_enhanced(db, pattern="High Performance")
        print(f"   ✅ Found {len(pattern_tires)} High Performance tires")
        
        print("\n" + "=" * 50)
        print("✅ All CRUD operations completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

def test_create_operations():
    """Test create operations"""
    print("\n🔧 Testing Create Operations")
    print("=" * 30)
    
    db = SessionLocal()
    
    try:
        # Create a new tire
        new_tire = schemas.TireEnhancedCreate(
            id="T_TEST_NEW",
            tug_no="TUG-TEST-NEW",
            spec_no="SPEC-TEST",
            size="225/45R17",
            owner="TestOwner",
            load_index="91V",
            pattern="TEST_PATTERN",
            project_no="PRJ-TEST",
            location="TEST-SHELF",
            description="Test tire for CRUD testing",
            is_active=True
        )
        
        created_tire = crud.create_tire_enhanced(db, new_tire)
        print(f"   ✅ Created tire: {created_tire.id} - {created_tire.tug_no}")
        
        # Create a new request item
        new_item = schemas.RequestItemCreate(
            id="ITEM_TEST_NEW",
            request_id="REQ001",
            tire_id=created_tire.id,
            quantity=2,
            disposition="AVAILABLE",
            notes="Test item created via CRUD",
            unit_price=150.0,
            section="TEST_SECTION"
        )
        
        created_item = crud.create_request_item(db, new_item)
        print(f"   ✅ Created request item: {created_item.id} for tire {created_item.tire_id}")
        
        # Create a new cut operation
        new_operation = schemas.CutOperationCreate(
            request_item_id=created_item.id,
            processing_id="PROC001",
            quantity=1,
            cut_price=75.50,
            status="PENDING",
            notes="Test cut operation created via CRUD"
        )
        
        created_operation = crud.create_cut_operation(db, new_operation)
        print(f"   ✅ Created cut operation: {created_operation.id} for item {created_operation.request_item_id}")
        
        print("   ✅ All create operations completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during create testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    test_simplified_crud()
    test_create_operations()
