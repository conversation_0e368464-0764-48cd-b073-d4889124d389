import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database import SessionLocal
from backend.models import User, Request, Attachment, RequestDetail, Processing
from backend.auth import get_password_hash
from datetime import datetime, timedelta
import random
import string

db = SessionLocal()

# Popola utenti con password hashate correttamente e ruoli
users_data = [
    {"email": "<EMAIL>", "full_name": "Admin User", "role": "admin"},
    {"email": "<EMAIL>", "full_name": "Editor User", "role": "editor"},
    {"email": "<EMAIL>", "full_name": "Viewer User", "role": "viewer"},
]

# Aggiungi altri utenti con ruoli casuali
for i in range(3, 10):
    users_data.append({
        "email": f"user{i}@example.com",
        "full_name": f"User {i}",
        "role": random.choice(["viewer", "editor", "admin"])
    })

for user_data in users_data:
    user = User(
        email=user_data["email"],
        hashed_password=get_password_hash("password"),  # Hash della password "password"
        full_name=user_data["full_name"],
        role=user_data["role"],
        is_active=True
    )
    db.add(user)
db.commit()

# Popola richieste
for i in range(10):
    req = Request(
        id=f"REQ{i}",
        request_by=f"user{i}@example.com",
        project_no=f"PRJ{i}",
        pid_project=f"PID{i}",
        tire_size="225/45R17",
        request_date=datetime.now() - timedelta(days=i),
        target_date=datetime.now() + timedelta(days=10+i),
        status=random.choice(["OPEN", "CLOSED", "IN_PROGRESS"]),
        type=random.choice(["NEWDEV", "REPAIR"]),
        total_n=random.randint(1, 10),
        destination="PLANT_A",
        internal=bool(i % 2),
        wish_date=datetime.now() + timedelta(days=5+i),
        num_pneus_set=random.randint(1, 4),
        num_pneus_set_unit="SET",
        in_charge_of=f"User {i}",
        note=f"Nota richiesta {i}"
    )
    db.add(req)
db.commit()

# Popola attachments
for i in range(10):
    att = Attachment(
        id=f"ATT{i}",
        name=f"file{i}.pdf",
        size=random.randint(1000, 10000),
        type="application/pdf",
        upload_date=datetime.now() - timedelta(days=i),
        status="Uploaded",
        request_id=f"REQ{random.randint(0,9)}"
    )
    db.add(att)
db.commit()

# Popola request_details
for i in range(30):
    detail = RequestDetail(
        id=f"DET{i}",
        request_id=f"REQ{random.randint(0,9)}",
        tug_number=f"TUG-{random.choice(['A','B','C'])}{random.randint(1,5)}",
        section=random.choice(['A','B','C']),
        project_number=f"PN-XP{random.randint(1,5)}",
        spec_number=f"SP{str(i+1).zfill(3)}",
        tire_size="245/40R18",
        pattern=random.choice(["P-Zero", "Pilot Sport 4S", "Eagle F1"]),
        note=random.choice(["Batch 1, new compound", "Reference tire", "Damaged sidewall", None]),
        disposition=random.choice(["AVAILABLE", "TESTED", "SCRAP"]),
        process_number=random.randint(1,10)
    )
    db.add(detail)
db.commit()

# Popola processing
tire_types = ["Type A", "Type B", "Type C", "Type D", "Type E"]

for i in range(50):
    proc = Processing(
        id=f"PROC{i+1}",
        tire_type=tire_types[i % len(tire_types)],
        description1=f"Desc 1 - Variant {i+1}",
        description2=f"Detail Desc 2 - Option {i+1}",
        test_code1=f"TC1-{str(i+1).zfill(3)}",
        test_code2=f"TC2-{str(i+1).zfill(3)}",
        cost=f"{round(random.uniform(50, 150), 2)} €",
        picture=bool(random.getrandbits(1))
    )
    db.add(proc)
db.commit()

db.close()
