// @ts-nocheck
"use client";

import * as React from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import type { AppRequest } from "@/types";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Trash2 } from "lucide-react";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";

function safeFormatDate(dateValue: string | Date | undefined | null) {
  if (!dateValue) return "-";
  const dateObj = new Date(dateValue);
  if (isNaN(dateObj.getTime())) return "-";
  return format(dateObj, "yyyy-MM-dd");
}


interface RequestTableProps {
  requests: AppRequest[];
  selectedRequestId: string | null;
  onRowClick: (id: string) => void;
  onSortChange?: (field: keyof AppRequest | string, direction: "asc" | "desc") => void;
  sortField?: string;
  sortDirection?: "asc" | "desc";
  onDeleteRequest?: (id: string) => void;
}

const StatusBadge = ({ status }: { status: string }) => {
  let variant: "default" | "secondary" | "destructive" | "outline" | "accent" = "outline";
  let badgeClassName = "";

  switch (status.toUpperCase()) {
    case "DRAFT":
      variant = "secondary";
      break;
    case "APPROVED":
      variant = "default";
      break;
    case "WORKING":
    case "FROZEN":
      variant = "outline";
      break;
    case "REJECTED":
    case "CANCELLED":
      variant = "destructive";
      break;
    case "COMPLETED":
      variant = "default"
      badgeClassName = "bg-accent text-accent-foreground hover:bg-accent/90";
      break;
    default:
      variant = "outline";
  }
  return <Badge variant={variant} className={cn(badgeClassName)}>{status}</Badge>;
};


export function RequestTableSection({
  requests,
  selectedRequestId,
  onRowClick,
  onSortChange,
  sortField,
  sortDirection,
  onDeleteRequest,
}: RequestTableProps) {
  // Safety check to ensure requests is always an array
  const safeRequests = requests || [];

  const handleSort = (columnId: keyof AppRequest | string) => {
    if (!onSortChange) return;
    let direction: "asc" | "desc" = "asc";
    if (sortField === columnId) {
      direction = sortDirection === "asc" ? "desc" : "asc";
    }
    onSortChange(columnId, direction);
  };

  const SortableHeader = ({
    children,
    columnId,
  }: {
    children: React.ReactNode;
    columnId: keyof AppRequest | string;
  }) => (
    <Button
      variant="ghost"
      className="px-1 py-1 h-auto hover:bg-transparent group focus-visible:ring-0 focus-visible:ring-offset-0"
      onClick={() => handleSort(columnId)}
    >
      <span className="group-hover:opacity-100 transition-opacity">{children}</span>
      <ArrowUpDown
        className={cn(
          "ml-2 h-4 w-4 opacity-50 group-hover:opacity-100 transition-opacity",
          sortField === columnId ? "text-primary" : ""
        )}
      />
    </Button>
  );


  return (
    <section aria-labelledby="request-table-heading">
      <div className="mb-2 text-sm text-muted-foreground">
        <p id="request-table-heading">
          {safeRequests.length} Requests found, displaying {safeRequests.length} Requests. Page 1 / 1
        </p>
      </div>
      <div className="rounded-md bg-card">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead><SortableHeader columnId="id">Request</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="requestBy">Request By</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="projectNo">Project#</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="tireSize">Tire Size</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="requestDate">Request Date</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="targetDate">Target Date</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="status">Status</SortableHeader></TableHead>
              <TableHead><SortableHeader columnId="type">Type</SortableHeader></TableHead>
              {onDeleteRequest && <TableHead className="w-[50px]">Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {safeRequests.map((request) => (
              <TableRow
                key={request.id}
                data-state={selectedRequestId === request.id ? "selected" : ""}
                onClick={() => onRowClick(request.id)}
                className={cn(
                  "cursor-pointer",
                  selectedRequestId === request.id ? "bg-primary/30" : "hover:bg-accent/30"
                )}
                aria-selected={selectedRequestId === request.id}
              >
                <TableCell>{request.id}</TableCell>
                <TableCell>{request.requestBy}</TableCell>
                <TableCell>{request.projectNo}</TableCell>
                <TableCell>{request.tireSize}</TableCell>
                <TableCell>{safeFormatDate(request.requestDate)}</TableCell>
                <TableCell>{safeFormatDate(request.targetDate)}</TableCell>
                <TableCell><StatusBadge status={request.status} /></TableCell>
                <TableCell>{request.type}</TableCell>
                {onDeleteRequest && (
                  <TableCell>
                    <DeleteButton
                      onClick={() => onDeleteRequest(request.id)}
                      title="Confirm Request Deletion"
                      description={`Are you sure you want to delete request ${request.id}? This action cannot be undone.`}
                      itemType="request"
                      itemName={request.id}
                      size="icon"
                      className="h-8 w-8"
                    />
                  </TableCell>
                )}
              </TableRow>
            ))}
             {safeRequests.length === 0 && (
              <TableRow>
                <TableCell colSpan={onDeleteRequest ? 8 : 7} className="text-center">
                  No requests found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {safeRequests.length > 0 && (
         <div className="mt-4 flex justify-center">
            <Button variant="outline" size="sm" className="rounded-full w-8 h-8 p-0">
            1
            </Button>
         </div>
      )}
    </section>
  );
}
