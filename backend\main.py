import os
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from typing import List

app = FastAPI(
    title="CutRequestStudio API",
    version="1.0.0"
)

# Configura CORS per accettare richieste dal frontend
# Leggi le origini da una variabile d'ambiente, separate da virgola
# Esempio: CORS_ORIGINS="http://localhost:3000,http://localhost:9002"
CORS_ORIGINS_STR = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:9002,http://localhost:8000,http://127.0.0.1:3000,null")
origins: List[str] = [origin.strip() for origin in CORS_ORIGINS_STR.split(',')]

# Aggiungi un controllo per assicurarti che origins non sia una lista vuota se CORS_ORIGINS_STR è vuoto
if not origins or not origins[0]: # Se la stringa era vuota o solo spazi/virgole
    origins = ["http://localhost:3000", "null"] # Default a un valore sicuro se non configurato, incluso null per file locali

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=False,  # Must be False when allow_origins=["*"]
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def health_check():
    return {"status": "ok"}

# Add token endpoint for authentication (alias for /users/login)
from fastapi import Depends
from fastapi.security import OAuth2PasswordRequestForm
from .database import get_db
from . import crud, auth_simple as auth, schemas
from sqlalchemy.orm import Session
from datetime import timedelta

@app.post("/token", response_model=schemas.Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = crud.get_user_by_email(db, email=form_data.username)
    if not user or not auth.verify_password(form_data.password, user.hashed_password):
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=auth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth.create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

from .routers import user
from .routers import request
from .routers import tire
from .routers import request_detail_cut
from .routers import cut_processing

# New simplified routers
from .routers import tires_enhanced
from .routers import request_items
from .routers import cut_operations

# Create API v1 router
from fastapi import APIRouter
api_v1 = APIRouter(prefix="/api/v1")

# Include all routers under /api/v1
api_v1.include_router(user.router)
api_v1.include_router(request.router)
api_v1.include_router(tire.router)
api_v1.include_router(request_detail_cut.router)
api_v1.include_router(cut_processing.router)

# Include new simplified routers under /api/v1
api_v1.include_router(tires_enhanced.router)
api_v1.include_router(request_items.router)
api_v1.include_router(cut_operations.router)

# Include the versioned API router in the main app
app.include_router(api_v1)
