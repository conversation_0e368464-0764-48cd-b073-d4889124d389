"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Search, Plus, Edit, Trash2, Eye } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { EnhancedTire, EnhancedTireFormData, initialEnhancedTireFormData } from "@/types";
import {
  getEnhancedTires,
  createEnhancedTire,
  updateEnhancedTire,
  deleteEnhancedTire,
  searchEnhancedTires,
  validateEnhancedTireData,
  type EnhancedTireFilterParams
} from "@/services/enhancedTireService";

interface EnhancedTireManagementProps {
  onTireSelect?: (tire: EnhancedTire) => void;
  selectionMode?: boolean;
  initialFilters?: EnhancedTireFilterParams;
}

export function EnhancedTireManagement({
  onTireSelect,
  selectionMode = false,
  initialFilters = {}
}: EnhancedTireManagementProps) {
  const [tires, setTires] = useState<EnhancedTire[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<EnhancedTireFilterParams>(initialFilters);
  const [selectedTire, setSelectedTire] = useState<EnhancedTire | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<EnhancedTireFormData>(initialEnhancedTireFormData);
  const [formErrors, setFormErrors] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);

  const { toast } = useToast();

  // Load tires on component mount and when filters change
  useEffect(() => {
    loadTires();
  }, [filters]);

  const loadTires = async () => {
    try {
      setLoading(true);
      const data = await getEnhancedTires(filters);
      setTires(data);
    } catch (error) {
      console.error("Error loading enhanced tires:", error);
      toast({
        title: "Error",
        description: "Failed to load tires. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadTires();
      return;
    }

    try {
      setLoading(true);
      const searchResults = await searchEnhancedTires({
        query: searchQuery,
        ...filters,
      });
      setTires(searchResults);
    } catch (error) {
      console.error("Error searching tires:", error);
      toast({
        title: "Error",
        description: "Search failed. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTire = () => {
    setFormData(initialEnhancedTireFormData);
    setIsEditing(false);
    setFormErrors([]);
    setIsDialogOpen(true);
  };

  const handleEditTire = (tire: EnhancedTire) => {
    setFormData({
      id: tire.id,
      tugNo: tire.tugNo,
      specNo: tire.specNo,
      size: tire.size,
      owner: tire.owner,
      loadIndex: tire.loadIndex,
      pattern: tire.pattern,
      projectNo: tire.projectNo,
      location: tire.location,
      description: tire.description || "",
      isActive: tire.isActive !== false,
    });
    setIsEditing(true);
    setFormErrors([]);
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    const errors = validateEnhancedTireData(formData);
    if (errors.length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setSubmitting(true);
      if (isEditing && formData.id) {
        await updateEnhancedTire(formData.id, formData);
        toast({
          title: "Success",
          description: "Tire updated successfully.",
        });
      } else {
        await createEnhancedTire(formData);
        toast({
          title: "Success",
          description: "Tire created successfully.",
        });
      }
      setIsDialogOpen(false);
      loadTires();
    } catch (error) {
      console.error("Error saving tire:", error);
      toast({
        title: "Error",
        description: "Failed to save tire. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteTire = async (tire: EnhancedTire) => {
    if (!confirm(`Are you sure you want to delete tire ${tire.tugNo}?`)) {
      return;
    }

    try {
      await deleteEnhancedTire(tire.id);
      toast({
        title: "Success",
        description: "Tire deleted successfully.",
      });
      loadTires();
    } catch (error) {
      console.error("Error deleting tire:", error);
      toast({
        title: "Error",
        description: "Failed to delete tire. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleTireClick = (tire: EnhancedTire) => {
    if (selectionMode && onTireSelect) {
      onTireSelect(tire);
    } else {
      setSelectedTire(tire);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Enhanced Tire Catalog</h2>
        <Button onClick={handleCreateTire} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add New Tire
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search tires..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Search
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="owner-filter">Owner</Label>
              <Input
                id="owner-filter"
                placeholder="Filter by owner"
                value={filters.owner || ""}
                onChange={(e) => setFilters({ ...filters, owner: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="pattern-filter">Pattern</Label>
              <Input
                id="pattern-filter"
                placeholder="Filter by pattern"
                value={filters.pattern || ""}
                onChange={(e) => setFilters({ ...filters, pattern: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="size-filter">Size</Label>
              <Input
                id="size-filter"
                placeholder="Filter by size"
                value={filters.size || ""}
                onChange={(e) => setFilters({ ...filters, size: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="project-filter">Project</Label>
              <Input
                id="project-filter"
                placeholder="Filter by project"
                value={filters.project_no || ""}
                onChange={(e) => setFilters({ ...filters, project_no: e.target.value })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tires Table */}
      <Card>
        <CardHeader>
          <CardTitle>Tires ({tires.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>TUG No</TableHead>
                  <TableHead>Spec No</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Pattern</TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tires.map((tire) => (
                  <TableRow
                    key={tire.id}
                    className={`cursor-pointer hover:bg-gray-50 ${
                      selectedTire?.id === tire.id ? "bg-blue-50" : ""
                    }`}
                    onClick={() => handleTireClick(tire)}
                  >
                    <TableCell className="font-medium">{tire.tugNo}</TableCell>
                    <TableCell>{tire.specNo}</TableCell>
                    <TableCell>{tire.size}</TableCell>
                    <TableCell>{tire.owner}</TableCell>
                    <TableCell>{tire.pattern}</TableCell>
                    <TableCell>{tire.projectNo}</TableCell>
                    <TableCell>
                      <Badge variant={tire.isActive !== false ? "default" : "secondary"}>
                        {tire.isActive !== false ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditTire(tire);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTire(tire);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{isEditing ? "Edit Tire" : "Create New Tire"}</DialogTitle>
            <DialogDescription>
              {isEditing ? "Update tire information" : "Add a new tire to the catalog"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="tugNo">TUG Number *</Label>
              <Input
                id="tugNo"
                value={formData.tugNo}
                onChange={(e) => setFormData({ ...formData, tugNo: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="specNo">Spec Number *</Label>
              <Input
                id="specNo"
                value={formData.specNo}
                onChange={(e) => setFormData({ ...formData, specNo: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="size">Size *</Label>
              <Input
                id="size"
                value={formData.size}
                onChange={(e) => setFormData({ ...formData, size: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="owner">Owner *</Label>
              <Input
                id="owner"
                value={formData.owner}
                onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="loadIndex">Load Index *</Label>
              <Input
                id="loadIndex"
                value={formData.loadIndex}
                onChange={(e) => setFormData({ ...formData, loadIndex: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="pattern">Pattern *</Label>
              <Input
                id="pattern"
                value={formData.pattern}
                onChange={(e) => setFormData({ ...formData, pattern: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="projectNo">Project Number *</Label>
              <Input
                id="projectNo"
                value={formData.projectNo}
                onChange={(e) => setFormData({ ...formData, projectNo: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="location">Location *</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </div>
            <div className="col-span-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
          </div>

          {formErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <ul className="text-red-600 text-sm space-y-1">
                {formErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={submitting}>
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
