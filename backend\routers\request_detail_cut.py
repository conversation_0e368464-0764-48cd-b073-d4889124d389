from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from .. import crud, models, schemas, auth
from ..database import get_db

router = APIRouter(
    prefix="/request-detail-cuts",
    tags=["request-detail-cuts"],
)

@router.get("/", response_model=List[schemas.RequestDetailCutRead], dependencies=[Depends(auth.require_viewer_role)])
def read_request_detail_cuts(
    skip: int = 0,
    limit: int = 100,
    request_detail_id: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Retrieve all request detail cuts with optional filtering.

    Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (pagination)
    - request_detail_id: Filter by request detail ID

    Returns:
    - List of request detail cut objects
    """
    cuts = crud.get_request_detail_cuts(
        db,
        request_detail_id=request_detail_id,
        skip=skip,
        limit=limit
    )
    return cuts

@router.get("/{cut_id}", response_model=schemas.RequestDetailCutRead, dependencies=[Depends(auth.require_viewer_role)])
def read_request_detail_cut(cut_id: int, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Retrieve a specific request detail cut by ID.

    Parameters:
    - cut_id: ID of the cut to retrieve

    Returns:
    - The cut object

    Raises:
    - 404 Not Found: If the cut with the specified ID does not exist
    """
    db_cut = crud.get_request_detail_cut(db, cut_id=cut_id)
    if db_cut is None:
        raise HTTPException(status_code=404, detail="Request detail cut not found")
    return db_cut

@router.post("/", response_model=schemas.RequestDetailCutRead, dependencies=[Depends(auth.require_editor_role)])
def create_request_detail_cut(cut: schemas.RequestDetailCutCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Create a new request detail cut.

    Parameters:
    - cut: Cut data to create

    Returns:
    - The created cut object
    """
    db_cut = crud.create_request_detail_cut(db, cut)
    return db_cut

@router.put("/{cut_id}", response_model=schemas.RequestDetailCutRead, dependencies=[Depends(auth.require_editor_role)])
def update_request_detail_cut(
    cut_id: int,
    cut: schemas.RequestDetailCutUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Update an existing request detail cut.

    Parameters:
    - cut_id: ID of the cut to update
    - cut: Updated cut data

    Returns:
    - The updated cut object

    Raises:
    - 404 Not Found: If the cut with the specified ID does not exist
    """
    db_cut = crud.update_request_detail_cut(db, cut_id, cut)
    if db_cut is None:
        raise HTTPException(status_code=404, detail="Request detail cut not found")
    return db_cut

@router.delete("/{cut_id}", response_model=schemas.RequestDetailCutRead, dependencies=[Depends(auth.require_admin_role)])
def delete_request_detail_cut(cut_id: int, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Delete a request detail cut.

    Parameters:
    - cut_id: ID of the cut to delete

    Returns:
    - The deleted cut object

    Raises:
    - 404 Not Found: If the cut with the specified ID does not exist
    """
    db_cut = crud.delete_request_detail_cut(db, cut_id)
    if db_cut is None:
        raise HTTPException(status_code=404, detail="Request detail cut not found")
    return db_cut

# --- Additional endpoint to get cuts by request detail ID ---
@router.get("/by-request-detail/{request_detail_id}", response_model=List[schemas.RequestDetailCutRead], dependencies=[Depends(auth.require_viewer_role)])
def get_cuts_by_request_detail(
    request_detail_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Get all cuts for a specific request detail.

    Parameters:
    - request_detail_id: ID of the request detail

    Returns:
    - List of cuts for the specified request detail
    """
    cuts = crud.get_request_detail_cuts(db, request_detail_id=request_detail_id)
    return cuts
