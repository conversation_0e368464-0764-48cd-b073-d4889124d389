"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RequestDetailCutFormData, initialRequestDetailCutFormData, CUT_SET_OPTIONS, CUT_STATUS_OPTIONS } from "@/types";

interface CutFormProps {
  initialData?: RequestDetailCutFormData;
  onSubmit: (data: RequestDetailCutFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function CutForm({ initialData, onSubmit, onCancel, isLoading = false }: CutFormProps) {
  const [formData, setFormData] = useState<RequestDetailCutFormData>(
    initialData || initialRequestDetailCutFormData
  );

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (field: keyof RequestDetailCutFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="set">Set</Label>
          <Select
            value={formData.set ?? ""}
            onValueChange={(value) => handleInputChange("set", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleziona set" />
            </SelectTrigger>
            <SelectContent>
              {CUT_SET_OPTIONS.map(option => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="direction">Direzione</Label>
          <Input
            id="direction"
            type="number"
            placeholder="0"
            value={formData.direction ?? ""}
            onChange={(e) => handleInputChange("direction", parseInt(e.target.value) || 0)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cutPrice">Prezzo Taglio (€)</Label>
          <Input
            id="cutPrice"
            type="number"
            step="0.01"
            placeholder="0.00"
            value={formData.cutPrice ?? ""}
            onChange={(e) => handleInputChange("cutPrice", parseFloat(e.target.value) || undefined)}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">Stato</Label>
        <Select
          value={formData.status ?? ""}
          onValueChange={(value) => handleInputChange("status", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Seleziona stato" />
          </SelectTrigger>
          <SelectContent>
            {CUT_STATUS_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Note</Label>
        <Textarea
          id="notes"
          placeholder="Inserisci note aggiuntive..."
          value={formData.notes ?? ""}
          onChange={(e) => handleInputChange("notes", e.target.value)}
          rows={3}
        />
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Annulla
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? "Salvando..." : "Salva"}
        </Button>
      </div>
    </form>
  );
}
