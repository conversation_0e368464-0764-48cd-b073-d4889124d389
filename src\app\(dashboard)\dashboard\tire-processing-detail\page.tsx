"use client";

import * as React from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { RequestInfoDisplay } from "@/components/tire-processing-detail/request-info-display";
import { TireForm } from "@/components/request-detail/tire-form";
import { ProcessingListSection } from "@/components/tire-processing-detail/processing-list-section";
import { ProcessingEditForm } from "@/components/tire-processing-detail/processing-edit-form";
import { DetailPageActions } from "@/components/request-detail/page-actions";
import { ProcessingSearchDialog } from "@/components/request-detail/processing-search-dialog"; // Import the dialog
import { useToast } from "@/hooks/use-toast";
import type { AppRequest, TireProcessingItem, TireProcessingFormData, TireFormData, DialogProcessing } from "@/types";
import { initialTireProcessingFormData } from "@/types";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { getRequest, getRequestTires } from "@/services/requestService";
import { getTireProcessing, updateTireProcessing } from "@/services/tireProcessingService";
import { updateRequestDetail } from "@/services/requestDetailService";
import type { RequestDetailUpdateData } from "@/services/requestDetailService";

export default function TireProcessingDetailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  const [, setCurrentTireId] = React.useState<string | null>(null);
  const [currentRequestId, setCurrentRequestId] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string | null>(null);

  const [appRequest, setAppRequest] = React.useState<AppRequest | null>(null);
  const [tireData, setTireData] = React.useState<TireFormData>({
    id: "",
    tugNo: "",
    projectN: "",
    tireSize: "",
    note: "",
    disposition: "AVAILABLE",
    quantity: 0,
    set: "1/4",
    direction: 0,
  });
  const [processings, setProcessings] = React.useState<TireProcessingItem[]>([]);
  const [selectedProcessingId, setSelectedProcessingId] = React.useState<string | null>(null);
  const [currentProcessingFormData, setCurrentProcessingFormData] = React.useState<TireProcessingFormData>(initialTireProcessingFormData);
  const [isProcessingSearchDialogOpen, setIsProcessingSearchDialogOpen] = React.useState<boolean>(false);
  const [isDeleteConfirmationOpen, setIsDeleteConfirmationOpen] = React.useState<boolean>(false);

  // State for tracking save operations
  const [isSaving, setIsSaving] = React.useState(false);
  const [modifiedProcessingIds, setModifiedProcessingIds] = React.useState<Set<string>>(new Set());
  const [isTireDataModified, setIsTireDataModified] = React.useState(false);

  // Function to map API tire data to TireFormData
  const mapApiTireToTireFormData = (apiTire: any): TireFormData => {
    return {
      id: apiTire.id,
      tugNo: apiTire.tug_number, // API returns snake_case
      projectN: apiTire.project_number, // API returns snake_case
      tireSize: apiTire.tire_size, // API returns snake_case
      note: apiTire.note,
      disposition: apiTire.disposition,
      quantity: apiTire.process_number, // API returns process_number for quantity
      set: "1/4", // Default value
      direction: 0, // Default value
    };
  };

  React.useEffect(() => {
    const tireIdFromParams = searchParams.get('tireId');
    const requestIdFromParams = searchParams.get('requestId');
    setCurrentTireId(tireIdFromParams);
    setCurrentRequestId(requestIdFromParams);

    if (!tireIdFromParams || !requestIdFromParams) {
      toast({
        title: "Missing Parameters",
        description: "Tire ID or Request ID not provided.",
        variant: "destructive"
      });
      setError("Missing required parameters");
      return;
    }

    setLoading(true);
    setError(null);

    const fetchData = async () => {
      try {
        // Load request and tire data
        const [reqRes, tiresRes] = await Promise.all([
          getRequest(requestIdFromParams),
          getRequestTires(requestIdFromParams),
        ]);

        // Set request data
        setAppRequest(reqRes as AppRequest);

        // Find the specific tire
        const tiresData = Array.isArray(tiresRes) ? tiresRes : [];
        const foundTire = tiresData.find((tire: any) => tire.id === tireIdFromParams);

        if (foundTire) {
          // Map the found tire to TireFormData
          const mappedTireData = mapApiTireToTireFormData(foundTire);
          setTireData(mappedTireData);

          // Fetch tire-specific processing data from the API
          try {
            const tireProcessingData = await getTireProcessing(tireIdFromParams);
            setProcessings(tireProcessingData);

            toast({
              title: "Data Loaded",
              description: `Loaded details for tire ${mappedTireData.tugNo} with ${tireProcessingData.length} processing operations`
            });
          } catch (processingError) {
            console.error("Error loading tire processing data:", processingError);
            setProcessings([]);
            toast({
              title: "Processing Data Warning",
              description: `Tire data loaded but processing data could not be retrieved: ${mappedTireData.tugNo}`,
              variant: "destructive"
            });
          }
        } else {
          // Tire not found
          setTireData({
            id: tireIdFromParams,
            tugNo: "N/A",
            projectN: "N/A",
            tireSize: "N/A",
            note: "Error loading tire - tire not found in request",
            disposition: "AVAILABLE",
            quantity: 0,
            set: "1/4",
            direction: 0,
          });
          setProcessings([]);
          toast({
            title: "Tire Not Found",
            description: `Tire ${tireIdFromParams} not found in request ${requestIdFromParams}`,
            variant: "destructive"
          });
        }
      } catch (err: any) {
        console.error("Error loading data:", err);
        setError("Failed to load tire and request data");
        setTireData({
          id: tireIdFromParams,
          tugNo: "N/A",
          projectN: "N/A",
          tireSize: "N/A",
          note: "Error loading tire - API call failed",
          disposition: "AVAILABLE",
          quantity: 0,
          set: "1/4",
          direction: 0,
        });
        setProcessings([]);
        toast({
          title: "Loading Error",
          description: "Failed to load tire data. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [searchParams, toast]);

  const handleTireInfoChange = (field: keyof TireFormData, value: any) => {
    setTireData(prev => ({ ...prev, [field]: value }));
    setIsTireDataModified(true);
  };

  const handleProcessingTableRowClick = (processingId: string) => {
    setSelectedProcessingId(processingId);
    const selectedProc = processings.find(p => p.id === processingId);
    if (selectedProc) {
      setCurrentProcessingFormData({
        id: selectedProc.id,
        description1: selectedProc.description1,
        description2: selectedProc.description2,
        tyreType: selectedProc.tyreType,
        n: selectedProc.n,
        picture: selectedProc.picture || false,
      });
    } else {
        setCurrentProcessingFormData(initialTireProcessingFormData); // Reset if not found
    }
  };

  const handleProcessingFormChange = (field: keyof TireProcessingFormData, value: any) => {
    // Only allow modification of the 'n' field since other fields are read-only
    if (field === 'n') {
      setCurrentProcessingFormData(prev => ({ ...prev, [field]: value }));
      if (selectedProcessingId) {
        setModifiedProcessingIds(prev => new Set(prev).add(selectedProcessingId));
      }
    }
  };

  const handleProcessingTableInputChange = (processingId: string, field: keyof TireProcessingItem, value: any) => {
    // Only allow modification of the 'n' field since other fields are read-only
    if (field === 'n') {
      setProcessings(prev =>
        prev.map(p => p.id === processingId ? { ...p, [field]: value } : p)
      );
      // Track that this processing item has been modified
      setModifiedProcessingIds(prev => new Set(prev).add(processingId));

      // If the currently edited form is for this processingId, update the form too
      if (selectedProcessingId === processingId && field in currentProcessingFormData) {
        setCurrentProcessingFormData(prev => ({...prev, [field as keyof TireProcessingFormData]: value}));
      }
    }
  };

  const handleProcessingAction = (action: "back" | "add" | "delete") => {
    switch(action) {
      case "back":
        if (currentRequestId) {
          router.push(`/dashboard/dettaglio?requestId=${currentRequestId}`);
        } else {
           router.push('/dashboard/dettaglio');
        }
        toast({ title: "Navigation", description: "Returning to request details." });
        break;
      case "add":
        setIsProcessingSearchDialogOpen(true);
        break;
      case "delete":
        if (!selectedProcessingId) {
            toast({ title: "Processing Action", description: "Please select a processing to delete.", variant: "destructive" });
            return;
        }
        setIsDeleteConfirmationOpen(true);
        break;
    }
  };

  const handleSaveProcessingForm = () => {
      if (!selectedProcessingId) {
          toast({title: "Save Error", description: "No processing selected to save.", variant: "destructive"});
          return;
      }
      setProcessings(prev => prev.map(p => {
          if (p.id === selectedProcessingId) {
              return {
                  ...p,
                  // Only update the 'n' field since other fields are read-only
                  n: currentProcessingFormData.n ?? p.n,
              };
          }
          return p;
      }));
      toast({title: "Processing Saved", description: `Processing details for ${selectedProcessingId} updated.`});
      setSelectedProcessingId(null); // Close form after save
      setCurrentProcessingFormData(initialTireProcessingFormData);
  };

  const handleCancelProcessingForm = () => {
    setSelectedProcessingId(null);
    setCurrentProcessingFormData(initialTireProcessingFormData);
    toast({title: "Edit Cancelled", description: "Processing edits cancelled."});
  };

  const handleHomeClick = () => {
    router.push('/dashboard');
    toast({ title: "Navigation", description: "Returning to Dashboard." });
  };

  const handleCopySendClick = () => {
    toast({ title: "Page Action", description: "Copy + Send action triggered." });
  };

  const handleSavePageClick = async () => {
    if (isSaving) return; // Prevent multiple simultaneous save operations

    setIsSaving(true);
    let saveErrors: string[] = [];
    let successCount = 0;

    try {
      // Save tire data if modified
      if (isTireDataModified && tireData.id) {
        try {
          const requestDetailUpdateData: RequestDetailUpdateData = {
            tug_no: tireData.tugNo,
            project_no: tireData.projectN,
            tire_size: tireData.tireSize,
            note: tireData.note,
            disposition: tireData.disposition,
            quantity: tireData.quantity,
          };

          await updateRequestDetail(tireData.id, requestDetailUpdateData);
          setIsTireDataModified(false);
          successCount++;
        } catch (error: any) {
          console.error("Error saving tire data:", error);
          saveErrors.push(`Tire data: ${error?.message || "Unknown error"}`);
        }
      }

      // Save modified processing data
      if (modifiedProcessingIds.size > 0) {
        const savePromises = Array.from(modifiedProcessingIds).map(async (processingId) => {
          const processing = processings.find(p => p.id === processingId);
          if (!processing) return;

          try {
            // Extract the numeric ID from the prefixed ID (e.g., "CP-123" -> 123)
            const numericId = parseInt(processing.id.replace(/^CP-/, ''));
            if (isNaN(numericId)) {
              throw new Error(`Invalid processing ID format: ${processing.id}`);
            }

            await updateTireProcessing(numericId, processing.n, undefined);
            successCount++;
          } catch (error: any) {
            console.error(`Error saving processing ${processingId}:`, error);
            saveErrors.push(`Processing ${processingId}: ${error?.message || "Unknown error"}`);
          }
        });

        await Promise.allSettled(savePromises);
        setModifiedProcessingIds(new Set()); // Clear modified tracking
      }

      // Refresh processing data from server after successful save
      if (successCount > 0 && tireData.id) {
        try {
          const refreshedProcessingData = await getTireProcessing(tireData.id);
          setProcessings(refreshedProcessingData);
        } catch (refreshError) {
          console.error("Error refreshing processing data after save:", refreshError);
          // Don't show error to user as the save was successful, just log it
        }
      }

      // Show appropriate feedback
      if (saveErrors.length === 0) {
        if (successCount > 0) {
          toast({
            title: "Save Successful",
            description: `Successfully saved ${successCount} item(s).`,
            variant: "default"
          });
        } else {
          toast({
            title: "No Changes",
            description: "No changes detected to save.",
            variant: "default"
          });
        }
      } else {
        toast({
          title: "Save Completed with Errors",
          description: `Saved ${successCount} item(s). Errors: ${saveErrors.join("; ")}`,
          variant: "destructive"
        });
      }

    } catch (error: any) {
      console.error("Unexpected error during save operation:", error);
      toast({
        title: "Save Failed",
        description: error?.message || "An unexpected error occurred during save.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveTireForm = () => {
    // Save tire form data
    toast({ title: "Tire Info Saved", description: "Tire information has been saved." });
  };

  const handleCancelTireForm = () => {
    // Cancel tire form edits
    toast({ title: "Tire Edit Cancelled", description: "Tire information edits cancelled." });
  };

  const handleConfirmDelete = () => {
    if (!selectedProcessingId) return;

    setProcessings(prev => prev.filter(p => p.id !== selectedProcessingId));
    setSelectedProcessingId(null);
    setCurrentProcessingFormData(initialTireProcessingFormData);
    toast({ title: "Processing Action", description: `Processing ${selectedProcessingId} deleted.` });
    setIsDeleteConfirmationOpen(false);
  };

  const handleCloseProcessingSearchDialog = () => {
    setIsProcessingSearchDialogOpen(false);
  };

  const handleAddProcessingsFromDialog = (selectedDialogProcessings: DialogProcessing[]) => {
    const newTireProcessings: TireProcessingItem[] = selectedDialogProcessings.map((dialogProc, index) => ({
      id: `DLGPROC-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 7)}`, // Ensure unique ID
      description1: dialogProc.description1,
      description2: dialogProc.description2,
      price: dialogProc.cost.replace(' €', '').trim(), // Assuming cost is "XX.YY €"
      tyreType: dialogProc.tireType,
      code1: dialogProc.testCode1,
      code2: dialogProc.testCode2,
      n: 1, // Default N for newly added processing
      picture: false, // Default picture status
    }));

    setProcessings(prev => [...prev, ...newTireProcessings]);
    toast({ title: "Processings Added", description: `${newTireProcessings.length} processing types were added from the search dialog.`});
    setIsProcessingSearchDialogOpen(false); // Close dialog after adding
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="text-center text-gray-500">Loading tire processing details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="text-center text-red-500">{error}</div>
        <DetailPageActions
          onHomeClick={handleHomeClick}
          onCopySendClick={handleCopySendClick}
          onSaveClick={handleSavePageClick}
          isSaving={isSaving}
        />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {appRequest && <RequestInfoDisplay request={appRequest} />}
      <TireForm
          formData={tireData}
          onFormChange={handleTireInfoChange}
          onSave={handleSaveTireForm}
          onCancel={handleCancelTireForm}
          isNewTire={false}
      />
      <ProcessingListSection
        processings={processings}
        selectedProcessingId={selectedProcessingId}
        onRowClick={handleProcessingTableRowClick}
        onAction={handleProcessingAction}
        onTableInputChange={handleProcessingTableInputChange}
      />
      {selectedProcessingId && (
        <ProcessingEditForm
          formData={currentProcessingFormData}
          onFormChange={handleProcessingFormChange}
          onSave={handleSaveProcessingForm}
          onCancel={handleCancelProcessingForm}
        />
      )}
      <DetailPageActions
        onHomeClick={handleHomeClick}
        onCopySendClick={handleCopySendClick}
        onSaveClick={handleSavePageClick}
        isSaving={isSaving}
      />
      <ProcessingSearchDialog
        isOpen={isProcessingSearchDialogOpen}
        onClose={handleCloseProcessingSearchDialog}
        onAddSelectedProcessing={handleAddProcessingsFromDialog}
      />
      <DeleteConfirmationDialog
        isOpen={isDeleteConfirmationOpen}
        onClose={() => setIsDeleteConfirmationOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Confirm Processing Deletion"
        description={`Are you sure you want to delete this processing? This action cannot be undone.`}
        itemType="processing"
      />
    </div>
  );
}
