#!/usr/bin/env python3
"""
Test authentication system directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database import SessionLocal
from backend import crud, auth
import bcrypt

def test_auth_system():
    """Test authentication system directly"""
    print("🔐 Testing Authentication System")
    print("=" * 40)
    
    db = SessionLocal()
    
    try:
        # Check users in database
        print("\n👥 Users in database:")
        users = crud.get_users(db)
        for user in users:
            print(f"   - {user.email} ({user.role}) - Active: {user.is_active}")
        
        # Test password verification
        print("\n🔑 Testing password verification:")
        test_email = "<EMAIL>"
        test_password = "admin123"
        
        user = crud.get_user_by_email(db, test_email)
        if user:
            print(f"   User found: {user.email}")
            print(f"   Stored hash: {user.hashed_password[:50]}...")
            
            # Test with bcrypt directly
            is_valid_bcrypt = bcrypt.checkpw(test_password.encode('utf-8'), user.hashed_password.encode('utf-8'))
            print(f"   Bcrypt verification: {is_valid_bcrypt}")
            
            # Test with auth module
            is_valid_auth = auth.verify_password(test_password, user.hashed_password)
            print(f"   Auth module verification: {is_valid_auth}")
            
            # Test authentication
            authenticated_user = crud.authenticate_user(db, test_email, test_password)
            if authenticated_user:
                print(f"   ✅ Authentication successful: {authenticated_user.email}")
                
                # Test token creation
                token = auth.create_access_token(data={"sub": authenticated_user.email})
                print(f"   🎫 Token created: {token[:50]}...")
                
                # Test token verification
                try:
                    payload = auth.verify_token(token)
                    print(f"   ✅ Token verification successful: {payload}")
                except Exception as e:
                    print(f"   ❌ Token verification failed: {e}")
                
            else:
                print(f"   ❌ Authentication failed")
        else:
            print(f"   ❌ User not found: {test_email}")
        
        # Test creating a new user with correct password
        print("\n👤 Creating test user:")
        test_user_email = "<EMAIL>"
        test_user_password = "test123"
        
        # Check if user already exists
        existing_user = crud.get_user_by_email(db, test_user_email)
        if existing_user:
            print(f"   User already exists: {test_user_email}")
        else:
            # Create new user
            from backend.schemas import UserCreate
            user_data = UserCreate(
                email=test_user_email,
                password=test_user_password,
                full_name="Test User",
                role="viewer"
            )
            
            new_user = crud.create_user(db, user_data)
            print(f"   ✅ Created user: {new_user.email}")
            
            # Test authentication with new user
            auth_user = crud.authenticate_user(db, test_user_email, test_user_password)
            if auth_user:
                print(f"   ✅ New user authentication successful")
            else:
                print(f"   ❌ New user authentication failed")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    test_auth_system()
