{"memoryBank": {"name": "CutRequestStudio Memory Bank", "version": "1.0.0", "created": "2025-05-28T13:52:00Z", "lastUpdated": "2025-05-28T13:52:00Z", "description": "Comprehensive knowledge repository for CutRequestStudio tire request management system", "project": {"name": "CutRequestStudio", "type": "Full-stack Web Application", "frontend": "Next.js 15.2.3 + React 18 + TypeScript", "backend": "FastAPI + SQLAlchemy + SQLite/PostgreSQL", "architecture": "Modern full-stack with optimization patterns"}, "categories": {"technical": {"description": "Technical architecture, patterns, and implementation details", "subcategories": ["architecture", "api", "frontend", "backend", "performance"]}, "development": {"description": "Development history, decisions, and evolution", "subcategories": ["history", "decisions", "migrations", "lessons-learned"]}, "business": {"description": "Business domain knowledge and workflows", "subcategories": ["domain", "workflows", "rules", "standards"]}, "operational": {"description": "Operational procedures and guidelines", "subcategories": ["development", "deployment", "maintenance", "troubleshooting"]}}, "statistics": {"totalDocuments": 0, "totalCrossReferences": 0, "lastIndexUpdate": "2025-05-28T13:52:00Z", "knowledgeCoverage": 0}}}