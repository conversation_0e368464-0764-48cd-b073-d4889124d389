#!/usr/bin/env python3
"""
Database Migration - Phase 1: Create New Simplified Tables

This script creates the new simplified tables alongside the existing ones:
- Enhanced 'tires' table (renamed from 'tire')
- New 'request_items' table (replacement for 'request_details')
- New 'cut_operations' table (replacement for 'REQUEST_DETAIL_CUT' + 'cut_processing')

The old tables remain intact for data migration in Phase 2.
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add the parent directory to the Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database import Base, engine, SessionLocal
import backend.models as models

def backup_database():
    """Create a backup of the current database"""
    db_path = "app.db"
    backup_path = f"app_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

    if os.path.exists(db_path):
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    else:
        print("⚠️  No existing database found to backup")
        return None

def create_new_tables():
    """Create only the new simplified tables"""
    print("🔧 Creating new simplified tables...")

    # Get database connection
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Create enhanced tires table (renamed from tire)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tires (
                id VARCHAR PRIMARY KEY,
                tug_no VARCHAR UNIQUE NOT NULL,
                spec_no VARCHAR NOT NULL,
                size VARCHAR NOT NULL,
                owner VARCHAR NOT NULL,
                load_index VARCHAR NOT NULL,
                pattern VARCHAR NOT NULL,
                project_no VARCHAR NOT NULL,
                location VARCHAR NOT NULL,
                description VARCHAR,
                is_active INTEGER DEFAULT 1
            )
        ''')

        # Create indexes for tires table
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tires_tug_no ON tires(tug_no)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tires_size ON tires(size)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tires_owner ON tires(owner)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tires_pattern ON tires(pattern)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tires_project_no ON tires(project_no)')

        # Create request_items table (replacement for request_details)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS request_items (
                id VARCHAR PRIMARY KEY,
                request_id VARCHAR NOT NULL,
                tire_id VARCHAR NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 1,
                disposition VARCHAR NOT NULL,
                notes VARCHAR,
                unit_price REAL,
                section VARCHAR,
                FOREIGN KEY (request_id) REFERENCES requests(id),
                FOREIGN KEY (tire_id) REFERENCES tires(id)
            )
        ''')

        # Create indexes for request_items table
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_request_items_request_id ON request_items(request_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_request_items_tire_id ON request_items(tire_id)')

        # Create cut_operations table (replacement for REQUEST_DETAIL_CUT + cut_processing)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cut_operations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_item_id VARCHAR NOT NULL,
                processing_id VARCHAR NOT NULL,
                quantity INTEGER DEFAULT 1,
                cut_price REAL,
                status VARCHAR,
                notes VARCHAR,
                created_date DATETIME,
                FOREIGN KEY (request_item_id) REFERENCES request_items(id),
                FOREIGN KEY (processing_id) REFERENCES processing(id)
            )
        ''')

        # Create indexes for cut_operations table
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_cut_operations_request_item_id ON cut_operations(request_item_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_cut_operations_processing_id ON cut_operations(processing_id)')

        conn.commit()
        print("✅ New simplified tables created successfully!")

        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        print("\n📋 Current database tables:")
        for table in tables:
            print(f"   - {table[0]}")

    except Exception as e:
        conn.rollback()
        print(f"❌ Error creating tables: {e}")
        raise
    finally:
        conn.close()

def verify_migration():
    """Verify that the new tables were created correctly"""
    print("\n🔍 Verifying migration...")

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Check if new tables exist
        new_tables = ['tires', 'request_items', 'cut_operations']
        for table in new_tables:
            cursor.execute(f"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='{table}'")
            count = cursor.fetchone()[0]
            if count == 1:
                print(f"   ✅ Table '{table}' created successfully")
            else:
                print(f"   ❌ Table '{table}' not found")

        # Check table schemas
        print("\n📊 New table schemas:")
        for table in new_tables:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"\n   {table}:")
            for col in columns:
                print(f"     - {col[1]} ({col[2]})")

    except Exception as e:
        print(f"❌ Error during verification: {e}")
    finally:
        conn.close()

def main():
    """Main migration function"""
    print("🚀 Starting Database Migration - Phase 1")
    print("=" * 50)

    # Step 1: Backup current database
    backup_path = backup_database()

    # Step 2: Create new simplified tables
    create_new_tables()

    # Step 3: Verify migration
    verify_migration()

    print("\n" + "=" * 50)
    print("✅ Phase 1 Migration completed successfully!")
    print("\nNext steps:")
    print("1. Run Phase 2 migration to transfer data")
    print("2. Update API endpoints to use new models")
    print("3. Run Phase 3 migration to remove old tables")

    if backup_path:
        print(f"\n💾 Database backup available at: {backup_path}")

if __name__ == "__main__":
    main()
