/**
 * ERROR HANDLING MIDDLEWARE - PRIORITÀ 3
 *
 * Consolida i pattern di gestione errori duplicati presenti in tutti i services e componenti.
 *
 * Caratteristiche:
 * - Middleware centralizzato per gestione errori API
 * - Integrazione con Generic CRUD Service di PRIORITÀ 1
 * - Toast notifications automatiche
 * - Logging strutturato degli errori
 * - Retry logic configurabile
 * - Error boundaries per React components
 * - Mapping errori backend → frontend user-friendly
 */

import React from 'react';
import { useToast } from '@/hooks/use-toast';
import { AxiosError, AxiosResponse } from 'axios';

// ============================================================================
// CORE ERROR TYPES
// ============================================================================

export interface ErrorContext {
  /** Operation being performed when error occurred */
  operation: string;
  /** Component or service where error occurred */
  source: string;
  /** User ID if available */
  userId?: string;
  /** Additional context data */
  metadata?: Record<string, any>;
  /** Timestamp of error */
  timestamp: Date;
}

export interface ErrorDetails {
  /** Error code */
  code: string;
  /** User-friendly message */
  message: string;
  /** Technical details for developers */
  details?: string;
  /** Suggested actions for user */
  suggestions?: string[];
  /** Whether error is recoverable */
  recoverable: boolean;
  /** Retry configuration if applicable */
  retryConfig?: RetryConfig;
}

export interface RetryConfig {
  /** Maximum number of retry attempts */
  maxAttempts: number;
  /** Delay between retries in milliseconds */
  delay: number;
  /** Exponential backoff multiplier */
  backoffMultiplier?: number;
  /** Maximum delay between retries */
  maxDelay?: number;
  /** Function to determine if error should be retried */
  shouldRetry?: (error: any, attempt: number) => boolean;
}

export interface ErrorHandlerOptions {
  /** Show toast notification */
  showToast?: boolean;
  /** Custom toast message */
  toastMessage?: string;
  /** Toast variant */
  toastVariant?: 'default' | 'destructive';
  /** Log error to console/service */
  logError?: boolean;
  /** Retry configuration */
  retry?: RetryConfig;
  /** Custom error mapping */
  errorMapping?: (error: any) => ErrorDetails;
  /** Fallback error message */
  fallbackMessage?: string;
  /** Include error details in user message */
  includeDetails?: boolean;
}

// ============================================================================
// ERROR CLASSIFICATION
// ============================================================================

export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER_ERROR = 'SERVER_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// ============================================================================
// ERROR MAPPING UTILITIES
// ============================================================================

/**
 * Maps backend errors to user-friendly frontend errors
 */
class ErrorMapper {

  private static readonly ERROR_MAPPINGS: Record<string, ErrorDetails> = {
    // Network Errors
    'NETWORK_ERROR': {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed. Please check your internet connection.',
      suggestions: ['Check your internet connection', 'Try again in a few moments'],
      recoverable: true,
      retryConfig: { maxAttempts: 3, delay: 1000, backoffMultiplier: 2 }
    },
    'TIMEOUT': {
      code: 'TIMEOUT',
      message: 'Request timed out. The server is taking too long to respond.',
      suggestions: ['Try again', 'Check your internet connection'],
      recoverable: true,
      retryConfig: { maxAttempts: 2, delay: 2000 }
    },

    // Authentication Errors
    'UNAUTHORIZED': {
      code: 'UNAUTHORIZED',
      message: 'You are not authorized to perform this action. Please log in again.',
      suggestions: ['Log in again', 'Contact administrator if problem persists'],
      recoverable: true
    },
    'TOKEN_EXPIRED': {
      code: 'TOKEN_EXPIRED',
      message: 'Your session has expired. Please log in again.',
      suggestions: ['Log in again'],
      recoverable: true
    },

    // Validation Errors
    'VALIDATION_ERROR': {
      code: 'VALIDATION_ERROR',
      message: 'Please check the form data and try again.',
      suggestions: ['Review the highlighted fields', 'Ensure all required fields are filled'],
      recoverable: true
    },
    'DUPLICATE_ENTRY': {
      code: 'DUPLICATE_ENTRY',
      message: 'This entry already exists. Please use different values.',
      suggestions: ['Use different values', 'Check existing entries'],
      recoverable: true
    },

    // Not Found Errors
    'NOT_FOUND': {
      code: 'NOT_FOUND',
      message: 'The requested resource was not found.',
      suggestions: ['Check if the item still exists', 'Refresh the page'],
      recoverable: false
    },

    // Server Errors
    'INTERNAL_SERVER_ERROR': {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An internal server error occurred. Please try again later.',
      suggestions: ['Try again in a few minutes', 'Contact support if problem persists'],
      recoverable: true,
      retryConfig: { maxAttempts: 2, delay: 5000 }
    },
    'SERVICE_UNAVAILABLE': {
      code: 'SERVICE_UNAVAILABLE',
      message: 'Service is temporarily unavailable. Please try again later.',
      suggestions: ['Try again in a few minutes'],
      recoverable: true,
      retryConfig: { maxAttempts: 3, delay: 10000 }
    }
  };

  /**
   * Maps HTTP status codes to error types
   */
  private static readonly STATUS_CODE_MAPPINGS: Record<number, ErrorType> = {
    400: ErrorType.VALIDATION,
    401: ErrorType.AUTHENTICATION,
    403: ErrorType.AUTHORIZATION,
    404: ErrorType.NOT_FOUND,
    408: ErrorType.TIMEOUT,
    422: ErrorType.VALIDATION,
    429: ErrorType.CLIENT_ERROR,
    500: ErrorType.SERVER_ERROR,
    502: ErrorType.SERVER_ERROR,
    503: ErrorType.SERVER_ERROR,
    504: ErrorType.TIMEOUT
  };

  /**
   * Maps error to standardized error details
   */
  static mapError(error: any, context?: ErrorContext): ErrorDetails {
    // Handle Axios errors
    if (error.isAxiosError) {
      return this.mapAxiosError(error as AxiosError, context);
    }

    // Handle custom application errors
    if (error.code && this.ERROR_MAPPINGS[error.code]) {
      return this.ERROR_MAPPINGS[error.code];
    }

    // Handle validation errors
    if (error.name === 'ValidationError' || error.errors) {
      return {
        code: 'VALIDATION_ERROR',
        message: 'Please check the form data and try again.',
        details: error.message || 'Validation failed',
        recoverable: true
      };
    }

    // Default unknown error
    return {
      code: 'UNKNOWN',
      message: 'An unexpected error occurred. Please try again.',
      details: error.message || 'Unknown error',
      suggestions: ['Try again', 'Refresh the page', 'Contact support if problem persists'],
      recoverable: true
    };
  }

  /**
   * Maps Axios errors to error details
   */
  private static mapAxiosError(error: AxiosError, context?: ErrorContext): ErrorDetails {
    const status = error.response?.status;
    const data = error.response?.data as any;

    // Network error (no response)
    if (!error.response) {
      return this.ERROR_MAPPINGS['NETWORK_ERROR'];
    }

    // Timeout error
    if (error.code === 'ECONNABORTED') {
      return this.ERROR_MAPPINGS['TIMEOUT'];
    }

    // Map by status code
    if (status) {
      const errorType = this.STATUS_CODE_MAPPINGS[status];

      switch (errorType) {
        case ErrorType.AUTHENTICATION:
          return this.ERROR_MAPPINGS['UNAUTHORIZED'];
        case ErrorType.AUTHORIZATION:
          return this.ERROR_MAPPINGS['UNAUTHORIZED'];
        case ErrorType.NOT_FOUND:
          return this.ERROR_MAPPINGS['NOT_FOUND'];
        case ErrorType.VALIDATION:
          return {
            ...this.ERROR_MAPPINGS['VALIDATION_ERROR'],
            details: data?.message || data?.detail || error.message
          };
        case ErrorType.SERVER_ERROR:
          return this.ERROR_MAPPINGS['INTERNAL_SERVER_ERROR'];
        case ErrorType.TIMEOUT:
          return this.ERROR_MAPPINGS['TIMEOUT'];
      }
    }

    // Extract backend error message if available
    const backendMessage = data?.message || data?.detail || data?.error;

    return {
      code: `HTTP_${status || 'UNKNOWN'}`,
      message: backendMessage || 'An error occurred while processing your request.',
      details: error.message,
      recoverable: status ? status < 500 : false
    };
  }

  /**
   * Determines error severity based on error details
   */
  static getErrorSeverity(error: ErrorDetails, context?: ErrorContext): ErrorSeverity {
    switch (error.code) {
      case 'NETWORK_ERROR':
      case 'TIMEOUT':
        return ErrorSeverity.MEDIUM;

      case 'UNAUTHORIZED':
      case 'TOKEN_EXPIRED':
        return ErrorSeverity.HIGH;

      case 'INTERNAL_SERVER_ERROR':
      case 'SERVICE_UNAVAILABLE':
        return ErrorSeverity.HIGH;

      case 'VALIDATION_ERROR':
      case 'DUPLICATE_ENTRY':
        return ErrorSeverity.LOW;

      case 'NOT_FOUND':
        return ErrorSeverity.MEDIUM;

      default:
        return ErrorSeverity.MEDIUM;
    }
  }
}

// ============================================================================
// ERROR LOGGER
// ============================================================================

/**
 * Structured error logging service
 */
class ErrorLogger {

  private static readonly LOG_LEVELS = {
    [ErrorSeverity.LOW]: 'info',
    [ErrorSeverity.MEDIUM]: 'warn',
    [ErrorSeverity.HIGH]: 'error',
    [ErrorSeverity.CRITICAL]: 'error'
  } as const;

  /**
   * Logs error with structured data
   */
  static logError(
    error: any,
    errorDetails: ErrorDetails,
    context?: ErrorContext,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
  ): void {
    const logLevel = this.LOG_LEVELS[severity];

    const logData = {
      timestamp: new Date().toISOString(),
      severity,
      code: errorDetails.code,
      message: errorDetails.message,
      details: errorDetails.details,
      context: context ? {
        operation: context.operation,
        source: context.source,
        userId: context.userId,
        metadata: context.metadata
      } : undefined,
      stack: error.stack,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined
    };

    // Console logging
    console[logLevel](`[${severity}] ${errorDetails.code}: ${errorDetails.message}`, logData);

    // In production, send to external logging service
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalLogger(logData, severity);
    }
  }

  /**
   * Sends error to external logging service (placeholder)
   */
  private static sendToExternalLogger(logData: any, severity: ErrorSeverity): void {
    // Implementation would depend on chosen logging service
    // Examples: Sentry, LogRocket, DataDog, etc.

    // For now, just store in localStorage for debugging
    try {
      const existingLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
      existingLogs.push(logData);

      // Keep only last 100 errors
      if (existingLogs.length > 100) {
        existingLogs.splice(0, existingLogs.length - 100);
      }

      localStorage.setItem('errorLogs', JSON.stringify(existingLogs));
    } catch (e) {
      console.warn('Failed to store error log:', e);
    }
  }
}

// ============================================================================
// RETRY LOGIC
// ============================================================================

/**
 * Handles retry logic for failed operations
 */
class RetryHandler {

  /**
   * Executes operation with retry logic
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    retryConfig: RetryConfig,
    context?: ErrorContext
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Check if we should retry
        if (attempt === retryConfig.maxAttempts ||
            (retryConfig.shouldRetry && !retryConfig.shouldRetry(error, attempt))) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = this.calculateDelay(retryConfig, attempt);

        console.warn(`Operation failed (attempt ${attempt}/${retryConfig.maxAttempts}), retrying in ${delay}ms...`, {
          error: (error as Error).message || 'Unknown error',
          context
        });

        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Calculates delay for retry with exponential backoff
   */
  private static calculateDelay(config: RetryConfig, attempt: number): number {
    let delay = config.delay;

    if (config.backoffMultiplier && attempt > 1) {
      delay = config.delay * Math.pow(config.backoffMultiplier, attempt - 1);
    }

    if (config.maxDelay) {
      delay = Math.min(delay, config.maxDelay);
    }

    return delay;
  }

  /**
   * Sleep utility
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// ============================================================================
// MAIN ERROR HANDLER
// ============================================================================

/**
 * Main error handling class that consolidates all error handling patterns
 */
export class ErrorHandler {

  /**
   * Handles API call with comprehensive error handling
   */
  static async handleApiCall<T>(
    apiCall: () => Promise<T>,
    options: ErrorHandlerOptions = {},
    context?: ErrorContext
  ): Promise<T> {
    const {
      showToast = true,
      logError = true,
      retry,
      errorMapping,
      fallbackMessage = 'An unexpected error occurred',
      includeDetails = false
    } = options;

    try {
      // Execute with retry if configured
      if (retry) {
        return await RetryHandler.executeWithRetry(apiCall, retry, context);
      } else {
        return await apiCall();
      }

    } catch (error) {
      // Map error to standardized format
      const errorDetails = errorMapping ?
        errorMapping(error) :
        ErrorMapper.mapError(error, context);

      // Determine severity
      const severity = ErrorMapper.getErrorSeverity(errorDetails, context);

      // Log error
      if (logError) {
        ErrorLogger.logError(error, errorDetails, context, severity);
      }

      // Show toast notification
      if (showToast && typeof window !== 'undefined') {
        this.showErrorToast(errorDetails, options);
      }

      // Re-throw with enhanced error information
      const enhancedError = new Error(errorDetails.message);
      (enhancedError as any).code = errorDetails.code;
      (enhancedError as any).details = errorDetails.details;
      (enhancedError as any).recoverable = errorDetails.recoverable;
      (enhancedError as any).suggestions = errorDetails.suggestions;
      (enhancedError as any).originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Shows error toast notification
   */
  private static showErrorToast(errorDetails: ErrorDetails, options: ErrorHandlerOptions): void {
    // This needs to be called from a React component context
    // We'll provide a hook for this
    const message = options.toastMessage || errorDetails.message;
    const variant = options.toastVariant || 'destructive';

    // Store toast data for hook to pick up
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('errorToast', {
        detail: { message, variant, suggestions: errorDetails.suggestions }
      }));
    }
  }

  /**
   * Creates error handler for specific operation
   */
  static createOperationHandler(
    operation: string,
    source: string,
    defaultOptions: ErrorHandlerOptions = {}
  ) {
    return async <T>(
      apiCall: () => Promise<T>,
      options: ErrorHandlerOptions = {}
    ): Promise<T> => {
      const context: ErrorContext = {
        operation,
        source,
        timestamp: new Date()
      };

      const mergedOptions = { ...defaultOptions, ...options };

      return this.handleApiCall(apiCall, mergedOptions, context);
    };
  }

  /**
   * Handles form submission errors specifically
   */
  static async handleFormSubmission<T>(
    submitFunction: () => Promise<T>,
    options: ErrorHandlerOptions = {}
  ): Promise<T> {
    const defaultOptions: ErrorHandlerOptions = {
      showToast: true,
      logError: true,
      fallbackMessage: 'Failed to submit form. Please check your data and try again.',
      errorMapping: (error) => {
        // Custom mapping for form errors
        if (error.response?.status === 422) {
          return {
            code: 'FORM_VALIDATION_ERROR',
            message: 'Please check the highlighted fields and try again.',
            details: error.response.data?.message,
            recoverable: true
          };
        }
        return ErrorMapper.mapError(error);
      }
    };

    return this.handleApiCall(
      submitFunction,
      { ...defaultOptions, ...options },
      {
        operation: 'form_submission',
        source: 'form_handler',
        timestamp: new Date()
      }
    );
  }

  /**
   * Gets error logs for debugging
   */
  static getErrorLogs(): any[] {
    try {
      return JSON.parse(localStorage.getItem('errorLogs') || '[]');
    } catch (e) {
      return [];
    }
  }

  /**
   * Clears error logs
   */
  static clearErrorLogs(): void {
    localStorage.removeItem('errorLogs');
  }
}

// ============================================================================
// REACT HOOKS
// ============================================================================

/**
 * Hook for handling errors in React components
 */
export function useErrorHandler() {
  const { toast } = useToast();

  // Listen for error toast events
  React.useEffect(() => {
    const handleErrorToast = (event: CustomEvent) => {
      const { message, variant, suggestions } = event.detail;

      toast({
        title: "Error",
        description: message,
        variant: variant || 'destructive'
      });
    };

    window.addEventListener('errorToast', handleErrorToast as EventListener);

    return () => {
      window.removeEventListener('errorToast', handleErrorToast as EventListener);
    };
  }, [toast]);

  return {
    handleError: (error: any, options?: ErrorHandlerOptions) => {
      const errorDetails = ErrorMapper.mapError(error);
      const severity = ErrorMapper.getErrorSeverity(errorDetails);

      ErrorLogger.logError(error, errorDetails, undefined, severity);

      toast({
        title: "Error",
        description: options?.toastMessage || errorDetails.message,
        variant: options?.toastVariant || 'destructive'
      });
    },

    handleApiCall: <T>(
      apiCall: () => Promise<T>,
      options?: ErrorHandlerOptions
    ) => ErrorHandler.handleApiCall(apiCall, options)
  };
}

// ============================================================================
// ERROR BOUNDARY UTILITIES
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Error Boundary utilities for React components
 * Note: Actual JSX implementation should be in a .tsx file
 */
export class ErrorBoundaryUtils {

  /**
   * Creates error boundary state from error
   */
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  /**
   * Handles component error logging
   */
  static handleComponentError(
    error: Error,
    errorInfo: React.ErrorInfo,
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  ): void {
    // Log error
    const errorDetails = ErrorMapper.mapError(error);
    const severity = ErrorMapper.getErrorSeverity(errorDetails);

    ErrorLogger.logError(error, errorDetails, {
      operation: 'component_render',
      source: 'error_boundary',
      timestamp: new Date(),
      metadata: { componentStack: errorInfo.componentStack }
    }, severity);

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo);
    }
  }

  /**
   * Creates default error fallback content
   */
  static createDefaultErrorFallback(error: Error, retry: () => void): string {
    return `
      <div class="error-boundary p-4 border border-red-200 rounded-lg bg-red-50">
        <h2 class="text-lg font-semibold text-red-800 mb-2">Something went wrong</h2>
        <p class="text-red-600 mb-4">
          An unexpected error occurred. Please try refreshing the page.
        </p>
        <button
          onclick="retry()"
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    `;
  }
}

// ============================================================================
// PRESET CONFIGURATIONS
// ============================================================================

/**
 * Preset error handling configurations for common scenarios
 */
export const ErrorHandlerPresets = {

  /**
   * Standard API call error handling
   */
  STANDARD_API: {
    showToast: true,
    logError: true,
    includeDetails: false
  } as ErrorHandlerOptions,

  /**
   * Silent error handling (no toast, only logging)
   */
  SILENT: {
    showToast: false,
    logError: true,
    includeDetails: false
  } as ErrorHandlerOptions,

  /**
   * Form submission error handling
   */
  FORM_SUBMISSION: {
    showToast: true,
    logError: true,
    includeDetails: true,
    fallbackMessage: 'Failed to submit form. Please check your data and try again.'
  } as ErrorHandlerOptions,

  /**
   * Critical operation error handling with retry
   */
  CRITICAL_WITH_RETRY: {
    showToast: true,
    logError: true,
    includeDetails: false,
    retry: {
      maxAttempts: 3,
      delay: 1000,
      backoffMultiplier: 2,
      maxDelay: 10000
    }
  } as ErrorHandlerOptions,

  /**
   * Background operation error handling
   */
  BACKGROUND: {
    showToast: false,
    logError: true,
    includeDetails: false
  } as ErrorHandlerOptions
};
