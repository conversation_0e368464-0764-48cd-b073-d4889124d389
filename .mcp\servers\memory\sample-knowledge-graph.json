{"entities": [{"name": "CutRequestStudio", "entityType": "software_project", "observations": ["Tire request management application", "Built with React and Next.js framework", "Uses TypeScript for type safety", "Has FastAPI backend with SQLite database", "Manages tire processing workflows", "Supports multiple user roles and departments", "Includes tire search and management features"]}, {"name": "<PERSON><PERSON>", "entityType": "person", "observations": ["Lead developer on CutRequestStudio project", "Uses Windows 11 development environment", "Working with MCP servers integration", "Experienced with React and TypeScript", "Focuses on tire management functionality"]}, {"name": "Tire_Management_System", "entityType": "feature", "observations": ["Core feature of CutRequestStudio", "Allows searching and filtering tires", "Supports tire request processing", "Includes tire specification management", "Recently had PIPPO button removed"]}, {"name": "Production_Department", "entityType": "department", "observations": ["Primary user of tire processing features", "Handles tire cut requests", "Manages equipment and processing workflows", "Requires detailed tire specifications"]}, {"name": "MCP_Memory_Server", "entityType": "tool", "observations": ["Knowledge graph memory system", "Provides persistent memory across sessions", "Supports entity and relationship management", "Enables intelligent context awareness", "Installed via NPX package manager"]}], "relations": [{"from": "<PERSON><PERSON>", "to": "CutRequestStudio", "relationType": "develops"}, {"from": "CutRequestStudio", "to": "Tire_Management_System", "relationType": "includes"}, {"from": "Production_Department", "to": "Tire_Management_System", "relationType": "uses"}, {"from": "CutRequestStudio", "to": "MCP_Memory_Server", "relationType": "integrates_with"}, {"from": "MCP_Memory_Server", "to": "<PERSON><PERSON>", "relationType": "assists"}], "metadata": {"created": "2025-05-28T13:27:00.000Z", "description": "Sample knowledge graph demonstrating Memory MCP Server capabilities for CutRequestStudio", "version": "1.0.0", "use_cases": ["User preference tracking", "Workflow optimization", "Knowledge retention", "Context awareness", "Relationship mapping"]}}