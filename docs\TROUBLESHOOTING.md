# CutRequestStudio Troubleshooting Guide

This guide helps resolve common issues encountered while using or developing CutRequestStudio.

## Quick Diagnostics

### Health Check
First, verify that both services are running:

```bash
# Check backend health
curl http://localhost:8000/health

# Expected response: {"status": "ok"}

# Check frontend
curl http://localhost:9002

# Should return HTML content
```

### Service Status
```bash
# Check if services are running
netstat -an | grep :8000  # Backend
netstat -an | grep :9002  # Frontend

# Or on Windows
netstat -an | findstr :8000
netstat -an | findstr :9002
```

## Common Issues and Solutions

### 1. Application Startup Issues

#### Backend Won't Start

**Symptoms:**
- Error: "ModuleNotFoundError: No module named 'fastapi'"
- Error: "Address already in use"
- Database connection errors

**Solutions:**

```bash
# Install missing dependencies
cd backend
pip install -r requirements.txt

# Check if port is in use
netstat -an | grep :8000
# Kill process using port 8000 if needed

# Database issues
python create_db.py  # Recreate database
```

**Common Causes:**
- Missing Python dependencies
- Port 8000 already in use
- Database file permissions
- Python version compatibility

#### Frontend Won't Start

**Symptoms:**
- Error: "Module not found"
- Port 9002 already in use
- Build errors

**Solutions:**

```bash
# Install missing dependencies
npm install

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check port usage
netstat -an | grep :9002

# Force different port
npm run dev -- --port 9003
```

### 2. Authentication Issues

#### Login Failures

**Symptoms:**
- "Incorrect username or password" error
- Token validation errors
- Automatic logout

**Diagnostic Steps:**

```bash
# Test login directly
curl -X POST http://localhost:8000/api/v1/users/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=testpassword"
```

**Solutions:**

1. **Check User Exists:**
   ```python
   # In Python shell
   from backend.database import SessionLocal
   from backend.models import User

   db = SessionLocal()
   users = db.query(User).all()
   for user in users:
       print(f"Email: {user.email}, Role: {user.role}")
   ```

2. **Create Test User:**
   ```python
   from backend.crud import create_user
   from backend.schemas import UserCreate
   from backend.database import SessionLocal

   db = SessionLocal()
   user_data = UserCreate(
       email="<EMAIL>",
       password="testpassword",
       full_name="Test User",
       role="admin"
   )
   create_user(db, user_data)
   ```

3. **Check Token Configuration:**
   ```python
   # Verify SECRET_KEY in backend/auth.py
   import os
   print(os.getenv("SECRET_KEY", "default-key"))
   ```

#### Token Expiry Issues

**Symptoms:**
- Frequent login prompts
- API calls returning 401 errors

**Solutions:**

1. **Increase Token Expiry:**
   ```python
   # In backend/auth.py
   ACCESS_TOKEN_EXPIRE_MINUTES = 60  # Increase from 30
   ```

2. **Check Token Storage:**
   ```javascript
   // In browser console
   console.log(localStorage.getItem('token'));
   ```

### 3. API Communication Issues

#### CORS Errors

**Symptoms:**
- "Access to fetch blocked by CORS policy"
- Network errors in browser console

**Solutions:**

1. **Check CORS Configuration:**
   ```python
   # In backend/main.py
   origins = [
       "http://localhost:9002",
       "http://127.0.0.1:9002",
       # Add your frontend URL
   ]
   ```

2. **Verify Frontend API URL:**
   ```typescript
   // In src/lib/axiosInstance.ts
   const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000/api/v1";
   ```

#### 404 API Errors

**Symptoms:**
- Specific endpoints returning 404
- "Not Found" errors for valid requests

**Diagnostic Steps:**

```bash
# List all available endpoints
curl http://localhost:8000/docs

# Test specific endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/requests/
```

**Solutions:**

1. **Check Endpoint URLs:**
   - Verify API version prefix (`/api/v1/`)
   - Check router inclusion in `main.py`
   - Confirm endpoint exists in router files

2. **Verify Router Registration:**
   ```python
   # In backend/main.py
   api_v1.include_router(user.router)
   api_v1.include_router(request.router)
   # Ensure all routers are included
   ```

### 4. Database Issues

#### SQLite Database Locked

**Symptoms:**
- "Database is locked" errors
- Slow database operations

**Solutions:**

```bash
# Check for lock files
ls -la *.db*

# Remove lock files (if safe)
rm app.db-wal app.db-shm

# Restart application
```

#### Data Integrity Issues

**Symptoms:**
- Foreign key constraint errors
- Data not saving properly

**Diagnostic Steps:**

```python
# Check database schema
from backend.database import engine
from sqlalchemy import inspect

inspector = inspect(engine)
tables = inspector.get_table_names()
print("Tables:", tables)

for table in tables:
    columns = inspector.get_columns(table)
    print(f"\n{table} columns:")
    for col in columns:
        print(f"  {col['name']}: {col['type']}")
```

**Solutions:**

1. **Recreate Database:**
   ```bash
   cd backend
   rm app.db  # Backup first if needed
   python create_db.py
   python populate_db.py
   ```

2. **Check Model Relationships:**
   ```python
   # Verify foreign key relationships in models.py
   # Ensure cascade options are correct
   ```

### 5. Frontend Issues

#### Tire Update 404 Errors (FIXED)

**Symptoms:**
- 404 errors when updating tire details
- "Endpoint not found" messages

**This issue has been resolved in v1.1.0:**
- Added proper CRUD function for request detail updates
- Fixed endpoint routing in backend
- Corrected field mapping between frontend and backend

**If still experiencing issues:**

1. **Verify Backend Version:**
   ```bash
   curl http://localhost:8000/health
   # Should return version 1.1.0 or later
   ```

2. **Check Endpoint Availability:**
   ```bash
   curl -X PUT http://localhost:8000/api/v1/requests/details/TEST_ID \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"tug_no": "test"}'
   ```

#### Navigation Errors (FIXED)

**Symptoms:**
- "Tire not found" when navigating to processing detail
- Data loading issues

**This issue has been resolved in v1.1.0:**
- Fixed data structure handling in tire-processing-detail page
- Improved URL parameter parsing
- Enhanced error handling

#### Tire Processing Data Disappearing (FIXED)

**Symptoms:**
- Processing data disappears after save operations
- Empty processing table after successful saves
- Data not persisting correctly

**This issue has been resolved in v1.2.0:**
- Implemented automatic data refresh after successful saves
- Fixed state management for processing data
- Added proper error handling for save operations

**If still experiencing issues:**

1. **Check Save Operation:**
   ```bash
   # Test processing update endpoint
   curl -X PUT http://localhost:8000/api/v1/cut-processing/1 \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"quantity": 2, "notes": "test"}'
   ```

2. **Verify Data Refresh:**
   ```bash
   # Check tire processing endpoint
   curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/cut-processing/by-tire/TIRE_ID
   ```

3. **Check Browser Console:**
   - Look for JavaScript errors during save operations
   - Verify API calls are completing successfully
   - Check for state management issues

#### Component Rendering Issues

**Symptoms:**
- Components not updating
- Stale data displayed
- UI inconsistencies

**Solutions:**

1. **Clear Browser Cache:**
   ```bash
   # Hard refresh
   Ctrl + F5 (Windows/Linux)
   Cmd + Shift + R (Mac)
   ```

2. **Check React DevTools:**
   - Install React Developer Tools browser extension
   - Check component state and props
   - Verify data flow

3. **Restart Development Server:**
   ```bash
   # Stop and restart frontend
   Ctrl + C
   npm run dev
   ```

### 6. Performance Issues

#### Slow API Responses

**Symptoms:**
- Long loading times
- Timeout errors
- High CPU usage

**Diagnostic Steps:**

```bash
# Check database query performance
# Enable SQL logging in backend/database.py
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},
    echo=True  # Enable SQL logging
)
```

**Solutions:**

1. **Database Optimization:**
   ```sql
   -- Add indexes for frequently queried fields
   CREATE INDEX idx_requests_status ON requests(status);
   CREATE INDEX idx_requests_date ON requests(request_date);
   ```

2. **Pagination Implementation:**
   ```python
   # Use skip and limit parameters
   requests = crud.get_requests(db, skip=0, limit=100)
   ```

#### Memory Issues

**Symptoms:**
- Browser tab crashes
- High memory usage
- Slow UI interactions

**Solutions:**

1. **Optimize Component Rendering:**
   ```typescript
   // Use React.memo for expensive components
   const ExpensiveComponent = React.memo(({ data }) => {
     // Component logic
   });
   ```

2. **Implement Virtual Scrolling:**
   ```typescript
   // For large lists, implement virtual scrolling
   // Consider libraries like react-window
   ```

### 7. Development Environment Issues

#### Hot Reload Not Working

**Symptoms:**
- Changes not reflected automatically
- Need to manually refresh

**Solutions:**

```bash
# Frontend hot reload issues
rm -rf .next
npm run dev

# Backend auto-reload issues
python -m uvicorn backend.main:app --reload --port 8000
```

#### TypeScript Errors

**Symptoms:**
- Type checking errors
- Build failures

**Solutions:**

```bash
# Run type checking
npm run typecheck

# Fix common issues
npm install @types/node @types/react @types/react-dom
```

## Debugging Tools

### Backend Debugging

```python
# Add logging to backend
import logging
logging.basicConfig(level=logging.DEBUG)

# Add breakpoints
import pdb; pdb.set_trace()

# Check database state
from backend.database import SessionLocal
from backend.models import *

db = SessionLocal()
# Query and inspect data
```

### Frontend Debugging

```javascript
// Browser console debugging
console.log('Debug info:', data);

// React DevTools
// Network tab for API calls
// Application tab for localStorage/sessionStorage
```

### Network Debugging

```bash
# Monitor network traffic
curl -v http://localhost:8000/api/v1/requests/

# Check headers and response
curl -I http://localhost:8000/health
```

## Getting Help

### Log Collection

When reporting issues, include:

1. **Backend Logs:**
   ```bash
   python -m uvicorn backend.main:app --reload --port 8000 > backend.log 2>&1
   ```

2. **Frontend Logs:**
   ```bash
   npm run dev > frontend.log 2>&1
   ```

3. **Browser Console:**
   - Open Developer Tools (F12)
   - Copy console errors and network failures

### System Information

Include system details:
- Operating System and version
- Node.js version (`node --version`)
- Python version (`python --version`)
- Browser and version
- Application version

### Reproduction Steps

Provide clear steps to reproduce the issue:
1. Specific actions taken
2. Expected behavior
3. Actual behavior
4. Error messages (exact text)
5. Screenshots if applicable

## Emergency Procedures

### Complete Reset

If all else fails, perform a complete reset:

```bash
# Stop all services
# Kill any running processes on ports 8000 and 9002

# Backend reset
cd backend
rm app.db
pip install -r requirements.txt
python create_db.py
python populate_db.py

# Frontend reset
cd ..
rm -rf node_modules package-lock.json .next
npm install
npm run dev

# Start backend in separate terminal
python -m uvicorn backend.main:app --reload --port 8000
```

### Data Recovery

If database is corrupted:

1. **Backup Current State:**
   ```bash
   cp app.db app.db.backup
   ```

2. **Export Data (if possible):**
   ```python
   # Export critical data before reset
   from backend.database import SessionLocal
   from backend.models import *

   db = SessionLocal()
   # Export requests, users, etc.
   ```

3. **Restore from Backup:**
   ```bash
   # If you have a recent backup
   cp app.db.backup app.db
   ```

This troubleshooting guide covers the most common issues. For complex problems or issues not covered here, consult the development team or create a detailed issue report.
