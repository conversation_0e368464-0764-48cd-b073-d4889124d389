#!/usr/bin/env python3
"""
Verify the database migration was successful
"""

import sqlite3

def verify_migration():
    """Verify the migration was successful"""
    print("🔍 Verifying Database Migration")
    print("=" * 40)
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # Check tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['tires', 'request_items', 'cut_operations', 'requests', 'processing', 'users', 'attachments']
        
        print("📋 Tables:")
        for table in expected_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} records")
            else:
                print(f"   ❌ {table}: MISSING")
        
        # Check relationships
        print("\n🔗 Testing Relationships:")
        
        # Tires -> Request Items
        cursor.execute("""
            SELECT COUNT(*) 
            FROM request_items ri 
            JOIN tires t ON ri.tire_id = t.id
        """)
        tire_items = cursor.fetchone()[0]
        print(f"   ✅ Request Items with valid Tires: {tire_items}")
        
        # Request Items -> Cut Operations
        cursor.execute("""
            SELECT COUNT(*) 
            FROM cut_operations co 
            JOIN request_items ri ON co.request_item_id = ri.id
        """)
        item_operations = cursor.fetchone()[0]
        print(f"   ✅ Cut Operations with valid Items: {item_operations}")
        
        # Cut Operations -> Processing
        cursor.execute("""
            SELECT COUNT(*) 
            FROM cut_operations co 
            JOIN processing p ON co.processing_id = p.id
        """)
        operation_processing = cursor.fetchone()[0]
        print(f"   ✅ Cut Operations with valid Processing: {operation_processing}")
        
        # Sample data
        print("\n📊 Sample Data:")
        
        cursor.execute("SELECT id, tug_no, size FROM tires LIMIT 3")
        tires = cursor.fetchall()
        for tire in tires:
            print(f"   🛞 Tire: {tire[0]} - {tire[1]} ({tire[2]})")
        
        cursor.execute("""
            SELECT ri.id, r.id, t.tug_no, ri.quantity 
            FROM request_items ri 
            JOIN requests r ON ri.request_id = r.id 
            JOIN tires t ON ri.tire_id = t.id 
            LIMIT 3
        """)
        items = cursor.fetchall()
        for item in items:
            print(f"   📦 Item: {item[0]} - Request: {item[1]} - Tire: {item[2]} (qty: {item[3]})")
        
        cursor.execute("""
            SELECT co.id, co.request_item_id, p.description1, co.status 
            FROM cut_operations co 
            JOIN processing p ON co.processing_id = p.id 
            LIMIT 3
        """)
        operations = cursor.fetchall()
        for op in operations:
            print(f"   ✂️ Operation: {op[0]} - Item: {op[1]} - {op[2]} ({op[3]})")
        
        print("\n" + "=" * 40)
        print("✅ Migration verification completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    verify_migration()
