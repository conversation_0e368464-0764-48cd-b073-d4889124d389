"use client";

import { useEffect, useState } from "react";
import { SummaryCard } from "@/components/dashboard/summary-card";
import type { DashboardRequestSummary } from "@/types/dashboard";
import { getRequests } from "@/services/requestService";

export default function DashboardPage() {
  const [requests, setRequests] = useState<DashboardRequestSummary[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setIsLoading(true);
    setError(null);
    getRequests()
      .then((response) => {
        const summaries = response.map((req) => ({
          id: req.id,
          title: req.projectNo,
          status: req.status,
          type: req.type,
          requestDate: req.requestDate instanceof Date ? req.requestDate.toISOString() : String(req.requestDate),
          targetDate: req.targetDate instanceof Date ? req.targetDate.toISOString() : String(req.targetDate),
          description: req.note ?? "",
        }));
        setRequests(summaries);
        setIsLoading(false);
      })
      .catch((err) => {
        setError("Errore durante il caricamento delle richieste.");
        setIsLoading(false);
      });
  }, []);

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-2xl sm:text-3xl font-medium tracking-normal text-primary">
          Le Tue Richieste in Sintesi
        </h2>
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-green-500"></div>
          <span className="text-sm font-medium">Completate: {requests.filter(r => r.status.toLowerCase().includes('complet')).length}</span>
          <div className="h-2 w-2 rounded-full bg-blue-500 ml-3"></div>
          <span className="text-sm font-medium">In Corso: {requests.filter(r => r.status.toLowerCase().includes('corso')).length}</span>
          <div className="h-2 w-2 rounded-full bg-amber-500 ml-3"></div>
          <span className="text-sm font-medium">In Attesa: {requests.filter(r => r.status.toLowerCase().includes('attesa')).length}</span>
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-12 bg-white dark:bg-gray-900 rounded-lg shadow-sm">
          <div className="flex flex-col items-center">
            <div className="h-8 w-8 rounded-full border-4 border-primary border-t-transparent animate-spin mb-3"></div>
            <span className="text-muted-foreground">Caricamento richieste...</span>
          </div>
        </div>
      )}

      {error && (
        <div className="flex justify-center items-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <span className="text-red-600 dark:text-red-400 font-medium">{error}</span>
        </div>
      )}

      {!isLoading && !error && requests.length === 0 && (
        <div className="flex justify-center items-center py-12 bg-white dark:bg-gray-900 rounded-lg shadow-sm">
          <span className="text-muted-foreground">Nessuna richiesta trovata</span>
        </div>
      )}

      {!isLoading && !error && requests.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 sm:gap-6">
          {requests.map((request) => (
            <SummaryCard key={request.id} request={request} />
          ))}
        </div>
      )}
    </div>
  );
}
