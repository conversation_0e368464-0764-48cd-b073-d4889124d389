from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from .. import crud, models, schemas, auth
from ..database import get_db

router = APIRouter(
    prefix="/requests",
    tags=["requests"],
)

from typing import Optional
from fastapi import Query

@router.get("/", response_model=List[schemas.RequestSummary], dependencies=[Depends(auth.require_viewer_role)])
def read_requests(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    requests = crud.get_requests_summary(db, skip=skip, limit=limit, status=status)
    return requests

@router.get("/test-ping", tags=["requests"])
async def test_ping_requests():
    return {"message": "Ping successful from requests router"}

@router.get("/{request_id}", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_viewer_role)])
def read_request(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    db_request = crud.get_request(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    return db_request

@router.post("/", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_editor_role)])
def create_request(request: schemas.RequestCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    print(f"--- CREATE_REQUEST PAYLOAD ---")
    print(request.dict())
    print(f"--- END CREATE_REQUEST PAYLOAD ---")
    db_request = crud.create_request(db, request)
    return db_request

@router.put("/{request_id}", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_editor_role)])
def update_request(request_id: str, request: schemas.RequestUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    print(f"--- UPDATE_REQUEST PAYLOAD for ID: {request_id} ---")
    print(request.dict())
    print(f"--- END UPDATE_REQUEST PAYLOAD ---")
    db_request = crud.update_request(db, request_id, request)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    return db_request

@router.delete("/{request_id}", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_admin_role)])
def delete_request(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    db_request = crud.delete_request(db, request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    return db_request

# --- GET /requests/{request_id}/tires ---
@router.get("/{request_id}/tires", response_model=List[schemas.RequestDetailRead], dependencies=[Depends(auth.require_viewer_role)])
def get_request_tires(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Get tires for a request by joining request_items with tires table.
    Returns data in the legacy RequestDetailRead format for backward compatibility.
    """
    db_request = crud.get_request(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")

    # Get request items with tire details
    request_items_with_tires = db.query(models.RequestItem).join(
        models.Tire, models.RequestItem.tire_id == models.Tire.id
    ).filter(models.RequestItem.request_id == request_id).all()

    # Convert to legacy RequestDetailRead format
    legacy_tire_details = []
    for item in request_items_with_tires:
        tire = item.tire
        legacy_detail = schemas.RequestDetailRead(
            id=item.id,
            request_id=item.request_id,
            tug_number=tire.tug_no,
            section=item.section or "SECTION_A",  # Use item section or default
            project_number=tire.project_no,
            spec_number=tire.spec_no,
            tire_size=tire.size,
            pattern=tire.pattern,
            note=item.notes or "",
            disposition=item.disposition,
            process_number=item.quantity
        )
        legacy_tire_details.append(legacy_detail)

    return legacy_tire_details

# --- GET /requests/details/{detail_id} ---
@router.get("/details/{detail_id}", response_model=schemas.RequestDetailRead, dependencies=[Depends(auth.require_viewer_role)])
def get_request_detail(detail_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Retrieve a specific request detail by ID.

    Parameters:
    - detail_id: ID of the request detail to retrieve

    Returns:
    - The request detail object

    Raises:
    - 404 Not Found: If the request detail with the specified ID does not exist
    """
    db_detail = crud.get_request_detail(db, detail_id)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Request detail not found")
    return db_detail

# --- DELETE /requests/details/{detail_id} ---
@router.delete("/details/{detail_id}", response_model=schemas.RequestDetailRead, dependencies=[Depends(auth.require_editor_role)])
def delete_request_detail(detail_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Delete a request detail (tire) record.

    Parameters:
    - detail_id: ID of the request detail to delete

    Returns:
    - The deleted request detail object

    Raises:
    - 404 Not Found: If the request detail with the specified ID does not exist
    """
    db_detail = crud.delete_request_detail(db, detail_id)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Request detail not found")
    return db_detail

# --- PUT /requests/details/{detail_id} ---
@router.put("/details/{detail_id}", response_model=schemas.RequestDetailRead, dependencies=[Depends(auth.require_editor_role)])
def update_request_detail(detail_id: str, detail: schemas.RequestDetailUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    print(f"--- UPDATE_REQUEST_DETAIL PAYLOAD for ID: {detail_id} ---")
    print(detail.dict())
    print(f"--- END UPDATE_REQUEST_DETAIL PAYLOAD ---")
    """
    Update a request detail (tire) record.

    Parameters:
    - detail_id: ID of the request detail to update
    - detail: Updated request detail data

    Returns:
    - The updated request detail object

    Raises:
    - 404 Not Found: If the request detail with the specified ID does not exist
    """
    db_detail = crud.update_request_detail(db, detail_id, detail)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Request detail not found")
    return db_detail

# --- POST /requests/{request_id}/save ---
from fastapi import Body

@router.post("/{request_id}/save", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_editor_role)])
def save_request(
    request_id: str,
    request_data: schemas.RequestUpdate = Body(...),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    print(f"--- SAVE_REQUEST PAYLOAD for ID: {request_id} ---")
    print(request_data.dict())
    print(f"--- END SAVE_REQUEST PAYLOAD ---")
    # Aggiorna la richiesta principale
    db_request = crud.update_request(db, request_id, request_data)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    # Aggiorna o crea i dettagli associati
    if hasattr(request_data, "request_details") and request_data.request_details:
        for detail in request_data.request_details:
            crud.update_or_create_request_detail(db, detail)
    db.commit()
    db.refresh(db_request)
    return db_request

# --- POST /requests/{request_id}/send ---
@router.post("/{request_id}/send", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_editor_role)])
def send_request_action(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    # Placeholder logic: Fetch the request and return it.
    # In a real scenario, this would likely update the request's status.
    db_request = crud.get_request(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found to send")

    # Example: Update status (actual logic would be in crud.py)
    # db_request.status = "SENT"
    # db.commit()
    # db.refresh(db_request)
    print(f"Simulating sending request: {request_id}") # Placeholder log
    return db_request

# --- POST /requests/{request_id}/copy-send ---
@router.post("/{request_id}/copy-send", response_model=schemas.RequestRead, dependencies=[Depends(auth.require_editor_role)])
def copy_send_request_action(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    # Placeholder logic: Fetch the original request, imagine copying it, and return original.
    # In a real scenario, this would:
    # 1. Fetch the original request.
    # 2. Create a new request based on the original's data (potentially with modifications).
    # 3. "Send" the new request (e.g., update its status).
    original_request = crud.get_request(db, request_id=request_id)
    if original_request is None:
        raise HTTPException(status_code=404, detail="Original request not found to copy-send")

    # Example: (actual logic would be in crud.py for copying and creating)
    # new_request_data = schemas.RequestCreate(**original_request.dict_for_copy()) # Hypothetical method
    # new_db_request = crud.create_request(db, new_request_data)
    # new_db_request.status = "SENT"
    # db.commit()
    # db.refresh(new_db_request)
    # return new_db_request
    print(f"Simulating copy-sending request: {request_id}") # Placeholder log
    return original_request # Returning original for now as placeholder

# ============================================================================
# NEW SIMPLIFIED ENDPOINTS - MIGRATION TARGET
# ============================================================================

# --- GET /requests/{request_id}/simplified ---
@router.get("/{request_id}/simplified", response_model=schemas.RequestSimplifiedRead, dependencies=[Depends(auth.require_viewer_role)])
def get_request_simplified(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Get a specific request by ID using the new simplified structure.

    Returns request with only the new structure (request_items with tire references).
    """
    db_request = crud.get_request_simplified(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    return db_request

# --- GET /requests/{request_id}/items ---
@router.get("/{request_id}/items", response_model=List[schemas.RequestItemRead], dependencies=[Depends(auth.require_viewer_role)])
def get_request_items(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Get all request items for a specific request (new simplified structure).

    Returns items with tire details included.
    """
    db_request = crud.get_request(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")

    items = crud.get_request_items_by_request(db, request_id=request_id)
    return items

# --- GET /requests/{request_id}/cut-operations ---
@router.get("/{request_id}/cut-operations", response_model=List[schemas.CutOperationRead], dependencies=[Depends(auth.require_viewer_role)])
def get_request_cut_operations(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Get all cut operations for a specific request (across all items).

    Returns operations with processing details and tire information.
    """
    db_request = crud.get_request(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")

    operations = crud.get_cut_operations_by_request(db, request_id=request_id)
    return operations

# --- GET /requests/{request_id}/summary ---
@router.get("/{request_id}/summary", dependencies=[Depends(auth.require_viewer_role)])
def get_request_summary(request_id: str, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Get a comprehensive summary of a request including items, cut operations, and statistics.
    """
    db_request = crud.get_request_simplified(db, request_id=request_id)
    if db_request is None:
        raise HTTPException(status_code=404, detail="Request not found")

    # Get cut operations
    cut_operations = crud.get_cut_operations_by_request(db, request_id=request_id)

    # Calculate statistics
    total_items = len(db_request.request_items)
    total_cuts = len(cut_operations)
    total_cost = sum(op.cut_price or 0 for op in cut_operations)

    # Group by status
    status_counts = {}
    for op in cut_operations:
        status = op.status or "UNKNOWN"
        status_counts[status] = status_counts.get(status, 0) + 1

    # Group by processing type
    processing_counts = {}
    for op in cut_operations:
        if op.processing:
            proc_type = op.processing.tire_type
            processing_counts[proc_type] = processing_counts.get(proc_type, 0) + 1

    return {
        "request": db_request,
        "statistics": {
            "total_items": total_items,
            "total_cuts": total_cuts,
            "total_cost": total_cost,
            "status_breakdown": status_counts,
            "processing_breakdown": processing_counts
        },
        "cut_operations": cut_operations
    }
