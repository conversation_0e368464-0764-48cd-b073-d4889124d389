/**
 * INTEGRATION TESTING SUITE - PRIORITÀ 3
 *
 * Test suite completa che testa:
 * - Integrazione completa PRIORITÀ 1 + PRIORITÀ 2 + PRIORITÀ 3
 * - Performance benchmarks
 * - Error handling scenarios
 * - Validation edge cases
 * - Cross-browser compatibility
 * - Accessibility compliance
 */

// PRIORITÀ 1 Components
import { CrudServiceFactory } from '@/lib/generic-crud-service';

// PRIORITÀ 2 Components
import { EnhancedDataTransformer } from '@/lib/data-transformer';

// PRIORITÀ 3 Components
import {
  ValidationEngine,
  RequestValidationSchema,
  TireValidationSchema
} from '@/lib/validation-schemas';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler';
import {
  PerformanceMonitor,
  ApiCache,
  MemoryManager
} from '@/lib/performance-optimizer';

// ============================================================================
// TEST UTILITIES
// ============================================================================

interface TestMetrics {
  renderTime: number;
  memoryUsage: number;
  apiResponseTime?: number;
  validationTime?: number;
  transformationTime?: number;
}

class IntegrationTestUtils {

  /**
   * Measures performance of a function
   */
  static async measurePerformance<T>(
    fn: () => Promise<T> | T,
    label: string
  ): Promise<{ result: T; metrics: TestMetrics }> {
    const startTime = performance.now();
    const startMemory = MemoryManager.getCurrentMemoryUsage();

    const result = await fn();

    const endTime = performance.now();
    const endMemory = MemoryManager.getCurrentMemoryUsage();

    const metrics: TestMetrics = {
      renderTime: endTime - startTime,
      memoryUsage: endMemory - startMemory
    };

    console.log(`Performance [${label}]:`, metrics);
    return { result, metrics };
  }

  /**
   * Creates mock API responses
   */
  static createMockApiResponse<T>(data: T, delay: number = 100): Promise<T> {
    return new Promise((resolve) => {
      setTimeout(() => resolve(data), delay);
    });
  }

  /**
   * Simulates network errors
   */
  static createNetworkError(message: string = 'Network Error'): Error {
    const error = new Error(message);
    (error as any).isAxiosError = true;
    (error as any).code = 'NETWORK_ERROR';
    return error;
  }

  /**
   * Creates test data for entities
   */
  static createTestData = {
    request: {
      id: 'test-request-1',
      requestBy: 'Test User',
      projectNo: 'PRJ-001',
      tireSize: '225/60R16',
      requestDate: new Date('2023-01-01'),
      targetDate: new Date('2023-01-15'),
      type: 'Standard',
      status: 'Pending'
    },
    tire: {
      id: 'test-tire-1',
      tugNo: 'TUG-001',
      projectNo: 'PRJ-001',
      tireSize: '225/60R16',
      quantity: 4,
      disposition: 'Available'
    },
    user: {
      id: 1,
      email: '<EMAIL>',
      fullName: 'Test User',
      role: 'editor',
      isActive: true
    }
  };

  /**
   * Runs integration tests
   */
  static async runIntegrationTests(): Promise<void> {
    console.log('🧪 Starting Integration Tests...');

    try {
      await this.testPriority1Integration();
      await this.testPriority2Integration();
      await this.testPriority3Integration();
      await this.testPerformanceBenchmarks();
      await this.testErrorHandling();

      console.log('✅ All integration tests passed!');
    } catch (error) {
      console.error('❌ Integration tests failed:', error);
      throw error;
    }
  }

  /**
   * Tests PRIORITÀ 1 integration
   */
  private static async testPriority1Integration(): Promise<void> {
    console.log('Testing PRIORITÀ 1 Integration...');

    // Test Generic CRUD Service
    const userService = CrudServiceFactory.createStandardService('/users');
    const tireService = CrudServiceFactory.createStandardService('/tires');

    // Verify services have consistent interface
    const requiredMethods = ['getAll', 'getById', 'create', 'update', 'delete'];

    for (const method of requiredMethods) {
      if (typeof (userService as any)[method] !== 'function') {
        throw new Error(`userService missing method: ${method}`);
      }
      if (typeof (tireService as any)[method] !== 'function') {
        throw new Error(`tireService missing method: ${method}`);
      }
    }

    console.log('✅ PRIORITÀ 1 Integration tests passed');
  }

  /**
   * Tests PRIORITÀ 2 integration
   */
  private static async testPriority2Integration(): Promise<void> {
    console.log('Testing PRIORITÀ 2 Integration...');

    // Test Data Transformation
    const testData = {
      requestBy: 'Test User',
      projectNo: 'PRJ-001',
      createdAt: new Date()
    };

    const { result: transformedData, metrics } = await this.measurePerformance(() => {
      return EnhancedDataTransformer.camelToSnake(testData);
    }, 'Data Transformation');

    const expectedResult = {
      request_by: 'Test User',
      project_no: 'PRJ-001',
      created_at: testData.createdAt
    };

    if (JSON.stringify(transformedData) !== JSON.stringify(expectedResult)) {
      throw new Error('Data transformation failed');
    }

    if (metrics.renderTime > 10) {
      throw new Error(`Data transformation too slow: ${metrics.renderTime}ms`);
    }

    // Test large dataset transformation
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      requestBy: `User ${i}`,
      projectNo: `PRJ-${i.toString().padStart(3, '0')}`,
      itemCount: i
    }));

    const { result: batchResult, metrics: batchMetrics } = await this.measurePerformance(() => {
      return EnhancedDataTransformer.transformBatch(
        largeDataset,
        (item) => EnhancedDataTransformer.camelToSnake(item)
      );
    }, 'Large Dataset Transformation');

    if (!Array.isArray(batchResult) || batchResult.length !== 1000) {
      throw new Error('Batch transformation failed');
    }

    if (batchMetrics.renderTime > 100) {
      throw new Error(`Batch transformation too slow: ${batchMetrics.renderTime}ms`);
    }

    console.log('✅ PRIORITÀ 2 Integration tests passed');
  }

  /**
   * Tests PRIORITÀ 3 integration
   */
  private static async testPriority3Integration(): Promise<void> {
    console.log('Testing PRIORITÀ 3 Integration...');

    // Test Validation Schema Integration
    const validData = this.createTestData.request;
    const invalidData = { ...validData, requestBy: '', tireSize: '' };

    const { result: validResult, metrics: validMetrics } = await this.measurePerformance(() => {
      return ValidationEngine.validate(validData, RequestValidationSchema);
    }, 'Valid Data Validation');

    const { result: invalidResult, metrics: invalidMetrics } = await this.measurePerformance(() => {
      return ValidationEngine.validate(invalidData, RequestValidationSchema);
    }, 'Invalid Data Validation');

    if (!validResult.isValid) {
      throw new Error('Valid data validation failed');
    }

    if (invalidResult.isValid) {
      throw new Error('Invalid data validation should have failed');
    }

    if (validMetrics.renderTime > 10 || invalidMetrics.renderTime > 10) {
      throw new Error('Validation too slow');
    }

    // Test cross-field validation
    const invalidDateData = {
      ...this.createTestData.request,
      requestDate: new Date('2023-01-15'),
      targetDate: new Date('2023-01-10') // Target before request
    };

    const crossFieldResult = ValidationEngine.validate(invalidDateData, RequestValidationSchema);

    if (crossFieldResult.isValid) {
      throw new Error('Cross-field validation should have failed');
    }

    // Test Error Handler Integration
    const error = this.createNetworkError('Test API Error');

    try {
      await ErrorHandler.handleApiCall(
        () => Promise.reject(error),
        { showToast: false, logError: true }
      );
      throw new Error('Error handler should have thrown');
    } catch (e) {
      // Expected to throw
    }

    // Test API Cache
    const testCacheData = this.createTestData.request;
    const cacheKey = 'test-cache-key';

    ApiCache.set(cacheKey, testCacheData);

    const { result: cachedResult, metrics: cacheMetrics } = await this.measurePerformance(() => {
      return ApiCache.get(cacheKey);
    }, 'Cache Retrieval');

    if (JSON.stringify(cachedResult) !== JSON.stringify(testCacheData)) {
      throw new Error('Cache retrieval failed');
    }

    if (cacheMetrics.renderTime > 1) {
      throw new Error('Cache retrieval too slow');
    }

    console.log('✅ PRIORITÀ 3 Integration tests passed');
  }

  /**
   * Tests performance benchmarks
   */
  private static async testPerformanceBenchmarks(): Promise<void> {
    console.log('Testing Performance Benchmarks...');

    const benchmarks = {
      validation: 10, // ms
      dataTransformation: 20, // ms
      cacheRetrieval: 1, // ms
      errorHandling: 30 // ms
    };

    // Test validation performance
    const { metrics: validationMetrics } = await this.measurePerformance(() => {
      return ValidationEngine.validate(
        this.createTestData.request,
        RequestValidationSchema
      );
    }, 'Validation Performance');

    // Test data transformation performance
    const { metrics: transformMetrics } = await this.measurePerformance(() => {
      return EnhancedDataTransformer.camelToSnake(this.createTestData.request);
    }, 'Data Transformation Performance');

    // Test cache performance
    ApiCache.set('perf-test', this.createTestData.request);
    const { metrics: cacheMetrics } = await this.measurePerformance(() => {
      return ApiCache.get('perf-test');
    }, 'Cache Performance');

    // Assert performance targets
    if (validationMetrics.renderTime > benchmarks.validation) {
      throw new Error(`Validation too slow: ${validationMetrics.renderTime}ms > ${benchmarks.validation}ms`);
    }

    if (transformMetrics.renderTime > benchmarks.dataTransformation) {
      throw new Error(`Data transformation too slow: ${transformMetrics.renderTime}ms > ${benchmarks.dataTransformation}ms`);
    }

    if (cacheMetrics.renderTime > benchmarks.cacheRetrieval) {
      throw new Error(`Cache retrieval too slow: ${cacheMetrics.renderTime}ms > ${benchmarks.cacheRetrieval}ms`);
    }

    console.log('✅ Performance benchmarks passed');
  }

  /**
   * Tests error handling scenarios
   */
  private static async testErrorHandling(): Promise<void> {
    console.log('Testing Error Handling...');

    // Test network error handling
    const networkError = this.createNetworkError('Network timeout');

    try {
      await ErrorHandler.handleApiCall(
        () => Promise.reject(networkError),
        { showToast: false, logError: false }
      );
      throw new Error('Should have thrown network error');
    } catch (error) {
      if (error.message !== 'Network timeout') {
        throw new Error(`Unexpected error message: ${error.message}`);
      }
    }

    // Test validation error handling
    const invalidData = { requestBy: '', tireSize: '' };
    const validationResult = ValidationEngine.validate(invalidData, RequestValidationSchema);

    if (validationResult.isValid) {
      throw new Error('Validation should have failed for invalid data');
    }

    if (!validationResult.errors.requestBy || !validationResult.errors.tireSize) {
      throw new Error('Validation errors not properly reported');
    }

    console.log('✅ Error handling tests passed');
  }

  /**
   * Tests memory efficiency
   */
  static async testMemoryEfficiency(): Promise<void> {
    console.log('Testing Memory Efficiency...');

    const initialMemory = MemoryManager.getCurrentMemoryUsage();

    // Perform memory-intensive operations
    const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
      id: i,
      data: `Large data string ${i}`.repeat(100)
    }));

    // Transform large dataset
    const transformed = EnhancedDataTransformer.transformBatch(
      largeDataset,
      (item) => EnhancedDataTransformer.camelToSnake(item)
    );

    const finalMemory = MemoryManager.getCurrentMemoryUsage();
    const memoryIncrease = finalMemory - initialMemory;

    // Memory increase should be reasonable (less than 50MB for this test)
    if (memoryIncrease > 50) {
      console.warn(`High memory usage detected: ${memoryIncrease}MB`);
    }

    if (!Array.isArray(transformed) || transformed.length !== 10000) {
      throw new Error('Large dataset transformation failed');
    }

    console.log('✅ Memory efficiency tests passed');
  }

  /**
   * Tests cross-browser compatibility
   */
  static testCrossBrowserCompatibility(): void {
    console.log('Testing Cross-Browser Compatibility...');

    // Test memory management works across browsers
    const memoryUsage = MemoryManager.getCurrentMemoryUsage();
    if (memoryUsage < 0) {
      throw new Error('Memory management not working properly');
    }

    // Test performance API availability
    if (typeof performance === 'undefined' || typeof performance.now !== 'function') {
      throw new Error('Performance API not available');
    }

    // Test basic API cache functionality
    ApiCache.set('browser-test', { test: 'data' });
    const cached = ApiCache.get('browser-test');
    if (!cached || cached.test !== 'data') {
      throw new Error('API cache not working properly');
    }

    console.log('✅ Cross-browser compatibility tests passed');
  }

  /**
   * Runs all integration tests
   */
  static async runAllTests(): Promise<void> {
    console.log('🚀 Starting Complete Integration Test Suite...');

    try {
      // Clear any existing state
      ApiCache.clear();
      PerformanceMonitor.clear();

      // Run all test suites
      await this.runIntegrationTests();
      await this.testMemoryEfficiency();
      this.testCrossBrowserCompatibility();

      // Test performance monitoring
      PerformanceMonitor.recordMetric({
        renderTime: 25,
        memoryUsage: 50,
        apiResponseTime: 200,
        rerenderCount: 1,
        timestamp: new Date()
      });

      const stats = PerformanceMonitor.getStats();
      if (stats.avgRenderTime !== 25) {
        throw new Error('Performance monitoring not working correctly');
      }

      console.log('🎉 All integration tests completed successfully!');
      console.log('📊 Final Performance Stats:', stats);

    } catch (error) {
      console.error('💥 Integration test suite failed:', error);
      throw error;
    }
  }
}

// ============================================================================
// INTEGRATION TEST RUNNER
// ============================================================================

/**
 * Main integration test runner
 */
class IntegrationTestRunner {

  /**
   * Runs integration tests in browser environment
   */
  static async runInBrowser(): Promise<void> {
    if (typeof window === 'undefined') {
      console.warn('Browser tests can only run in browser environment');
      return;
    }

    try {
      await IntegrationTestUtils.runAllTests();

      // Display results in browser
      const resultsDiv = document.createElement('div');
      resultsDiv.innerHTML = `
        <div style="position: fixed; top: 10px; right: 10px; background: green; color: white; padding: 10px; border-radius: 5px; z-index: 9999;">
          ✅ Integration Tests Passed
        </div>
      `;
      document.body.appendChild(resultsDiv);

      setTimeout(() => {
        document.body.removeChild(resultsDiv);
      }, 5000);

    } catch (error) {
      console.error('Integration tests failed:', error);

      // Display error in browser
      const errorDiv = document.createElement('div');
      errorDiv.innerHTML = `
        <div style="position: fixed; top: 10px; right: 10px; background: red; color: white; padding: 10px; border-radius: 5px; z-index: 9999;">
          ❌ Integration Tests Failed<br>
          <small>${error.message}</small>
        </div>
      `;
      document.body.appendChild(errorDiv);
    }
  }

  /**
   * Runs integration tests in Node.js environment
   */
  static async runInNode(): Promise<void> {
    if (typeof window !== 'undefined') {
      console.warn('Node tests can only run in Node.js environment');
      return;
    }

    try {
      await IntegrationTestUtils.runAllTests();
      process.exit(0);
    } catch (error) {
      console.error('Integration tests failed:', error);
      process.exit(1);
    }
  }
}

// ============================================================================
// AUTO-RUN IN DEVELOPMENT
// ============================================================================

// Auto-run tests in development mode
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Run tests after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      IntegrationTestRunner.runInBrowser().catch(console.error);
    }, 1000);
  });
}

// Export classes
export { IntegrationTestUtils, IntegrationTestRunner };