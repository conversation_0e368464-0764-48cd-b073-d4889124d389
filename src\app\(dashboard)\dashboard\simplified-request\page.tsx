"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ArrowLeft, FileText, Package, Scissors, BarChart3 } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { SimplifiedAppRequest, RequestItem, CutOperation } from "@/types";
import {
  getRequestSimplified,
  getRequestItems,
  getRequestCutOperations,
  getRequestSummary,
} from "@/services/requestService";

import { RequestItemsManagement } from "@/components/simplified/request-items-management";
import { CutOperationsManagement } from "@/components/simplified/cut-operations-management";

export default function SimplifiedRequestPage() {
  const searchParams = useSearchParams();
  const requestId = searchParams.get("id");

  const [request, setRequest] = useState<SimplifiedAppRequest | null>(null);
  const [requestItems, setRequestItems] = useState<RequestItem[]>([]);
  const [cutOperations, setCutOperations] = useState<CutOperation[]>([]);
  const [summary, setSummary] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  const { toast } = useToast();

  useEffect(() => {
    if (requestId) {
      loadRequestData();
    }
  }, [requestId]);

  const loadRequestData = async () => {
    if (!requestId) return;

    try {
      setLoading(true);

      // Load request with simplified structure
      const [requestData, itemsData, operationsData, summaryData] = await Promise.all([
        getRequestSimplified(requestId),
        getRequestItems(requestId),
        getRequestCutOperations(requestId),
        getRequestSummary(requestId),
      ]);

      setRequest(requestData);
      setRequestItems(itemsData);
      setCutOperations(operationsData);
      setSummary(summaryData);
    } catch (error) {
      console.error("Error loading request data:", error);
      toast({
        title: "Error",
        description: "Failed to load request data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusColors: Record<string, string> = {
      DRAFT: "bg-gray-100 text-gray-800",
      OPEN: "bg-blue-100 text-blue-800",
      IN_PROGRESS: "bg-yellow-100 text-yellow-800",
      COMPLETED: "bg-green-100 text-green-800",
      CANCELLED: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={statusColors[status] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!request) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold mb-4">Request Not Found</h1>
        <p className="text-gray-600 mb-4">The requested item could not be found.</p>
        <Button onClick={() => window.history.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{request.id}</h1>
            <p className="text-gray-600">{request.projectNo}</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {getStatusBadge(request.status)}
          <Badge variant="outline">{request.type}</Badge>
        </div>
      </div>

      {/* Request Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Request Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Requested By</label>
              <p className="text-sm">{request.requestBy}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Tire Size</label>
              <p className="text-sm">{request.tireSize}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Request Date</label>
              <p className="text-sm">{new Date(request.requestDate).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Target Date</label>
              <p className="text-sm">{new Date(request.targetDate).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Destination</label>
              <p className="text-sm">{request.destination || "N/A"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">In Charge Of</label>
              <p className="text-sm">{request.inChargeOf || "N/A"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Internal</label>
              <p className="text-sm">{request.internal ? "Yes" : "No"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Total Items</label>
              <p className="text-sm">{summary?.statistics?.total_items || 0}</p>
            </div>
          </div>
          {request.note && (
            <div className="mt-4">
              <label className="text-sm font-medium text-gray-500">Notes</label>
              <p className="text-sm mt-1">{request.note}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics Summary */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Items</p>
                  <p className="text-2xl font-bold">{summary.statistics.total_items}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Cuts</p>
                  <p className="text-2xl font-bold">{summary.statistics.total_cuts}</p>
                </div>
                <Scissors className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Cost</p>
                  <p className="text-2xl font-bold">€{summary.statistics.total_cost.toFixed(2)}</p>
                </div>
                <FileText className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Cost/Cut</p>
                  <p className="text-2xl font-bold">
                    €{summary.statistics.total_cuts > 0
                      ? (summary.statistics.total_cost / summary.statistics.total_cuts).toFixed(2)
                      : "0.00"
                    }
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs for detailed management */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="items">Request Items ({requestItems.length})</TabsTrigger>
          <TabsTrigger value="operations">Cut Operations ({cutOperations.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Request Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Status Breakdown</h4>
                  <div className="flex flex-wrap gap-2">
                    {summary?.statistics?.status_breakdown &&
                      Object.entries(summary.statistics.status_breakdown).map(([status, count]) => (
                        <Badge key={status} variant="outline">
                          {status}: {count as number}
                        </Badge>
                      ))
                    }
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Processing Breakdown</h4>
                  <div className="flex flex-wrap gap-2">
                    {summary?.statistics?.processing_breakdown &&
                      Object.entries(summary.statistics.processing_breakdown).map(([type, count]) => (
                        <Badge key={type} variant="outline">
                          {type}: {count as number}
                        </Badge>
                      ))
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="items">
          <RequestItemsManagement
            requestId={requestId!}
            onItemsChange={setRequestItems}
          />
        </TabsContent>

        <TabsContent value="operations">
          <CutOperationsManagement
            requestId={requestId!}
            onOperationsChange={setCutOperations}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
