from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from .. import crud, models, schemas, auth
from ..database import get_db

router = APIRouter(
    prefix="/cut-processing",
    tags=["cut-processing"],
)

@router.get("/", response_model=List[schemas.CutProcessingRead], dependencies=[Depends(auth.require_viewer_role)])
def read_cut_processing_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Retrieve all cut processing with pagination.

    Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (pagination)

    Returns:
    - List of cut processing objects
    """
    cut_processing_list = crud.get_cut_processing_list(db, skip=skip, limit=limit)
    return cut_processing_list

@router.get("/{cut_processing_id}", response_model=schemas.CutProcessingRead, dependencies=[Depends(auth.require_viewer_role)])
def read_cut_processing(cut_processing_id: int, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Retrieve a specific cut processing by ID.

    Parameters:
    - cut_processing_id: ID of the cut processing to retrieve

    Returns:
    - The cut processing object

    Raises:
    - 404 Not Found: If the cut processing with the specified ID does not exist
    """
    db_cut_processing = crud.get_cut_processing(db, cut_processing_id=cut_processing_id)
    if db_cut_processing is None:
        raise HTTPException(status_code=404, detail="Cut processing not found")
    return db_cut_processing

@router.post("/", response_model=schemas.CutProcessingRead, dependencies=[Depends(auth.require_editor_role)])
def create_cut_processing(cut_processing: schemas.CutProcessingCreate, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Create a new cut processing association.

    Parameters:
    - cut_processing: Cut processing data to create

    Returns:
    - The created cut processing object
    """
    db_cut_processing = crud.create_cut_processing(db, cut_processing)
    return db_cut_processing

@router.put("/{cut_processing_id}", response_model=schemas.CutProcessingRead, dependencies=[Depends(auth.require_editor_role)])
def update_cut_processing(
    cut_processing_id: int,
    cut_processing: schemas.CutProcessingUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Update an existing cut processing.

    Parameters:
    - cut_processing_id: ID of the cut processing to update
    - cut_processing: Updated cut processing data

    Returns:
    - The updated cut processing object

    Raises:
    - 404 Not Found: If the cut processing with the specified ID does not exist
    """
    db_cut_processing = crud.update_cut_processing(db, cut_processing_id, cut_processing)
    if db_cut_processing is None:
        raise HTTPException(status_code=404, detail="Cut processing not found")
    return db_cut_processing

@router.delete("/{cut_processing_id}", response_model=schemas.CutProcessingRead, dependencies=[Depends(auth.require_admin_role)])
def delete_cut_processing(cut_processing_id: int, db: Session = Depends(get_db), current_user: models.User = Depends(auth.get_current_user)):
    """
    Delete a cut processing.

    Parameters:
    - cut_processing_id: ID of the cut processing to delete

    Returns:
    - The deleted cut processing object

    Raises:
    - 404 Not Found: If the cut processing with the specified ID does not exist
    """
    # First check if the record exists before attempting deletion
    existing_record = crud.get_cut_processing(db, cut_processing_id)
    if existing_record is None:
        raise HTTPException(
            status_code=404,
            detail=f"Cut processing with ID {cut_processing_id} not found. It may have been already deleted or never existed."
        )

    db_cut_processing = crud.delete_cut_processing(db, cut_processing_id)
    if db_cut_processing is None:
        # This should not happen if the record existed above, but adding as safety check
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete cut processing with ID {cut_processing_id}. Please try again."
        )
    return db_cut_processing

@router.get("/by-cut/{cut_id}", response_model=List[schemas.CutProcessingRead], dependencies=[Depends(auth.require_viewer_role)])
def get_cut_processing_by_cut(
    cut_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Get all processing for a specific cut.

    Parameters:
    - cut_id: ID of the cut

    Returns:
    - List of processing for the specified cut
    """
    cut_processing_list = crud.get_cut_processing_by_cut(db, cut_id=cut_id)
    return cut_processing_list

@router.delete("/by-cut-and-processing/{cut_id}/{processing_id}", response_model=schemas.CutProcessingRead, dependencies=[Depends(auth.require_admin_role)])
def delete_cut_processing_by_cut_and_processing(
    cut_id: int,
    processing_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Delete a specific cut processing by cut_id and processing_id.

    Parameters:
    - cut_id: ID of the cut
    - processing_id: ID of the processing

    Returns:
    - The deleted cut processing object

    Raises:
    - 404 Not Found: If the cut processing association does not exist
    """
    db_cut_processing = crud.delete_cut_processing_by_cut_and_processing(db, cut_id, processing_id)
    if db_cut_processing is None:
        raise HTTPException(status_code=404, detail="Cut processing association not found")
    return db_cut_processing

# --- Processing search endpoints ---

@router.get("/processing/search", response_model=List[schemas.ProcessingRead], dependencies=[Depends(auth.require_viewer_role)])
def search_processing(
    tire_type: Optional[str] = Query(None),
    description1: Optional[str] = Query(None),
    description2: Optional[str] = Query(None),
    test_code1: Optional[str] = Query(None),
    test_code2: Optional[str] = Query(None),
    min_cost: Optional[float] = Query(None),
    max_cost: Optional[float] = Query(None),
    picture: Optional[bool] = Query(None),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Search processing with advanced filters.

    Parameters:
    - tire_type: Filter by tire type
    - description1: Filter by description1
    - description2: Filter by description2
    - test_code1: Filter by test code 1
    - test_code2: Filter by test code 2
    - min_cost: Minimum cost filter
    - max_cost: Maximum cost filter
    - picture: Filter by picture availability
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (pagination)

    Returns:
    - List of processing objects matching the criteria
    """
    filters = schemas.ProcessingSearchFilters(
        tire_type=tire_type,
        description1=description1,
        description2=description2,
        test_code1=test_code1,
        test_code2=test_code2,
        min_cost=min_cost,
        max_cost=max_cost,
        picture=picture
    )

    processing_list = crud.search_processing(db, filters, skip=skip, limit=limit)
    return processing_list

@router.get("/by-tire/{request_detail_id}", response_model=List[schemas.CutProcessingWithProcessingRead], dependencies=[Depends(auth.require_viewer_role)])
def get_tire_processing(
    request_detail_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    """
    Get all processing data for a specific tire (request detail).

    This endpoint aggregates all processing associated with cuts for a given tire.

    Parameters:
    - request_detail_id: ID of the request detail (tire)

    Returns:
    - List of cut processing with processing details for the specified tire
    """
    tire_processing_list = crud.get_tire_processing_by_request_detail(db, request_detail_id=request_detail_id)
    return tire_processing_list
