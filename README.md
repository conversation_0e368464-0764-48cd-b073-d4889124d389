# CutRequestStudio

**CutRequestStudio** is a comprehensive tire request management application built with a modern tech stack. It provides a complete solution for managing tire requests, processing details, and associated workflows in an industrial environment.

## 🚀 Features

### Core Functionality
- **Request Management**: Create, update, and track tire requests with detailed information
- **Tire Processing**: Manage tire details with processing workflows and cut specifications
- **User Authentication**: Secure JWT-based authentication with role-based access control
- **File Attachments**: Support for file uploads and attachment management
- **Advanced Search**: Powerful filtering and search capabilities across all entities
- **Real-time Updates**: Live data synchronization between frontend and backend

### User Workflows
- **Dashboard**: Overview of requests with status summaries and quick access
- **Request Creation**: Comprehensive form for creating new tire requests
- **Detail Management**: Detailed view and editing of request specifics and tire information
- **Processing Workflow**: Specialized interface for tire processing and cut management with advanced save functionality
- **Tire Processing Detail**: Dedicated page for managing tire-specific processing data with real-time updates
- **Data Persistence**: Robust save operations with error handling and user feedback
- **Reporting**: Generate and export detailed reports

## 🛠 Tech Stack

### Frontend
- **Framework**: Next.js 15.2.3 with React 18
- **Language**: TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context and hooks
- **HTTP Client**: Axios with custom instance configuration
- **UI Components**: Radix UI primitives with custom styling
- **Charts**: Recharts for data visualization

### Backend
- **Framework**: FastAPI with Python
- **Database**: SQLAlchemy ORM with SQLite (development) / PostgreSQL (production)
- **Validation**: Pydantic schemas for data validation
- **Authentication**: JWT tokens with bcrypt password hashing
- **API Documentation**: Auto-generated Swagger/OpenAPI docs

### Development Tools
- **Package Manager**: npm
- **Build Tool**: Next.js with Turbopack
- **Code Quality**: TypeScript strict mode
- **Development Server**: Hot reload for both frontend and backend

## 📁 Project Structure

```
CutRequestStudio/
├── src/                          # Frontend source code
│   ├── app/                      # Next.js app directory
│   │   ├── (dashboard)/          # Dashboard layout group
│   │   │   ├── dashboard/        # Dashboard pages
│   │   │   │   ├── richiesta/    # Request management page
│   │   │   │   ├── dettaglio/    # Request detail page
│   │   │   │   ├── tire-processing-detail/  # Tire processing page
│   │   │   │   └── lavorazioni/  # Processing management page
│   │   │   └── layout.tsx        # Dashboard layout
│   │   ├── login/                # Authentication pages
│   │   └── globals.css           # Global styles
│   ├── components/               # Reusable React components
│   │   ├── auth/                 # Authentication components
│   │   ├── dashboard/            # Dashboard-specific components
│   │   ├── request-detail/       # Request detail components
│   │   ├── tire-processing-detail/  # Tire processing components
│   │   └── ui/                   # Base UI components (shadcn/ui)
│   ├── contexts/                 # React contexts
│   ├── hooks/                    # Custom React hooks
│   ├── lib/                      # Utility libraries
│   ├── services/                 # API service layer
│   └── types/                    # TypeScript type definitions
├── backend/                      # Backend source code
│   ├── routers/                  # FastAPI route handlers
│   │   ├── user.py              # User management endpoints
│   │   ├── request.py           # Request management endpoints
│   │   ├── tire.py              # Tire management endpoints
│   │   ├── request_detail_cut.py # Cut management endpoints
│   │   └── cut_processing.py    # Processing management endpoints
│   ├── models.py                # SQLAlchemy database models
│   ├── schemas.py               # Pydantic validation schemas
│   ├── crud.py                  # Database operations
│   ├── auth.py                  # Authentication logic
│   ├── database.py              # Database configuration
│   └── main.py                  # FastAPI application entry point
├── docs/                        # Documentation
└── app.db                       # SQLite database (development)
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+ and npm
- **Python** 3.8+ with pip
- **Git** for version control

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CutRequestStudio
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Install backend dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   cd ..
   ```

4. **Initialize the database**
   ```bash
   cd backend
   python create_db.py
   cd ..
   ```

### Running the Application

#### Option 1: Quick Start (Recommended)
Use the provided batch script to start both services:
```bash
startapp.bat
```

#### Option 2: Manual Start
Start services separately:

**Backend (Terminal 1):**
```bash
python -m uvicorn backend.main:app --reload --port 8000
```

**Frontend (Terminal 2):**
```bash
npm run dev
```

### Access Points
- **Frontend Application**: http://localhost:9002
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Alternative API Docs**: http://localhost:8000/redoc

## 🔐 Authentication

The application uses JWT-based authentication with role-based access control:

- **Roles**: `admin`, `editor`, `viewer`
- **Default Admin**: Create through the API or database seeding
- **Token Expiry**: 30 minutes (configurable)

### User Roles
- **Admin**: Full access to all features and user management
- **Editor**: Can create, edit, and delete requests and related data
- **Viewer**: Read-only access to all data

## 📊 API Documentation

### Core Endpoints

#### Authentication
- `POST /api/v1/users/login` - User authentication
- `GET /api/v1/users/me` - Get current user info
- `POST /api/v1/users/` - Create new user (admin only)

#### Requests
- `GET /api/v1/requests/` - List all requests with filtering
- `GET /api/v1/requests/{id}` - Get specific request
- `POST /api/v1/requests/` - Create new request
- `PUT /api/v1/requests/{id}` - Update request
- `DELETE /api/v1/requests/{id}` - Delete request
- `GET /api/v1/requests/{id}/tires` - Get tires for request

#### Request Details (Tires)
- `GET /api/v1/requests/details/{detail_id}` - Get specific tire detail
- `PUT /api/v1/requests/details/{detail_id}` - Update tire detail
- `DELETE /api/v1/requests/details/{detail_id}` - Delete tire detail

#### Tires
- `GET /api/v1/tires/` - List all tires with filtering
- `GET /api/v1/tires/{id}` - Get specific tire
- `POST /api/v1/tires/` - Create new tire
- `PUT /api/v1/tires/{id}` - Update tire
- `DELETE /api/v1/tires/{id}` - Delete tire

#### Cut Processing
- `GET /api/v1/cut-processing/` - List cut processing associations
- `GET /api/v1/cut-processing/by-cut/{cut_id}` - Get processing for specific cut
- `GET /api/v1/cut-processing/by-tire/{request_detail_id}` - Get all processing for a specific tire
- `POST /api/v1/cut-processing/` - Create cut processing association
- `PUT /api/v1/cut-processing/{id}` - Update cut processing (quantity and notes only)
- `DELETE /api/v1/cut-processing/{id}` - Delete cut processing
- `GET /api/v1/cut-processing/processing/search` - Search processing with filters

#### Request Detail Cuts
- `GET /api/v1/request-detail-cuts/` - List all cuts
- `GET /api/v1/request-detail-cuts/{id}` - Get specific cut
- `POST /api/v1/request-detail-cuts/` - Create new cut
- `PUT /api/v1/request-detail-cuts/{id}` - Update cut
- `DELETE /api/v1/request-detail-cuts/{id}` - Delete cut
- `GET /api/v1/request-detail-cuts/by-request-detail/{detail_id}` - Get cuts for request detail

### Data Models

#### Request
```typescript
interface Request {
  id: string;
  request_by: string;
  project_no: string;
  pid_project?: string;
  tire_size: string;
  request_date: string;
  target_date: string;
  status: string;
  type: string;
  total_n?: number;
  destination?: string;
  internal?: boolean;
  wish_date?: string;
  num_pneus_set?: number;
  num_pneus_set_unit?: string;
  in_charge_of?: string;
  note?: string;
  attachments: Attachment[];
  request_details: RequestDetail[];
}
```

#### RequestDetail (Tire)
```typescript
interface RequestDetail {
  id: string;
  request_id: string;
  tug_number: string;
  section: string;
  project_number: string;
  spec_number: string;
  tire_size: string;
  pattern: string;
  note?: string;
  disposition: string;
  process_number: number;
}
```

#### Processing
```typescript
interface Processing {
  id: string;
  tire_type: string;
  description1: string;
  description2: string;
  test_code1: string;
  test_code2: string;
  cost: string;
  picture: boolean;
}
```

## 🗄️ Database Schema

The application uses SQLAlchemy ORM with the following main entities:

- **User**: User accounts with role-based permissions
- **Request**: Main tire request entity
- **RequestDetail**: Individual tire specifications within a request
- **Attachment**: File attachments linked to requests
- **Tire**: General tire catalog/inventory
- **Processing**: Available processing operations
- **RequestDetailCut**: Cut specifications for tire details
- **CutProcessing**: Association between cuts and processing operations

### Relationships
- Request → RequestDetail (One-to-Many)
- Request → Attachment (One-to-Many)
- RequestDetail → RequestDetailCut (One-to-Many)
- RequestDetailCut → CutProcessing (One-to-Many)
- Processing → CutProcessing (One-to-Many)

## 🔧 Development

### Frontend Development
```bash
# Start development server with hot reload
npm run dev

# Type checking
npm run typecheck

# Build for production
npm run build

# Start production server
npm start
```

### Backend Development
```bash
# Start with auto-reload
python -m uvicorn backend.main:app --reload --port 8000

# Run with specific host
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8000

# Database operations
cd backend
python create_db.py          # Initialize database
python populate_db.py        # Seed with sample data
```

### Environment Configuration

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api/v1
```

#### Backend (.env)
```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///./app.db
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 🚀 Deployment

### Production Considerations

1. **Database**: Migrate from SQLite to PostgreSQL or MySQL
2. **Environment Variables**: Set production values for all configuration
3. **CORS**: Update allowed origins for production domains
4. **Security**: Use strong secret keys and enable HTTPS
5. **Monitoring**: Implement logging and error tracking

### Docker Deployment (Recommended)
```dockerfile
# Example Dockerfile structure
FROM node:18-alpine AS frontend
# Frontend build steps...

FROM python:3.9-slim AS backend
# Backend setup steps...
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Tire Update 404 Error
**Problem**: Getting 404 errors when updating tires in the detail page.
**Solution**: This was fixed by implementing proper CRUD functions for request detail updates and correcting the field mapping between frontend and backend schemas.

#### 2. Tire Processing Detail Navigation Error
**Problem**: "Tire not found" error when navigating from dettaglio to tire-processing-detail page.
**Solution**: Fixed data structure inconsistency in API response handling. The issue was accessing `.data` property when the response was already unwrapped.

#### 3. CORS Issues
**Problem**: Cross-origin requests blocked.
**Solution**: Ensure CORS middleware is properly configured in `backend/main.py` with correct origins.

#### 4. Authentication Token Expiry
**Problem**: Frequent login prompts.
**Solution**: Check token expiry settings in `backend/auth.py` and implement token refresh mechanism.

#### 5. Database Connection Issues
**Problem**: SQLite database locked or connection errors.
**Solution**: Ensure proper session management and consider migrating to PostgreSQL for production.

### Debug Mode
Enable debug logging by setting environment variables:
```bash
# Backend
export FASTAPI_DEBUG=true

# Frontend
export NODE_ENV=development
```

## 📝 Recent Improvements

### Version 1.2.0 (Latest)
- ✅ **Enhanced Tire Processing Save Functionality**: Implemented robust save operations for tire processing data with automatic data refresh
- ✅ **Read-Only Field Management**: Properly distinguished between editable (quantity) and read-only (descriptions, codes, prices) fields in processing forms
- ✅ **Data Persistence Improvements**: Fixed issue where processing data disappeared after save operations
- ✅ **Material Design Compliance**: Enhanced UI with proper visual distinction between editable and read-only fields
- ✅ **Error Handling & User Feedback**: Comprehensive error handling with detailed user feedback and toast notifications
- ✅ **State Management Optimization**: Improved state synchronization between table and form views

### Version 1.1.0
- ✅ **Fixed tire update 404 errors**: Implemented proper `update_request_detail` CRUD function with correct schema mapping
- ✅ **Fixed tire processing navigation**: Resolved data structure inconsistency in API response handling
- ✅ **Enhanced error handling**: Improved error messages and debugging information
- ✅ **Updated API documentation**: Added missing endpoints and improved schema definitions
- ✅ **Improved field mapping**: Better handling of camelCase/snake_case conversion between frontend and backend

### Previous Versions
- **v1.0.0**: Initial release with core functionality
- **v0.9.0**: Beta release with authentication and basic CRUD operations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript strict mode for frontend
- Use Pydantic models for all API schemas
- Implement proper error handling
- Add tests for new features
- Update documentation for API changes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the [API Documentation](http://localhost:8000/docs) when running locally
- Review the troubleshooting section above
- Create an issue in the repository for bugs or feature requests

---

**Built with ❤️ for efficient tire request management**
