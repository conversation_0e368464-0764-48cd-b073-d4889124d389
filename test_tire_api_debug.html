<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tire API Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Tire API Debug</h1>
        <p>Questo test verifica direttamente l'API dei pneumatici per capire il problema dei campi vuoti.</p>

        <div class="section">
            <h2>Test API Endpoints</h2>
            <button class="button" onclick="testTireAPI()">Test /tires API</button>
            <button class="button" onclick="testBackendHealth()">Test Backend Health</button>
            <button class="button" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="section">
            <h2>Risultati API</h2>
            <div id="results">
                <p>Clicca sui pulsanti sopra per testare l'API...</p>
            </div>
        </div>

        <div class="section">
            <h2>Log di Debug</h2>
            <div id="debugLog" class="log">
                Pronto per il test...
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = 'Log cleared...\n';
            document.getElementById('results').innerHTML = '<p>Risultati cleared...</p>';
        }

        async function testBackendHealth() {
            log("=== TEST BACKEND HEALTH ===");
            try {
                const response = await fetch('http://localhost:8000/docs');
                log(`Backend health check: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    log("Backend is running!", 'success');
                } else {
                    log("Backend might not be running or accessible", 'error');
                }
            } catch (error) {
                log(`Backend health check failed: ${error.message}`, 'error');
                log("Possible issues: Backend not running, CORS, or network error", 'error');
            }
        }

        async function testTireAPI() {
            log("=== TEST TIRE API ===");

            // Test different endpoints
            const endpoints = [
                'http://localhost:8000/api/v1/tires',
                'http://localhost:8000/api/v1/tires?limit=5',
                'http://localhost:8000/api/v1/tires?limit=3&owner=Bridgestone'
            ];

            for (const endpoint of endpoints) {
                log(`Testing endpoint: ${endpoint}`);
                try {
                    const response = await fetch(endpoint, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });

                    log(`Response status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                    if (response.ok) {
                        const data = await response.json();
                        log(`Response data type: ${Array.isArray(data) ? 'Array' : typeof data}`, 'success');
                        log(`Number of items: ${Array.isArray(data) ? data.length : 'N/A'}`, 'success');

                        if (Array.isArray(data) && data.length > 0) {
                            log("First tire data:", 'success');
                            log(JSON.stringify(data[0], null, 2), 'success');

                            // Check for empty fields
                            const firstTire = data[0];
                            const emptyFields = [];
                            ['tugNo', 'tug_no', 'specNo', 'spec_no', 'projectNo', 'project_no'].forEach(field => {
                                if (!firstTire[field] || firstTire[field] === '' || firstTire[field] === null) {
                                    emptyFields.push(field);
                                }
                            });

                            if (emptyFields.length > 0) {
                                log(`PROBLEMA TROVATO! Campi vuoti: ${emptyFields.join(', ')}`, 'error');
                            } else {
                                log("Tutti i campi sembrano popolati correttamente", 'success');
                            }

                            // Display results in table
                            displayTireResults(data.slice(0, 5));
                        } else {
                            log("No tire data returned", 'error');
                        }
                    } else {
                        const errorText = await response.text();
                        log(`Error response: ${errorText}`, 'error');
                    }
                } catch (error) {
                    log(`Fetch error for ${endpoint}: ${error.message}`, 'error');
                }
                log("---");
            }
        }

        function displayTireResults(tires) {
            const resultsDiv = document.getElementById('results');

            if (!tires || tires.length === 0) {
                resultsDiv.innerHTML = '<p class="error">No tire data to display</p>';
                return;
            }

            let html = '<h3>Tire Data from API</h3>';
            html += '<table>';
            html += '<thead><tr>';
            html += '<th>ID</th><th>TUG No</th><th>Spec No</th><th>Size</th><th>Owner</th><th>Pattern</th><th>Project No</th><th>Location</th>';
            html += '</tr></thead>';
            html += '<tbody>';

            tires.forEach(tire => {
                html += '<tr>';
                html += `<td>${tire.id || 'N/A'}</td>`;
                html += `<td style="color: ${tire.tugNo || tire.tug_no ? 'green' : 'red'}">${tire.tugNo || tire.tug_no || 'EMPTY'}</td>`;
                html += `<td style="color: ${tire.specNo || tire.spec_no ? 'green' : 'red'}">${tire.specNo || tire.spec_no || 'EMPTY'}</td>`;
                html += `<td>${tire.size || 'N/A'}</td>`;
                html += `<td>${tire.owner || 'N/A'}</td>`;
                html += `<td>${tire.pattern || 'N/A'}</td>`;
                html += `<td style="color: ${tire.projectNo || tire.project_no ? 'green' : 'red'}">${tire.projectNo || tire.project_no || 'EMPTY'}</td>`;
                html += `<td>${tire.location || 'N/A'}</td>`;
                html += '</tr>';
            });

            html += '</tbody></table>';
            resultsDiv.innerHTML = html;
        }

        // Auto-run tests on page load
        window.onload = function() {
            log("Page loaded. Ready for testing.");
            log("Tip: Check if backend is running on http://localhost:8000");
        };
    </script>
</body>
</html>
