"use client";

import * as React from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area";
import { ArrowLeft, PlusCircle, Trash2 } from "lucide-react";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";
import type { TireProcessingItem } from "@/types";
import { cn } from "@/lib/utils";

interface ProcessingListSectionProps {
  processings: TireProcessingItem[];
  selectedProcessingId: string | null;
  onRowClick: (id: string) => void;
  onAction: (action: "back" | "add" | "delete") => void;
  onTableInputChange: (processingId: string, field: keyof TireProcessingItem, value: any) => void;
}

export function ProcessingListSection({
  processings,
  selectedProcessingId,
  onRowClick,
  onAction,
  onTableInputChange,
}: ProcessingListSectionProps) {

  const handleCellInputChange = (
    processingId: string,
    field: keyof TireProcessingItem,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value: string | number = e.target.value;
    // Only handle the 'n' field since other fields are now read-only
    if (field === 'n') {
      const numValue = parseInt(value, 10);
      value = isNaN(numValue) ? 0 : numValue;
    }
    onTableInputChange(processingId, field, value);
  };

  return (
    <section aria-labelledby="processing-list-heading" className="space-y-6">
      <div className="flex items-center justify-between bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-100">
        <div className="flex items-center gap-3">
          <div className="w-2 h-8 bg-green-500 rounded-full"></div>
          <div>
            <h2 id="processing-list-heading" className="text-xl font-semibold text-gray-900">
              Associated Processings
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {processings.length} {processings.length === 1 ? 'processing' : 'processings'} configured
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAction("back")}
            aria-label="Go back"
            className="rounded-md border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAction("add")}
            className="text-green-600 border-green-300 hover:bg-green-50 hover:text-green-700 hover:border-green-400 rounded-md transition-colors"
            aria-label="Add new processing"
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Add
          </Button>
          <DeleteButton
            onClick={() => onAction("delete")}
            disabled={!selectedProcessingId}
            title="Confirm Processing Deletion"
            description="Are you sure you want to delete this processing? This action cannot be undone."
            itemType="processing"
            size="sm"
            className="rounded-md border-red-300 hover:border-red-400 hover:bg-red-50 text-red-600 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          />
        </div>
      </div>
      <div className="rounded-lg bg-card border shadow-sm">
        <ScrollArea className="h-72">
          <Table>
            <TableHeader className="sticky top-0 bg-muted/30 z-10">
              <TableRow className="border-b">
                <TableHead className="font-semibold text-foreground">Description 1</TableHead>
                <TableHead className="font-semibold text-foreground">Description 2</TableHead>
                <TableHead className="w-[100px] font-semibold text-foreground text-right">Price</TableHead>
                <TableHead className="font-semibold text-foreground">Tyre Type</TableHead>
                <TableHead className="w-[120px] font-semibold text-foreground">Code1</TableHead>
                <TableHead className="w-[120px] font-semibold text-foreground">Code2</TableHead>
                <TableHead className="w-[80px] font-semibold text-foreground text-center">N°</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {processings.map((proc) => (
              <TableRow
                key={proc.id}
                data-state={selectedProcessingId === proc.id ? "selected" : ""}
                onClick={() => onRowClick(proc.id)}
                className={cn(
                  "cursor-pointer transition-colors border-b border-border/50",
                  selectedProcessingId === proc.id
                    ? "bg-green-50 hover:bg-green-100 border-green-200"
                    : "hover:bg-muted/50"
                )}
                aria-selected={selectedProcessingId === proc.id}
              >
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-gray-50 rounded-md font-medium text-gray-700">
                    {proc.description1}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-gray-50 rounded-md text-gray-600">
                    {proc.description2}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-8 flex items-center justify-end text-xs px-3 py-2 bg-blue-50 rounded-md font-medium text-blue-700">
                    €{proc.price}
                  </div>
                </TableCell>
                <TableCell className="font-medium text-gray-700">{proc.tyreType}</TableCell>
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-gray-50 rounded-md text-gray-600 font-mono">
                    {proc.code1}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-gray-50 rounded-md text-gray-600 font-mono">
                    {proc.code2}
                  </div>
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    value={proc.n}
                    onChange={(e) => handleCellInputChange(proc.id, 'n', e)}
                    className="h-8 text-xs text-center bg-white border-gray-300 rounded-md font-medium focus:border-blue-400 focus:ring-1 focus:ring-blue-200"
                    onClick={(e) => e.stopPropagation()}
                  />
                </TableCell>
              </TableRow>
            ))}
            {processings.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-12">
                  <div className="flex flex-col items-center gap-3 text-muted-foreground">
                    <PlusCircle className="h-12 w-12 text-gray-300" />
                    <div>
                      <p className="font-medium text-gray-600">No processings found</p>
                      <p className="text-sm text-gray-500">This tire doesn't have any processing operations yet.</p>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        </ScrollArea>
      </div>
    </section>
  );
}
