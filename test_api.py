#!/usr/bin/env python3
"""
Test script to verify the /api/tires endpoint is working correctly
"""

import requests
import json

# Test the API endpoint
def test_tires_api():
    base_url = "http://localhost:8000"

    # First, let's try to login to get a token
    login_data = {
        "username": "<EMAIL>",
        "password": "password123"  # Default password from populate script
    }

    try:
        # Try to login
        print("🔐 Attempting to login...")
        login_response = requests.post(f"{base_url}/api/v1/users/login", data=login_data)
        print(f"Login status: {login_response.status_code}")

        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get("access_token")
            print(f"✅ Login successful, token: {token[:20]}...")

            # Test the tires endpoint with authentication
            headers = {"Authorization": f"Bearer {token}"}
            print("\n🛞 Testing /api/v1/tires endpoint...")
            tires_response = requests.get(f"{base_url}/api/v1/tires", headers=headers)
            print(f"Tires API status: {tires_response.status_code}")

            if tires_response.status_code == 200:
                tires_data = tires_response.json()
                print(f"✅ Success! Found {len(tires_data)} tires")
                print("\nSample tires:")
                for i, tire in enumerate(tires_data[:3]):
                    print(f"  {i+1}. {tire.get('id')}: {tire.get('tug_no')} - {tire.get('owner')} {tire.get('pattern')}")
                return True
            else:
                print(f"❌ Tires API failed: {tires_response.text}")
                return False
        else:
            print(f"❌ Login failed: {login_response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 8000?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Tire Management API")
    print("=" * 40)
    success = test_tires_api()
    print("\n" + "=" * 40)
    if success:
        print("✅ API test passed! The tire management system is working correctly.")
    else:
        print("❌ API test failed. Check the backend server and database.")
