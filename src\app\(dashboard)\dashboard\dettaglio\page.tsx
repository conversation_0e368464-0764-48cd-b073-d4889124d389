"use client";

import * as React from "react";
import { useRouter, useSearchPara<PERSON> } from 'next/navigation';
import { RequestDisplay } from "@/components/request-detail/request-display";
import { TiresSection } from "@/components/request-detail/tires-section";
import { TireForm } from "@/components/request-detail/tire-form";
import { CutsSection } from "@/components/request-detail/cuts-section";
import { DetailPageActions } from "@/components/request-detail/page-actions";
import { ProcessingSearchDialog } from "@/components/request-detail/processing-search-dialog";
import { DettaglioReportDialog } from "@/components/request-detail/dettaglio-report-dialog";
import { CutManagementDialog } from "@/components/request-detail/cut-management-dialog";
import { ProcessingManagementDialog } from "@/components/request-detail/processing-management-dialog";
import { DepartmentSelectionDialog } from "@/components/request-detail/department-selection-dialog";
import { useToast } from "@/hooks/use-toast";
import type { AppRequest, Tire, Tire<PERSON>orm<PERSON><PERSON>, DialogProcessing, RequestDetailCut } from "@/types";
import { initialTireFormData } from "@/types";
import {
  getRequest,
  getRequestTires,
  saveRequest,
  copySendRequest,
} from "@/services/requestService";
import { updateRequestDetail } from "@/services/requestDetailService";
import { getMultipleTireProcessingTotals } from "@/services/tireProcessingService";

export default function DettaglioPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  const [requestData, setRequestData] = React.useState<AppRequest | null>(null);
  const [tires, setTires] = React.useState<Tire[]>([]);
  const [selectedTireId, setSelectedTireId] = React.useState<string | null>(null);
  const [currentTireFormData, setCurrentTireFormData] = React.useState<TireFormData>(initialTireFormData);
  const [isNewTireMode, setIsNewTireMode] = React.useState<boolean>(false);
  const [isProcessingSearchDialogOpen, setIsProcessingSearchDialogOpen] = React.useState<boolean>(false);
  const [isDettaglioReportDialogOpen, setIsDettaglioReportDialogOpen] = React.useState<boolean>(false);
  const [isDepartmentDialogOpen, setIsDepartmentDialogOpen] = React.useState<boolean>(false);
  const [isCutManagementDialogOpen, setIsCutManagementDialogOpen] = React.useState<boolean>(false);
  const [isProcessingManagementDialogOpen, setIsProcessingManagementDialogOpen] = React.useState<boolean>(false);
  const [selectedTireForCuts, setSelectedTireForCuts] = React.useState<Tire | null>(null);
  const [selectedCutForProcessing, setSelectedCutForProcessing] = React.useState<RequestDetailCut | null>(null);
  const [processingNSums, setProcessingNSums] = React.useState<Record<string, number>>({});
  const [loading, setLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string | null>(null);

  // Fetch request and tires data
  React.useEffect(() => {
    const reqId = searchParams.get('requestId');
    if (!reqId) {
      setRequestData(null);
      setTires([]);
      setProcessingNSums({});
      toast({ title: "No Request ID", description: "No requestId provided in URL.", variant: "destructive" });
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch request and tires data
        const [reqRes, tiresRes] = await Promise.all([
          getRequest(reqId),
          getRequestTires(reqId),
        ]);

        setRequestData(reqRes as AppRequest);

        // Map API tires (snake_case) to Tire (camelCase)
        function mapApiTireToTire(apiTire: any): Tire {
          return {
            id: apiTire.id,
            tugNo: apiTire.tug_number,
            section: apiTire.section,
            projectNo: apiTire.project_number,
            specNo: apiTire.spec_number,
            tireSize: apiTire.tire_size,
            pattern: apiTire.pattern,
            note: apiTire.note,
            disposition: apiTire.disposition,
            quantity: apiTire.process_number,
          };
        }

        const mappedTires: Tire[] = Array.isArray(tiresRes)
          ? tiresRes.map(mapApiTireToTire)
          : [];
        setTires(mappedTires);

        // Fetch real processing totals for all tires
        if (mappedTires.length > 0) {
          try {
            const tireIds = mappedTires.map(tire => tire.id);
            const processingTotals = await getMultipleTireProcessingTotals(tireIds);
            setProcessingNSums(processingTotals);
          } catch (processingError) {
            console.error("Failed to fetch processing totals:", processingError);
            // Set default values for processing totals on error
            const defaultTotals = mappedTires.reduce((acc, tire) => {
              acc[tire.id] = 0;
              return acc;
            }, {} as Record<string, number>);
            setProcessingNSums(defaultTotals);
          }
        } else {
          setProcessingNSums({});
        }
      } catch (err: any) {
        setError("Errore nel caricamento dati: " + (err?.message || "Unknown error"));
        setRequestData(null);
        setTires([]);
        setProcessingNSums({});
        toast({ title: "Errore", description: "Impossibile caricare i dati della richiesta.", variant: "destructive" });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [searchParams, toast]);

  const handleRequestIconClick = (action: "report" | "inChargeOf") => {
    if (!requestData) {
      toast({ title: "No Request Data", description: "No request data loaded.", variant: "destructive" });
      return;
    }
    if (action === "report") {
      setIsDettaglioReportDialogOpen(true);
    } else if (action === "inChargeOf") {
      setIsDepartmentDialogOpen(true);
    }
  };

  const handleSelectDepartment = (department: string) => {
    if (requestData) {
      setRequestData(prev => prev ? { ...prev, inChargeOf: department } : null);
      toast({ title: "Department Selected", description: `${department} assigned as 'In Charge Of' for this request.` });
    }
    setIsDepartmentDialogOpen(false);
  };

  const handleTireRowClick = (tireId: string) => {
    setSelectedTireId(tireId);
    setIsNewTireMode(false);
    const selectedTire = tires.find(t => t.id === tireId);
    if (selectedTire) {
      setCurrentTireFormData({
        id: selectedTire.id,
        tugNo: selectedTire.tugNo,
        projectN: selectedTire.projectNo,
        tireSize: selectedTire.tireSize,
        note: selectedTire.note,
        disposition: selectedTire.disposition,
        quantity: selectedTire.quantity,
      });
    }
  };

  const handleTireFormChange = (field: keyof TireFormData, value: any) => {
    setCurrentTireFormData(prev => ({ ...prev, [field]: value }));
  };

  const resetTireForm = () => {
    setSelectedTireId(null);
    setCurrentTireFormData(initialTireFormData);
    setIsNewTireMode(false);
  };

  const handleSaveTire = async () => {
    if (isNewTireMode) {
      const newTire: Tire = {
        id: `T${Math.floor(Math.random() * 1000) + tires.length + 1}`,
        tugNo: currentTireFormData.tugNo || "N/A",
        section: "NEW_DIRECT",
        projectNo: currentTireFormData.projectN || "N/A",
        specNo: `SP-FORM-${Math.floor(Math.random() * 100)}`,
        tireSize: currentTireFormData.tireSize || "N/A",
        pattern: "New Pattern (Form)",
        note: currentTireFormData.note,
        disposition: currentTireFormData.disposition || "AVAILABLE",
        quantity: currentTireFormData.quantity || 1,
      };
      setTires(prev => [...prev, newTire]);
      setProcessingNSums(prevSums => ({ ...prevSums, [newTire.id]: 0 }));
      toast({ title: "Tire Added", description: `New tire ${newTire.tugNo} added directly.` });
    } else if (selectedTireId && currentTireFormData.id) {
      try {
        // Prepare data for API update (snake_case, only allowed fields)
        const updateData = {
          id: currentTireFormData.id,
          tug_no: currentTireFormData.tugNo,
          project_no: currentTireFormData.projectN,
          tire_size: currentTireFormData.tireSize,
          note: currentTireFormData.note,
          disposition: currentTireFormData.disposition,
          quantity: currentTireFormData.quantity
        };

        // Call API to update the tire
        await updateRequestDetail(selectedTireId, updateData);

        // Update local state
        setTires(prevTires => prevTires.map(tire =>
          tire.id === selectedTireId
            ? {
              ...tire,
              tugNo: currentTireFormData.tugNo || tire.tugNo,
              projectNo: currentTireFormData.projectN || tire.projectNo,
              tireSize: currentTireFormData.tireSize || tire.tireSize,
              note: currentTireFormData.note,
              disposition: currentTireFormData.disposition || tire.disposition,
              quantity: currentTireFormData.quantity === undefined ? tire.quantity : currentTireFormData.quantity,
            }
            : tire
        ));

        toast({
          title: "Tire Updated",
          description: `Tire ${currentTireFormData.tugNo || selectedTireId} has been updated.`
        });
      } catch (error) {
        console.error("Failed to update tire:", error);
        toast({
          title: "Update Failed",
          description: "There was an error updating the tire. Please try again.",
          variant: "destructive"
        });
      }
    }
    resetTireForm();
  };


  const handleCancelTireEdit = () => {
    resetTireForm();
    toast({ title: "Tire Edit Cancelled", description: "No changes were saved to the tire." });
  };


  const handleOpenProcessingSearchDialog = () => {
    if (!selectedTireId) {
      toast({ title: "No Tire Selected", description: "Please select a tire from the table to assign processing.", variant: "destructive" });
      return;
    }
    setIsProcessingSearchDialogOpen(true);
  };

  const handleCloseProcessingSearchDialog = () => {
    setIsProcessingSearchDialogOpen(false);
  };

  const handleAddProcessingFromDialog = (selectedProcessing: DialogProcessing[]) => {
    toast({ title: "Processing Added", description: `${selectedProcessing.length} processing types added to tire ${selectedTireId}. The 'N°' in the tire table would update if this were a full implementation.` });
    handleCloseProcessingSearchDialog();
  };

  const handleNavigateToProcessingDetail = () => {
    if (!selectedTireId) {
      toast({ title: "No Tire Selected", description: "Please select a tire from the table to view/assign processing.", variant: "destructive" });
      return;
    }
    if (!requestData?.id) {
      toast({ title: "No Request Data", description: "Request data is not available. Please reload the page.", variant: "destructive" });
      return;
    }
    router.push(`/dashboard/tire-processing-detail?tireId=${selectedTireId}&requestId=${requestData.id}`);
    toast({ title: "Navigation", description: `Navigating to processing details for tire ${selectedTireId}.` });
  };

  const handleCopyTire = () => {
    if (!selectedTireId) {
      toast({ title: "No Tire Selected", description: "Please select a tire to copy.", variant: "destructive" });
      return;
    }
    const tireToCopy = tires.find(t => t.id === selectedTireId);
    if (tireToCopy) {
      const copiedTire: Tire = {
        ...tireToCopy,
        id: `TCOPY-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
        tugNo: `${tireToCopy.tugNo} (Copy)`,
        note: tireToCopy.note ? `${tireToCopy.note} (Copied)` : "(Copied)",
      };
      setTires(prev => [...prev, copiedTire]);
      setProcessingNSums(prevSums => ({ ...prevSums, [copiedTire.id]: prevSums[tireToCopy.id] ?? 0 }));

      setSelectedTireId(copiedTire.id);
      setCurrentTireFormData({
        id: copiedTire.id,
        tugNo: copiedTire.tugNo,
        projectN: copiedTire.projectNo,
        tireSize: copiedTire.tireSize,
        note: copiedTire.note,
        disposition: copiedTire.disposition,
        quantity: copiedTire.quantity,
      });
      setIsNewTireMode(false);
      toast({ title: "Tire Copied", description: `Tire ${tireToCopy.tugNo} copied as ${copiedTire.tugNo}.` });
    }
  };

  const handleManageCuts = (tire: Tire) => {
    setSelectedTireForCuts(tire);
    setIsCutManagementDialogOpen(true);
  };

  const handleCloseCutManagementDialog = () => {
    setIsCutManagementDialogOpen(false);
    setSelectedTireForCuts(null);
  };

  const handleOpenProcessingManagement = (cut: RequestDetailCut) => {
    setSelectedCutForProcessing(cut);
    setIsProcessingManagementDialogOpen(true);
  };

  const handleCloseProcessingManagementDialog = () => {
    setIsProcessingManagementDialogOpen(false);
    setSelectedCutForProcessing(null);
  };

  const handleHomeClick = () => {
    router.push('/dashboard');
    toast({ title: "Navigation", description: "Returning to Dashboard." });
  };

  const handleCopySendClick = async () => {
    if (!requestData?.id) {
      toast({ title: "No Request Data", description: "No request data loaded.", variant: "destructive" });
      return;
    }
    try {
      setLoading(true);
      await copySendRequest(requestData.id);
      toast({ title: "Copy+Send", description: "Copy+Send action completed successfully." });
    } catch (err: any) {
      toast({ title: "Errore Copy+Send", description: err?.message || "Errore sconosciuto", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  const handleSavePageClick = async () => {
    if (!requestData?.id) {
      toast({ title: "No Request Data", description: "No request data loaded.", variant: "destructive" });
      return;
    }
    try {
      setLoading(true);

      // Costruisci manualmente il payload in snake_case
      const payload = {
        id: requestData.id,
        request_by: requestData.requestBy,
        project_no: requestData.projectNo,
        pid_project: requestData.pidProject,
        tire_size: requestData.tireSize,
        request_date: requestData.requestDate ? new Date(requestData.requestDate).toISOString() : null,
        target_date: requestData.targetDate ? new Date(requestData.targetDate).toISOString() : null,
        status: requestData.status,
        type: requestData.type,
        total_n: requestData.totalN,
        destination: requestData.destination,
        internal: requestData.internal,
        wish_date: requestData.wishDate ? new Date(requestData.wishDate).toISOString() : null,
        num_pneus_set: requestData.numPneusSet,
        num_pneus_set_unit: requestData.numPneusSetUnit,
        in_charge_of: requestData.inChargeOf,
        note: requestData.note,
        attachments: [], // Mappa se necessario
        request_details: tires.map(tire => ({
          id: tire.id ?? "",
          request_id: requestData.id ?? "",
          tug_number: tire.tugNo ?? "",
          section: tire.section ?? "",
          project_number: tire.projectNo ?? "",
          spec_number: tire.specNo ?? "",
          tire_size: tire.tireSize ?? "",
          pattern: tire.pattern ?? "",
          note: tire.note ?? "",
          disposition: tire.disposition ?? "AVAILABLE",
          process_number: typeof tire.quantity === "number" ? tire.quantity : 1
        }))
      };

      // Log payload for debugging 422 errors
      console.log("SaveRequest payload:", payload);

      // Usa il service centralizzato per la chiamata API sulla porta corretta
      await saveRequest(requestData.id, payload);

      toast({ title: "Save", description: "Save action completed successfully." });
    } catch (err: any) {
      toast({ title: "Errore Save", description: err?.message || "Errore sconosciuto", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {loading && <div className="text-center text-gray-500">Caricamento dati...</div>}
      {error && <div className="text-center text-red-500">{error}</div>}
      <RequestDisplay request={requestData} onIconClick={handleRequestIconClick} disableinChargeOfButton={true} />
      <TiresSection
        tires={tires}
        selectedTireId={selectedTireId}
        onRowClick={handleTireRowClick}
        onProcessingType={handleNavigateToProcessingDetail}
        onCopyTire={handleCopyTire}
        onManageCuts={handleManageCuts}
        processingNSums={processingNSums}
        onUpdateProcessingSums={setProcessingNSums}
        onUpdateTires={setTires}
      />
      {(selectedTireId || isNewTireMode) && (
        <TireForm
          formData={currentTireFormData}
          onFormChange={handleTireFormChange}
          onSave={handleSaveTire}
          onCancel={handleCancelTireEdit}
          isNewTire={isNewTireMode}
        />
      )}
      {selectedTireId && !isNewTireMode && (
        <CutsSection requestDetailId={selectedTireId} />
      )}
      <DetailPageActions
        onHomeClick={handleHomeClick}
        onCopySendClick={handleCopySendClick}
        onSaveClick={handleSavePageClick}
      />
      {/* Dialogs */}
      <ProcessingSearchDialog
        isOpen={isProcessingSearchDialogOpen}
        onClose={handleCloseProcessingSearchDialog}
        onAddSelectedProcessing={handleAddProcessingFromDialog}
      />
      <DettaglioReportDialog
        isOpen={isDettaglioReportDialogOpen}
        onClose={() => setIsDettaglioReportDialogOpen(false)}
        requestData={requestData}
        tiresData={tires}
      />
      <DepartmentSelectionDialog
        isOpen={isDepartmentDialogOpen}
        onClose={() => setIsDepartmentDialogOpen(false)}
        onSelectDepartment={handleSelectDepartment}
      />
      <CutManagementDialog
        isOpen={isCutManagementDialogOpen}
        onClose={handleCloseCutManagementDialog}
        tire={selectedTireForCuts}
        onOpenProcessingManagement={handleOpenProcessingManagement}
      />
      <ProcessingManagementDialog
        isOpen={isProcessingManagementDialogOpen}
        onClose={handleCloseProcessingManagementDialog}
        cut={selectedCutForProcessing}
      />
    </div>
  );
}
