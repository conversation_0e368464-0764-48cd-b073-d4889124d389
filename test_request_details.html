<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Request Details API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, button {
            padding: 8px;
            margin: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .field-analysis {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .field-item {
            margin: 5px 0;
            padding: 5px;
            background-color: white;
            border-radius: 3px;
        }
        .missing {
            background-color: #f8d7da;
        }
        .present {
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <h1>Test Request Details API</h1>

    <div class="container">
        <div class="input-group">
            <label for="baseUrl">Base URL:</label>
            <input type="text" id="baseUrl" value="http://localhost:8000" style="width: 300px;">
        </div>

        <div class="input-group">
            <label for="requestId">Request ID:</label>
            <input type="text" id="requestId" placeholder="Enter request ID" style="width: 200px;">
        </div>

        <div class="input-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="<EMAIL>" style="width: 200px;">
        </div>

        <div class="input-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="password123" style="width: 200px;">
        </div>

        <button onclick="testLogin()">1. Test Login</button>
        <button onclick="testGetRequests()">2. Get All Requests</button>
        <button onclick="testGetRequest()">3. Get Specific Request</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        let authToken = null;

        function getBaseUrl() {
            return document.getElementById('baseUrl').value.trim();
        }

        function getRequestId() {
            return document.getElementById('requestId').value.trim();
        }

        function addResult(title, content, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="result ${isError ? 'error' : 'success'}">${content}</div>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const baseUrl = getBaseUrl();

            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch(`${baseUrl}/token`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.access_token;
                    addResult('Login Test', `✅ Login successful!\nToken: ${authToken.substring(0, 50)}...`);
                } else {
                    addResult('Login Test', `❌ Login failed!\nStatus: ${response.status}\nError: ${JSON.stringify(data, null, 2)}`, true);
                }
            } catch (error) {
                addResult('Login Test', `❌ Network error: ${error.message}`, true);
            }
        }

        async function testGetRequests() {
            const baseUrl = getBaseUrl();

            if (!authToken) {
                addResult('Get Requests Test', '❌ Please login first!', true);
                return;
            }

            try {
                const response = await fetch(`${baseUrl}/api/v1/requests`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    addResult('Get Requests Test', `✅ Requests fetched successfully!\nCount: ${data.length}\nFirst few requests:\n${JSON.stringify(data.slice(0, 3), null, 2)}`);

                    // Auto-populate first request ID if available
                    if (data.length > 0 && data[0].id) {
                        document.getElementById('requestId').value = data[0].id;
                    }
                } else {
                    addResult('Get Requests Test', `❌ Failed to fetch requests!\nStatus: ${response.status}\nError: ${JSON.stringify(data, null, 2)}`, true);
                }
            } catch (error) {
                addResult('Get Requests Test', `❌ Network error: ${error.message}`, true);
            }
        }

        async function testGetRequest() {
            const baseUrl = getBaseUrl();
            const requestId = getRequestId();

            if (!authToken) {
                addResult('Get Request Test', '❌ Please login first!', true);
                return;
            }

            if (!requestId) {
                addResult('Get Request Test', '❌ Please enter a request ID!', true);
                return;
            }

            try {
                const response = await fetch(`${baseUrl}/api/v1/requests/${requestId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    // Analyze the data structure
                    const analysis = analyzeRequestData(data);

                    addResult('Get Request Test', `✅ Request fetched successfully!\nRequest ID: ${requestId}\n\nRaw Data:\n${JSON.stringify(data, null, 2)}\n\n${analysis}`);
                } else {
                    addResult('Get Request Test', `❌ Failed to fetch request!\nStatus: ${response.status}\nError: ${JSON.stringify(data, null, 2)}`, true);
                }
            } catch (error) {
                addResult('Get Request Test', `❌ Network error: ${error.message}`, true);
            }
        }

        function analyzeRequestData(data) {
            const requiredFields = [
                'id', 'request_by', 'project_no', 'tire_size',
                'request_date', 'target_date', 'status', 'type'
            ];

            const optionalFields = [
                'pid_project', 'total_n', 'destination', 'internal',
                'wish_date', 'num_pneus_set', 'num_pneus_set_unit',
                'in_charge_of', 'note', 'attachments'
            ];

            let analysis = "FIELD ANALYSIS:\n\n";

            analysis += "Required Fields:\n";
            requiredFields.forEach(field => {
                const value = data[field];
                const status = (value !== null && value !== undefined && value !== '') ? '✅' : '❌';
                analysis += `${status} ${field}: ${JSON.stringify(value)}\n`;
            });

            analysis += "\nOptional Fields:\n";
            optionalFields.forEach(field => {
                const value = data[field];
                const status = (value !== null && value !== undefined && value !== '') ? '✅' : '⚪';
                analysis += `${status} ${field}: ${JSON.stringify(value)}\n`;
            });

            // Check for missing required fields
            const missingRequired = requiredFields.filter(field => {
                const value = data[field];
                return value === null || value === undefined || value === '';
            });

            if (missingRequired.length > 0) {
                analysis += `\n⚠️  MISSING REQUIRED FIELDS: ${missingRequired.join(', ')}\n`;
            } else {
                analysis += "\n✅ All required fields are present!\n";
            }

            return analysis;
        }
    </script>
</body>
</html>
