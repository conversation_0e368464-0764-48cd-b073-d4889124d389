# CutRequestStudio Development Guide

This guide provides comprehensive information for developers working on the CutRequestStudio project, including the latest tire processing functionality and save operations.

## Development Environment Setup

### Prerequisites
- **Node.js** 18+ with npm
- **Python** 3.8+ with pip
- **Git** for version control
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - Python
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets

### Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd CutRequestStudio

# Install frontend dependencies
npm install

# Install backend dependencies
cd backend
pip install -r requirements.txt
cd ..

# Initialize database
cd backend
python create_db.py
python populate_db.py  # Optional: Add sample data
cd ..
```

### Development Workflow
```bash
# Start both services (recommended)
./startapp.bat

# Or start separately:
# Terminal 1 - Backend
python -m uvicorn backend.main:app --reload --port 8000

# Terminal 2 - Frontend
npm run dev
```

## Project Architecture

### Frontend Architecture (Next.js + React)

#### Directory Structure
```
src/
├── app/                          # Next.js App Router
│   ├── (dashboard)/              # Route group for dashboard
│   │   ├── dashboard/            # Dashboard pages
│   │   │   ├── richiesta/        # Request management
│   │   │   ├── dettaglio/        # Request details
│   │   │   ├── tire-processing-detail/  # Tire processing
│   │   │   └── lavorazioni/      # Processing management
│   │   └── layout.tsx            # Dashboard layout
│   ├── login/                    # Authentication pages
│   ├── globals.css               # Global styles
│   └── layout.tsx                # Root layout
├── components/                   # Reusable components
│   ├── auth/                     # Authentication components
│   ├── dashboard/                # Dashboard-specific components
│   ├── request-detail/           # Request detail components
│   ├── tire-processing-detail/   # Tire processing components
│   └── ui/                       # Base UI components (shadcn/ui)
├── contexts/                     # React contexts
├── hooks/                        # Custom React hooks
├── lib/                          # Utility libraries
├── services/                     # API service layer
└── types/                        # TypeScript definitions
```

#### Key Components

**Layout Components:**
- `DashboardLayout`: Main dashboard wrapper with sidebar navigation
- `DashboardAppBar`: Top navigation bar with user info and logout

**Page Components:**
- `RichiestaPage`: Request management and creation
- `DettaglioPage`: Request details and tire management
- `TireProcessingDetailPage`: Tire processing workflow
- `LavorazioniPage`: Processing management

**Feature Components:**
- `RequestDisplay`: Shows request information
- `TiresSection`: Tire listing and management
- `TireForm`: Tire creation/editing form
- `ProcessingListSection`: Processing operations list

#### State Management
- **Authentication**: React Context (`AuthContext`)
- **Local State**: React hooks (`useState`, `useEffect`)
- **Form State**: React Hook Form for complex forms
- **API State**: Direct API calls with loading/error states

#### Styling
- **Framework**: Tailwind CSS
- **Components**: shadcn/ui (Radix UI primitives)
- **Icons**: Lucide React
- **Theme**: Custom design system with Material Design influence

### Backend Architecture (FastAPI + SQLAlchemy)

#### Directory Structure
```
backend/
├── routers/                      # API route handlers
│   ├── user.py                   # User management
│   ├── request.py                # Request operations
│   ├── tire.py                   # Tire management
│   ├── request_detail_cut.py     # Cut operations
│   └── cut_processing.py         # Processing operations
├── models.py                     # SQLAlchemy database models
├── schemas.py                    # Pydantic validation schemas
├── crud.py                       # Database operations
├── auth.py                       # Authentication logic
├── database.py                   # Database configuration
└── main.py                       # FastAPI application
```

#### Database Models

**Core Entities:**
- `User`: User accounts with role-based permissions
- `Request`: Main tire request entity
- `RequestDetail`: Individual tire specifications
- `Attachment`: File attachments for requests
- `Tire`: General tire catalog
- `Processing`: Available processing operations
- `RequestDetailCut`: Cut specifications
- `CutProcessing`: Cut-processing associations

**Relationships:**
```python
# One-to-Many relationships
Request → RequestDetail
Request → Attachment
RequestDetail → RequestDetailCut

# Many-to-Many relationships
RequestDetailCut ↔ Processing (via CutProcessing)
```

#### API Design Patterns

**RESTful Endpoints:**
- `GET /api/v1/{resource}/` - List resources
- `GET /api/v1/{resource}/{id}` - Get specific resource
- `POST /api/v1/{resource}/` - Create resource
- `PUT /api/v1/{resource}/{id}` - Update resource
- `DELETE /api/v1/{resource}/{id}` - Delete resource

**Authentication:**
- JWT token-based authentication
- Role-based access control (admin, editor, viewer)
- Protected endpoints with dependency injection

**Data Validation:**
- Pydantic schemas for request/response validation
- Automatic API documentation generation
- Type safety throughout the application

## Development Patterns

### Frontend Patterns

#### Service Layer Pattern
```typescript
// services/requestService.ts
export const getRequests = async (params?: Record<string, any>) => {
  try {
    const response = await axiosInstance.get<AppRequest[]>('/requests', { params });
    return response.data;
  } catch (error) {
    console.error('[requestService] Error fetching requests:', error);
    throw error;
  }
};
```

#### Component Composition
```typescript
// Composable components with clear responsibilities
<RequestDisplay request={requestData} onIconClick={handleIconClick} />
<TiresSection
  tires={tires}
  onRowClick={handleTireRowClick}
  onAddTire={handleAddTire}
/>
<TireForm
  formData={formData}
  onFormChange={handleFormChange}
  onSave={handleSave}
/>
```

#### Custom Hooks
```typescript
// hooks/useRequestDetails.ts
export const useRequestDetails = (requestId: string) => {
  const [request, setRequest] = useState<AppRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Hook logic...

  return { request, loading, error, refetch };
};
```

### Backend Patterns

#### Dependency Injection
```python
# Consistent dependency injection pattern
@router.get("/", response_model=List[schemas.RequestSummary])
def read_requests(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user)
):
    return crud.get_requests_summary(db, skip=skip, limit=limit)
```

#### CRUD Operations
```python
# Standardized CRUD operations
def get_request(db: Session, request_id: str):
    return db.query(models.Request).filter(models.Request.id == request_id).first()

def create_request(db: Session, request: schemas.RequestCreate):
    db_request = models.Request(**request.dict())
    db.add(db_request)
    db.commit()
    db.refresh(db_request)
    return db_request
```

#### Schema Validation
```python
# Pydantic schemas for validation
class RequestCreate(BaseModel):
    id: str
    request_by: str
    project_no: str
    tire_size: str
    request_date: datetime
    target_date: datetime
    status: str
    type: str
```

## Testing Strategy

### Frontend Testing
```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom jest

# Test structure
src/
├── __tests__/                    # Test files
│   ├── components/               # Component tests
│   ├── services/                 # Service tests
│   └── utils/                    # Utility tests
```

### Backend Testing
```bash
# Install testing dependencies
pip install pytest pytest-asyncio httpx

# Test structure
backend/
├── tests/                        # Test files
│   ├── test_auth.py             # Authentication tests
│   ├── test_crud.py             # CRUD operation tests
│   └── test_routers.py          # API endpoint tests
```

## Code Quality

### Frontend Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **Naming**: camelCase for variables, PascalCase for components

### Backend Standards
- **Type Hints**: All functions should have type annotations
- **Docstrings**: Document all public functions
- **PEP 8**: Python style guide compliance
- **Naming**: snake_case for variables and functions

### Git Workflow
```bash
# Feature development workflow
git checkout -b feature/new-feature
git add .
git commit -m "feat: add new feature description"
git push origin feature/new-feature
# Create pull request
```

## Debugging

### Frontend Debugging
```typescript
// Debug API calls
console.log('[Service] Request:', requestData);
console.log('[Service] Response:', response.data);

// React Developer Tools
// Network tab for API inspection
// Console for error tracking
```

### Backend Debugging
```python
# Add logging
import logging
logger = logging.getLogger(__name__)

@router.post("/")
def create_request(request: schemas.RequestCreate, db: Session = Depends(get_db)):
    logger.info(f"Creating request: {request.dict()}")
    # Function logic...
```

## Performance Optimization

### Frontend Optimization
- **Code Splitting**: Automatic with Next.js
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: `npm run build` with analysis
- **Caching**: API response caching strategies

### Backend Optimization
- **Database Indexing**: Add indexes for frequently queried fields
- **Query Optimization**: Use select_related and prefetch_related
- **Connection Pooling**: Configure database connection pools
- **Caching**: Implement Redis for frequently accessed data

## Common Development Tasks

### Adding a New API Endpoint
1. Define Pydantic schema in `schemas.py`
2. Add database model in `models.py` (if needed)
3. Implement CRUD operations in `crud.py`
4. Create router endpoint in appropriate router file
5. Add frontend service function
6. Update TypeScript types

### Adding a New Page
1. Create page component in `src/app/`
2. Add navigation link in dashboard layout
3. Implement required components
4. Add API service calls
5. Update routing and navigation

### Database Schema Changes
1. Modify models in `models.py`
2. Update Pydantic schemas
3. Create migration script (if needed)
4. Update CRUD operations
5. Test with sample data

### Implementing Tire Processing Features
1. **Backend API**: Add endpoints in `backend/routers/cut_processing.py`
2. **Service Layer**: Create/update service in `src/services/tireProcessingService.ts`
3. **Data Transformation**: Map backend response to frontend types
4. **Component State**: Manage editable vs read-only fields
5. **Save Operations**: Implement with data refresh and error handling
6. **UI Components**: Use Material Design patterns for field distinction

#### Example: Adding New Processing Field
```typescript
// 1. Update backend schema
class CutProcessingUpdate(BaseModel):
    quantity: Optional[int] = None
    notes: Optional[str] = None
    new_field: Optional[str] = None  # Add new field

// 2. Update frontend types
interface TireProcessingItem {
    id: string;
    quantity: number;
    notes?: string;
    newField?: string;  // Add new field
    // ... other fields
}

// 3. Update component to handle new field
const handleProcessingTableInputChange = (processingId: string, field: keyof TireProcessingItem, value: any) => {
    // Only allow modification of editable fields
    if (field === 'quantity' || field === 'notes' || field === 'newField') {
        setProcessings(prev =>
            prev.map(p => p.id === processingId ? { ...p, [field]: value } : p)
        );
        setModifiedProcessingIds(prev => new Set(prev).add(processingId));
    }
};
```

## Recent Fixes and Improvements

### Tire Processing Save Functionality (v1.2.0)
- **Enhancement**: Implemented robust save operations for tire processing data
- **Features**:
  - Automatic data refresh after successful saves
  - Read-only field management (descriptions, codes, prices)
  - Editable quantity fields with proper validation
  - Comprehensive error handling and user feedback
- **Files Changed**:
  - `src/app/(dashboard)/dashboard/tire-processing-detail/page.tsx`
  - `src/components/tire-processing-detail/processing-list-section.tsx`
  - `src/components/tire-processing-detail/processing-edit-form.tsx`
  - `src/services/tireProcessingService.ts`

### Tire Update 404 Error Fix (v1.1.0)
- **Issue**: 404 errors when updating tire details
- **Root Cause**: Missing CRUD function for request detail updates
- **Solution**: Implemented `update_request_detail` function with proper field mapping
- **Files Changed**: `backend/crud.py`, `backend/routers/request.py`

### Tire Processing Navigation Fix (v1.1.0)
- **Issue**: "Tire not found" error in navigation
- **Root Cause**: Data structure inconsistency in API response handling
- **Solution**: Fixed data access pattern in tire-processing-detail page
- **Files Changed**: `src/app/(dashboard)/dashboard/tire-processing-detail/page.tsx`

## Best Practices

### Security
- Never commit sensitive data (API keys, passwords)
- Use environment variables for configuration
- Implement proper input validation
- Follow OWASP security guidelines

### Performance
- Minimize API calls with proper data fetching
- Implement proper error boundaries
- Use React.memo for expensive components
- Optimize database queries

### Maintainability
- Write self-documenting code
- Use consistent naming conventions
- Implement proper error handling
- Keep components small and focused
