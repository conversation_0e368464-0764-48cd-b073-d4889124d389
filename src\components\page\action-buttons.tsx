import { But<PERSON> } from "@/components/ui/button";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";

interface ActionButtonsProps {
  onActionClick: (action: "new" | "delete" | "save" | "detail" | "send" | "home") => void;
}

export function ActionButtonsSection({ onActionClick }: ActionButtonsProps) {
  return (
    <section aria-labelledby="action-buttons-heading" className="flex flex-wrap justify-start gap-2">
      <h2 id="action-buttons-heading" className="sr-only">Action Buttons</h2>
      <Button variant="outline" onClick={() => onActionClick("home")}>HOME</Button>
      <DeleteButton
        onClick={() => onActionClick("delete")}
        size="default"
        title="Confirm Deletion"
        description="Are you sure you want to delete this item? This action cannot be undone."
      />
      <Button variant="outline" onClick={() => onActionClick("new")}>New</Button>
      <Button variant="default" onClick={() => onActionClick("save")}>Save</Button>
      <Button variant="outline" onClick={() => onActionClick("detail")}>Detail</Button>
      <Button variant="default" className="bg-accent hover:bg-accent/90" onClick={() => onActionClick("send")}>Send</Button>
    </section>
  );
}
