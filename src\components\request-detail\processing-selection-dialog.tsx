"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, X, Calculator } from "lucide-react";
import { DialogProcessing, ProcessingSearchFilters, initialProcessingSearchFilters } from "@/types";
import { searchProcessing } from "@/services/cutProcessingService";
import { useToast } from "@/hooks/use-toast";

interface ProcessingWithQuantity extends DialogProcessing {
  quantity: number;
}

interface ProcessingSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSelectedProcessing: (selectedProcessing: ProcessingWithQuantity[]) => void;
}

export function ProcessingSelectionDialog({
  isOpen,
  onClose,
  onAddSelectedProcessing,
}: ProcessingSelectionDialogProps) {
  const [processing, setProcessing] = useState<DialogProcessing[]>([]);
  const [selectedProcessing, setSelectedProcessing] = useState<Set<string>>(new Set());
  const [processingQuantities, setProcessingQuantities] = useState<Map<string, number>>(new Map());
  const [filters, setFilters] = useState<ProcessingSearchFilters>(initialProcessingSearchFilters);
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const { toast } = useToast();

  // Carica i processing quando il dialog si apre
  useEffect(() => {
    if (isOpen) {
      loadProcessing();
    }
  }, [isOpen]);

  const loadProcessing = async () => {
    try {
      setIsLoading(true);
      const response = await searchProcessing(filters);
      setProcessing(response.data);
    } catch (error) {
      console.error("Errore nel caricamento dei processing:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i processing",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    loadProcessing();
  };

  const handleFilterChange = (field: keyof ProcessingSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters(initialProcessingSearchFilters);
    setSelectedProcessing(new Set());
  };

  const handleProcessingToggle = (processingId: string) => {
    const newSelected = new Set(selectedProcessing);
    if (newSelected.has(processingId)) {
      newSelected.delete(processingId);
    } else {
      newSelected.add(processingId);
    }
    setSelectedProcessing(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedProcessing.size === processing.length) {
      setSelectedProcessing(new Set());
    } else {
      setSelectedProcessing(new Set(processing.map(p => p.id)));
    }
  };

  const handleQuantityChange = (processingId: string, quantity: number) => {
    const newQuantities = new Map(processingQuantities);
    if (quantity > 0) {
      newQuantities.set(processingId, quantity);
    } else {
      newQuantities.delete(processingId);
    }
    setProcessingQuantities(newQuantities);
  };

  const getQuantity = (processingId: string): number => {
    return processingQuantities.get(processingId) || 1;
  };

  const handleAddSelected = () => {
    const selected: ProcessingWithQuantity[] = processing
      .filter(p => selectedProcessing.has(p.id))
      .map(p => ({
        ...p,
        quantity: getQuantity(p.id)
      }));
    onAddSelectedProcessing(selected);
    setSelectedProcessing(new Set());
    setProcessingQuantities(new Map());
    onClose();
  };

  const handleClose = () => {
    setSelectedProcessing(new Set());
    setProcessingQuantities(new Map());
    onClose();
  };

  // Estrai il valore numerico dal costo (es. "52.41 €" -> 52.41)
  const extractCostValue = (cost: string): number => {
    const match = cost.match(/(\d+\.?\d*)/);
    return match ? parseFloat(match[1]) : 0;
  };

  // Calcola il costo totale dei processing selezionati
  const calculateTotalCost = (): number => {
    return processing
      .filter(p => selectedProcessing.has(p.id))
      .reduce((total, proc) => {
        const cost = extractCostValue(proc.cost);
        const quantity = getQuantity(proc.id);
        return total + (cost * quantity);
      }, 0);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Seleziona Processing</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col space-y-4 flex-1 overflow-hidden">
          {/* Filtri */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Filtri di Ricerca</h3>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {showFilters ? "Nascondi" : "Mostra"} Filtri
                </Button>
                <Button variant="outline" size="sm" onClick={handleClearFilters}>
                  <X className="h-4 w-4 mr-2" />
                  Pulisci
                </Button>
                <Button onClick={handleSearch} disabled={isLoading}>
                  <Search className="h-4 w-4 mr-2" />
                  {isLoading ? "Cercando..." : "Cerca"}
                </Button>
              </div>
            </div>

            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tireType">Tipo Pneumatico</Label>
                  <Input
                    id="tireType"
                    placeholder="es. Type A"
                    value={filters.tireType || ""}
                    onChange={(e) => handleFilterChange("tireType", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description1">Descrizione 1</Label>
                  <Input
                    id="description1"
                    placeholder="es. Desc 1"
                    value={filters.description1 || ""}
                    onChange={(e) => handleFilterChange("description1", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description2">Descrizione 2</Label>
                  <Input
                    id="description2"
                    placeholder="es. Detail Desc 2"
                    value={filters.description2 || ""}
                    onChange={(e) => handleFilterChange("description2", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="testCode1">Test Code 1</Label>
                  <Input
                    id="testCode1"
                    placeholder="es. TC1-001"
                    value={filters.testCode1 || ""}
                    onChange={(e) => handleFilterChange("testCode1", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="testCode2">Test Code 2</Label>
                  <Input
                    id="testCode2"
                    placeholder="es. TC2-001"
                    value={filters.testCode2 || ""}
                    onChange={(e) => handleFilterChange("testCode2", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minCost">Costo Minimo (€)</Label>
                  <Input
                    id="minCost"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={filters.minCost || ""}
                    onChange={(e) => handleFilterChange("minCost", parseFloat(e.target.value) || undefined)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxCost">Costo Massimo (€)</Label>
                  <Input
                    id="maxCost"
                    type="number"
                    step="0.01"
                    placeholder="999.99"
                    value={filters.maxCost || ""}
                    onChange={(e) => handleFilterChange("maxCost", parseFloat(e.target.value) || undefined)}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Risultati */}
          <div className="flex-1 overflow-hidden border rounded-lg">
            <div className="p-4 border-b bg-muted/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-muted-foreground">
                    {processing.length} processing trovati
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {selectedProcessing.size} selezionati
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={processing.length === 0}
                >
                  {selectedProcessing.size === processing.length ? "Deseleziona Tutti" : "Seleziona Tutti"}
                </Button>
              </div>
            </div>

            <div className="overflow-auto max-h-96">
              {isLoading ? (
                <div className="text-center py-8">Caricamento...</div>
              ) : processing.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Nessun processing trovato
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedProcessing.size === processing.length && processing.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>ID</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Descrizione 1</TableHead>
                      <TableHead>Descrizione 2</TableHead>
                      <TableHead>Test Code 1</TableHead>
                      <TableHead>Test Code 2</TableHead>
                      <TableHead>Costo</TableHead>
                      <TableHead className="w-20">Quantità</TableHead>
                      <TableHead>Totale</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {processing.map((proc) => {
                      const quantity = getQuantity(proc.id);
                      const cost = extractCostValue(proc.cost);
                      const total = cost * quantity;

                      return (
                        <TableRow
                          key={proc.id}
                          className={selectedProcessing.has(proc.id) ? "bg-muted/50" : ""}
                        >
                          <TableCell>
                            <Checkbox
                              checked={selectedProcessing.has(proc.id)}
                              onCheckedChange={() => handleProcessingToggle(proc.id)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{proc.id}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{proc.tireType}</Badge>
                          </TableCell>
                          <TableCell className="max-w-[150px] truncate">
                            {proc.description1}
                          </TableCell>
                          <TableCell className="max-w-[150px] truncate">
                            {proc.description2}
                          </TableCell>
                          <TableCell>{proc.testCode1}</TableCell>
                          <TableCell>{proc.testCode2}</TableCell>
                          <TableCell className="font-medium">{proc.cost}</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="1"
                              max="999"
                              value={quantity}
                              onChange={(e) => handleQuantityChange(proc.id, parseInt(e.target.value) || 1)}
                              className="w-16 h-8 text-center"
                              disabled={!selectedProcessing.has(proc.id)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            {selectedProcessing.has(proc.id) ? `€${total.toFixed(2)}` : "-"}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </div>
          </div>

          {/* Preview Costo Totale e Azioni */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="flex items-center space-x-4">
              {selectedProcessing.size > 0 && (
                <div className="flex items-center space-x-2 bg-primary/10 px-4 py-2 rounded-lg">
                  <Calculator className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">
                    Costo Totale: <span className="text-primary font-bold">€{calculateTotalCost().toFixed(2)}</span>
                  </span>
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleClose}>
                Annulla
              </Button>
              <Button
                onClick={handleAddSelected}
                disabled={selectedProcessing.size === 0}
              >
                Aggiungi {selectedProcessing.size} Processing
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
