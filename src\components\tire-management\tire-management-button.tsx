"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { TireManagementDialog } from "./tire-management-dialog";
import { DialogTire } from "@/types";

interface TireManagementButtonProps {
  className?: string;
  onAddSelectedTires?: (selectedTires: DialogTire[]) => void;
}

export function TireManagementButton({
  className,
  onAddSelectedTires
}: TireManagementButtonProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        onClick={() => setOpen(true)}
        className={`text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700 ${className}`}
        aria-label="Add new tire"
      >
        <PlusCircle className="h-5 w-5" />
      </Button>

      <TireManagementDialog
        open={open}
        onOpenChange={setOpen}
        onAddSelectedTires={onAddSelectedTires}
      />
    </>
  );
}
