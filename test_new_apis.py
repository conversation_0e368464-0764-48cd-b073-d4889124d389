#!/usr/bin/env python3
"""
Test script for the new simplified API endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """Get authentication token"""
    login_url = f"{BASE_URL}/users/login"
    data = {
        "username": "<EMAIL>",
        "password": "admin123"
    }

    response = requests.post(login_url, data=data)
    if response.status_code == 200:
        token_data = response.json()
        return token_data["access_token"]
    else:
        print(f"Failed to get token: {response.status_code} - {response.text}")
        return None

def test_endpoint(endpoint, token, method="GET", data=None):
    """Test an API endpoint"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    url = f"{BASE_URL}{endpoint}"
    print(f"\n🔍 Testing {method} {endpoint}")

    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            if isinstance(result, list):
                print(f"   ✅ Success! Received {len(result)} records")
                if result:
                    print(f"   📋 Sample record: {json.dumps(result[0], indent=2)[:200]}...")
            else:
                print(f"   ✅ Success! Response: {json.dumps(result, indent=2)[:200]}...")
        else:
            print(f"   ❌ Error: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

def main():
    """Test all new simplified API endpoints"""
    print("🚀 Testing New Simplified API Endpoints")
    print("=" * 50)

    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return

    print("✅ Got authentication token")

    # Test Enhanced Tires API
    print("\n🛞 TESTING ENHANCED TIRES API")
    test_endpoint("/tires", token)
    test_endpoint("/tires/T001", token)
    test_endpoint("/tires?owner=Michelin", token)
    test_endpoint("/tires/stats/summary", token)

    # Test Request Items API
    print("\n📦 TESTING REQUEST ITEMS API")
    test_endpoint("/request-items/request/REQ001", token)
    test_endpoint("/request-items/ITEM001", token)
    test_endpoint("/request-items/ITEM001/cut-operations", token)
    test_endpoint("/request-items/ITEM001/summary", token)

    # Test Cut Operations API
    print("\n✂️ TESTING CUT OPERATIONS API")
    test_endpoint("/cut-operations", token)
    test_endpoint("/cut-operations/request/REQ001", token)
    test_endpoint("/cut-operations/item/ITEM001", token)
    test_endpoint("/cut-operations/stats/summary", token)

    # Test Enhanced Request API
    print("\n📋 TESTING ENHANCED REQUEST API")
    test_endpoint("/requests/REQ001/simplified", token)
    test_endpoint("/requests/REQ001/items", token)
    test_endpoint("/requests/REQ001/cut-operations", token)
    test_endpoint("/requests/REQ001/summary", token)

    # Test Create Operations
    print("\n🔧 TESTING CREATE OPERATIONS")

    # Create a new tire
    new_tire = {
        "id": "T_TEST_001",
        "tug_no": "TUG-TEST-NEW",
        "spec_no": "SPEC-TEST",
        "size": "225/45R17",
        "owner": "TestOwner",
        "load_index": "91V",
        "pattern": "TEST_PATTERN",
        "project_no": "PRJ-TEST",
        "location": "TEST-SHELF",
        "description": "Test tire for API testing",
        "is_active": True
    }
    test_endpoint("/tires", token, "POST", new_tire)

    # Create a new request item
    new_item = {
        "id": "ITEM_TEST_001",
        "request_id": "REQ001",
        "tire_id": "T001",
        "quantity": 1,
        "disposition": "AVAILABLE",
        "notes": "Test item created via API",
        "unit_price": 100.0,
        "section": "TEST_SECTION"
    }
    test_endpoint("/request-items", token, "POST", new_item)

    # Create a new cut operation
    new_cut = {
        "request_item_id": "ITEM001",
        "processing_id": "PROC001",
        "quantity": 1,
        "cut_price": 75.50,
        "status": "PENDING",
        "notes": "Test cut operation created via API"
    }
    test_endpoint("/cut-operations", token, "POST", new_cut)

    print("\n" + "=" * 50)
    print("✅ API Testing completed!")

if __name__ == "__main__":
    main()
