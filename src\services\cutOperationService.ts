import axiosInstance from "@/lib/axiosInstance";
import { CutOperation, CutOperationFormData } from "@/types";

// Cut Operations API paths
const CUT_OPERATIONS_API_PATH = "/cut-operations";

// Interface for cut operation statistics
export interface CutOperationStats {
  total_operations: number;
  status_breakdown: Array<{ status: string; count: number }>;
  processing_breakdown: Array<{ tire_type: string; count: number }>;
  financial: {
    total_cost: number;
    average_cost: number;
  };
  monthly_trend: Array<{ month: string; count: number }>;
}

// Interface for cut operation filters
export interface CutOperationFilterParams {
  skip?: number;
  limit?: number;
  status?: string;
  processing_id?: string;
}

/**
 * Cut Operations Service - New Simplified Structure
 * 
 * This service handles cut operations (simplified replacement for RequestDetailCut + CutProcessing).
 * Cut operations combine cutting specifications with processing operations in a single entity.
 */

// Get all cut operations for a specific request (across all items)
export const getCutOperationsByRequest = async (requestId: string): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.get<CutOperation[]>(`${CUT_OPERATIONS_API_PATH}/request/${requestId}`);
    return response.data;
  } catch (error) {
    console.error(`[cutOperationService] Error fetching cut operations for request ${requestId}:`, error);
    throw error;
  }
};

// Get all cut operations for a specific request item
export const getCutOperationsByItem = async (itemId: string): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.get<CutOperation[]>(`${CUT_OPERATIONS_API_PATH}/item/${itemId}`);
    return response.data;
  } catch (error) {
    console.error(`[cutOperationService] Error fetching cut operations for item ${itemId}:`, error);
    throw error;
  }
};

// Get a specific cut operation by ID
export const getCutOperation = async (operationId: number): Promise<CutOperation> => {
  try {
    const response = await axiosInstance.get<CutOperation>(`${CUT_OPERATIONS_API_PATH}/${operationId}`);
    return response.data;
  } catch (error) {
    console.error(`[cutOperationService] Error fetching cut operation with ID ${operationId}:`, error);
    throw error;
  }
};

// Create a new cut operation
export const createCutOperation = async (data: CutOperationFormData): Promise<CutOperation> => {
  try {
    const response = await axiosInstance.post<CutOperation>(CUT_OPERATIONS_API_PATH, data);
    return response.data;
  } catch (error) {
    console.error("[cutOperationService] Error creating cut operation:", error);
    throw error;
  }
};

// Update an existing cut operation
export const updateCutOperation = async (operationId: number, data: Partial<CutOperationFormData>): Promise<CutOperation> => {
  try {
    const response = await axiosInstance.put<CutOperation>(`${CUT_OPERATIONS_API_PATH}/${operationId}`, data);
    return response.data;
  } catch (error) {
    console.error(`[cutOperationService] Error updating cut operation with ID ${operationId}:`, error);
    throw error;
  }
};

// Delete a cut operation
export const deleteCutOperation = async (operationId: number): Promise<CutOperation> => {
  try {
    const response = await axiosInstance.delete<CutOperation>(`${CUT_OPERATIONS_API_PATH}/${operationId}`);
    return response.data;
  } catch (error) {
    console.error(`[cutOperationService] Error deleting cut operation with ID ${operationId}:`, error);
    throw error;
  }
};

// Get all cut operations with optional filtering
export const getCutOperations = async (params?: CutOperationFilterParams): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.get<CutOperation[]>(CUT_OPERATIONS_API_PATH, { params });
    return response.data;
  } catch (error) {
    console.error("[cutOperationService] Error fetching cut operations:", error);
    throw error;
  }
};

// Get cut operations statistics
export const getCutOperationStats = async (): Promise<CutOperationStats> => {
  try {
    const response = await axiosInstance.get<CutOperationStats>(`${CUT_OPERATIONS_API_PATH}/stats/summary`);
    return response.data;
  } catch (error) {
    console.error("[cutOperationService] Error fetching cut operation stats:", error);
    throw error;
  }
};

// Create multiple cut operations in a batch
export const batchCreateCutOperations = async (operations: CutOperationFormData[]): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.post<CutOperation[]>(`${CUT_OPERATIONS_API_PATH}/batch`, operations);
    return response.data;
  } catch (error) {
    console.error("[cutOperationService] Error in batch create cut operations:", error);
    throw error;
  }
};

// Update status for multiple cut operations
export const batchUpdateStatus = async (operationIds: number[], newStatus: string): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.put<CutOperation[]>(`${CUT_OPERATIONS_API_PATH}/batch/status`, {
      operation_ids: operationIds,
      new_status: newStatus,
    });
    return response.data;
  } catch (error) {
    console.error("[cutOperationService] Error in batch update status:", error);
    throw error;
  }
};

// Validate cut operation data before submission
export const validateCutOperationData = (data: CutOperationFormData): string[] => {
  const errors: string[] = [];

  if (!data.requestItemId?.trim()) {
    errors.push("Request item ID is required");
  }

  if (!data.processingId?.trim()) {
    errors.push("Processing ID is required");
  }

  if (!data.quantity || data.quantity <= 0) {
    errors.push("Quantity must be greater than 0");
  }

  if (data.cutPrice !== undefined && data.cutPrice < 0) {
    errors.push("Cut price cannot be negative");
  }

  const validStatuses = ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"];
  if (data.status && !validStatuses.includes(data.status)) {
    errors.push("Invalid status value");
  }

  return errors;
};

// Transform legacy cut data to cut operation format
export const transformLegacyToCutOperation = (legacyCut: any, requestItemId: string): CutOperationFormData => {
  return {
    requestItemId: requestItemId,
    processingId: legacyCut.processingId || legacyCut.processing_id || "",
    quantity: legacyCut.quantity || legacyCut.n || 1,
    cutPrice: legacyCut.cutPrice || legacyCut.cut_price || 0,
    status: legacyCut.status || "PENDING",
    notes: legacyCut.notes || legacyCut.note || "",
  };
};

// Transform cut operation to legacy format for backward compatibility
export const transformCutOperationToLegacy = (cutOperation: CutOperation): any => {
  return {
    id: cutOperation.id,
    request_item_id: cutOperation.requestItemId,
    processing_id: cutOperation.processingId,
    quantity: cutOperation.quantity,
    cut_price: cutOperation.cutPrice,
    status: cutOperation.status,
    notes: cutOperation.notes,
    created_date: cutOperation.createdDate,
    // Legacy fields for compatibility
    cutId: cutOperation.id,
    n: cutOperation.quantity,
    note: cutOperation.notes,
    processing: cutOperation.processing,
  };
};

// Calculate totals for cut operations
export const calculateCutOperationTotals = (operations: CutOperation[]): {
  totalOperations: number;
  totalCost: number;
  totalQuantity: number;
  operationsByStatus: Record<string, number>;
  operationsByProcessing: Record<string, number>;
} => {
  const totals = {
    totalOperations: operations.length,
    totalCost: 0,
    totalQuantity: 0,
    operationsByStatus: {} as Record<string, number>,
    operationsByProcessing: {} as Record<string, number>,
  };

  operations.forEach(operation => {
    totals.totalCost += (operation.cutPrice || 0) * operation.quantity;
    totals.totalQuantity += operation.quantity;
    
    const status = operation.status || "UNKNOWN";
    totals.operationsByStatus[status] = (totals.operationsByStatus[status] || 0) + 1;
    
    const processingType = operation.processing?.tireType || "UNKNOWN";
    totals.operationsByProcessing[processingType] = (totals.operationsByProcessing[processingType] || 0) + 1;
  });

  return totals;
};

// Generate cut operation summary for reporting
export const generateCutOperationSummary = (operations: CutOperation[]): {
  summary: string;
  details: {
    completed: number;
    pending: number;
    inProgress: number;
    cancelled: number;
    totalCost: number;
    averageCost: number;
  };
} => {
  const totals = calculateCutOperationTotals(operations);
  const completed = totals.operationsByStatus["COMPLETED"] || 0;
  const pending = totals.operationsByStatus["PENDING"] || 0;
  const inProgress = totals.operationsByStatus["IN_PROGRESS"] || 0;
  const cancelled = totals.operationsByStatus["CANCELLED"] || 0;
  
  const averageCost = totals.totalOperations > 0 ? totals.totalCost / totals.totalOperations : 0;

  return {
    summary: `${totals.totalOperations} operations (${completed} completed, ${pending} pending, ${inProgress} in progress, ${cancelled} cancelled)`,
    details: {
      completed,
      pending,
      inProgress,
      cancelled,
      totalCost: totals.totalCost,
      averageCost,
    },
  };
};

// Search cut operations with multiple criteria
export const searchCutOperations = async (searchParams: {
  requestId?: string;
  itemId?: string;
  status?: string;
  processingId?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
}): Promise<CutOperation[]> => {
  try {
    if (searchParams.requestId) {
      return await getCutOperationsByRequest(searchParams.requestId);
    } else if (searchParams.itemId) {
      return await getCutOperationsByItem(searchParams.itemId);
    } else {
      const params: CutOperationFilterParams = {
        limit: searchParams.limit || 100,
        status: searchParams.status,
        processing_id: searchParams.processingId,
      };
      return await getCutOperations(params);
    }
  } catch (error) {
    console.error("[cutOperationService] Error searching cut operations:", error);
    throw error;
  }
};
