<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tire Management Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .empty-field {
            color: #999;
            font-style: italic;
        }
        .filled-field {
            color: #333;
            font-weight: bold;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Tire Management Fix</h1>
        <p>Questo test simula il problema dei campi vuoti quando si aggiungono pneumatici dalla gestione.</p>

        <div class="section">
            <h2>Simulazione Dati Pneumatici dal Management</h2>
            <p>Questi sono i dati che arrivano dal TireManagementDialog:</p>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>TUG Number</th>
                        <th>Spec Number</th>
                        <th>Size</th>
                        <th>Owner</th>
                        <th>Pattern</th>
                        <th>Project Number</th>
                        <th>Location</th>
                    </tr>
                </thead>
                <tbody id="managementData">
                    <tr>
                        <td>T_TEST_NEW</td>
                        <td class="filled-field">TUG-001</td>
                        <td class="filled-field">SP-123</td>
                        <td class="filled-field">225/45R17</td>
                        <td class="filled-field">TestOwner</td>
                        <td class="filled-field">TEST_PATTERN</td>
                        <td class="filled-field">PRJ-001</td>
                        <td class="filled-field">Shelf 1</td>
                    </tr>
                    <tr>
                        <td>T001</td>
                        <td class="filled-field">TUG-002</td>
                        <td class="empty-field">null</td>
                        <td class="filled-field">225/45R17</td>
                        <td class="filled-field">Bridgestone</td>
                        <td class="filled-field">Potenza S007</td>
                        <td class="empty-field">undefined</td>
                        <td class="filled-field">Shelf 2</td>
                    </tr>
                </tbody>
            </table>
            <button class="button" onclick="simulateAddTires()">Simula ADD SELECTED</button>
        </div>

        <div class="section">
            <h2>Risultato nella Tabella Request Details</h2>
            <p>Dopo la correzione, i campi dovrebbero essere popolati correttamente:</p>
            <table>
                <thead>
                    <tr>
                        <th>Tug#</th>
                        <th>Section</th>
                        <th>Project#</th>
                        <th>Spec#</th>
                        <th>Tire Size</th>
                        <th>Pattern</th>
                        <th>Note</th>
                        <th>Disposition</th>
                    </tr>
                </thead>
                <tbody id="requestDetailsData">
                    <!-- I risultati appariranno qui -->
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>Log di Debug</h2>
            <div id="debugLog" class="log">
                Clicca "Simula ADD SELECTED" per vedere il log di debug...
            </div>
        </div>
    </div>

    <script>
        // Simula i dati che arrivano dal TireManagementDialog
        const mockDialogTires = [
            {
                id: "T_TEST_NEW",
                tugNo: "TUG-001",
                specNo: "SP-123",
                size: "225/45R17",
                owner: "TestOwner",
                loadIndex: "91",
                pattern: "TEST_PATTERN",
                projectNo: "PRJ-001",
                location: "Shelf 1"
            },
            {
                id: "T001",
                tugNo: "TUG-002",
                specNo: null, // Campo vuoto
                size: "225/45R17",
                owner: "Bridgestone",
                loadIndex: "91",
                pattern: "Potenza S007",
                projectNo: undefined, // Campo vuoto
                location: "Shelf 2"
            }
        ];

        function log(message) {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Simula la funzione mapEnhancedTireToDialogTire (CORRETTA)
        function mapEnhancedTireToDialogTire(enhancedTire) {
            log(`mapEnhancedTireToDialogTire: Mapping enhanced tire: ${JSON.stringify(enhancedTire)}`);

            const dialogTire = {
                id: enhancedTire.id,
                tugNo: enhancedTire.tugNo || "",
                specNo: enhancedTire.specNo || "",
                size: enhancedTire.size || "",
                owner: enhancedTire.owner || "",
                loadIndex: enhancedTire.loadIndex || "",
                pattern: enhancedTire.pattern || "",
                projectNo: enhancedTire.projectNo || "",
                location: enhancedTire.location || "",
            };

            log(`mapEnhancedTireToDialogTire: Mapped to dialog tire: ${JSON.stringify(dialogTire)}`);
            return dialogTire;
        }

        // Simula la funzione handleAddTiresFromManagement (CORRETTA)
        function handleAddTiresFromManagement(selectedTires) {
            if (selectedTires.length === 0) return;

            log(`handleAddTiresFromManagement: Adding tires from management: ${JSON.stringify(selectedTires)}`);

            const newTires = selectedTires.map((dialogTire, index) => {
                log(`Processing tire ${index}: ${JSON.stringify({
                    tugNo: dialogTire.tugNo,
                    projectNo: dialogTire.projectNo,
                    specNo: dialogTire.specNo,
                    size: dialogTire.size,
                    pattern: dialogTire.pattern
                })}`);

                return {
                    id: `MGT-${Date.now()}-${index}`,
                    tugNo: dialogTire.tugNo || "",
                    section: "FROM_MANAGEMENT",
                    projectNo: dialogTire.projectNo || "",
                    specNo: dialogTire.specNo || "",
                    tireSize: dialogTire.size || "",
                    pattern: dialogTire.pattern || "",
                    note: `Added via management. Owner: ${dialogTire.owner || ""}, LI: ${dialogTire.loadIndex || ""}, Loc: ${dialogTire.location || ""}`,
                    disposition: "AVAILABLE",
                    quantity: 1,
                };
            });

            log(`handleAddTiresFromManagement: Created new tires: ${JSON.stringify(newTires)}`);
            return newTires;
        }

        function simulateAddTires() {
            log("=== INIZIO SIMULAZIONE ===");
            log("Simulando il flusso di aggiunta pneumatici...");

            // Simula il mapping dei dati
            const mappedTires = mockDialogTires.map(mapEnhancedTireToDialogTire);

            // Simula l'aggiunta alla tabella
            const resultTires = handleAddTiresFromManagement(mappedTires);

            // Mostra i risultati nella tabella
            const tbody = document.getElementById('requestDetailsData');
            tbody.innerHTML = '';

            resultTires.forEach(tire => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="${tire.tugNo ? 'filled-field' : 'empty-field'}">${tire.tugNo || '(vuoto)'}</td>
                    <td>${tire.section}</td>
                    <td class="${tire.projectNo ? 'filled-field' : 'empty-field'}">${tire.projectNo || '(vuoto)'}</td>
                    <td class="${tire.specNo ? 'filled-field' : 'empty-field'}">${tire.specNo || '(vuoto)'}</td>
                    <td class="${tire.tireSize ? 'filled-field' : 'empty-field'}">${tire.tireSize || '(vuoto)'}</td>
                    <td class="${tire.pattern ? 'filled-field' : 'empty-field'}">${tire.pattern || '(vuoto)'}</td>
                    <td>${tire.note}</td>
                    <td>${tire.disposition}</td>
                `;
                tbody.appendChild(row);
            });

            log("=== FINE SIMULAZIONE ===");
            log("RISULTATO: I campi Tug#, Project#, e Spec# ora mantengono i valori originali invece di essere sostituiti con '-'");
        }

        // Inizializza il log
        log("Test inizializzato. Pronto per la simulazione.");
    </script>
</body>
</html>
