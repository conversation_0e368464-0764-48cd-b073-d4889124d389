{"knowledgeGraph": {"version": "1.0.0", "lastUpdated": "2025-05-28T13:52:00Z", "nodes": {}, "edges": {}, "clusters": {"technical": {"id": "technical", "name": "Technical Knowledge", "description": "Technical architecture, patterns, and implementations", "color": "#3B82F6", "nodes": []}, "development": {"id": "development", "name": "Development History", "description": "Development evolution and decisions", "color": "#10B981", "nodes": []}, "business": {"id": "business", "name": "Business Domain", "description": "Business workflows and domain knowledge", "color": "#F59E0B", "nodes": []}, "operational": {"id": "operational", "name": "Operational Knowledge", "description": "Operational procedures and guidelines", "color": "#EF4444", "nodes": []}}, "nodeTypes": {"component": {"icon": "component", "color": "#8B5CF6", "description": "Software component or module"}, "pattern": {"icon": "pattern", "color": "#06B6D4", "description": "Design or implementation pattern"}, "workflow": {"icon": "workflow", "color": "#84CC16", "description": "Business workflow or process"}, "decision": {"icon": "decision", "color": "#F97316", "description": "Technical or business decision"}, "document": {"icon": "document", "color": "#6B7280", "description": "Documentation or guide"}}, "edgeTypes": {"implements": {"color": "#10B981", "style": "solid", "description": "Implements relationship"}, "depends": {"color": "#EF4444", "style": "dashed", "description": "Dependency relationship"}, "relates": {"color": "#6B7280", "style": "dotted", "description": "General relationship"}, "evolves": {"color": "#8B5CF6", "style": "solid", "description": "Evolution relationship"}}, "layout": {"algorithm": "force-directed", "settings": {"repulsion": 1000, "attraction": 0.1, "damping": 0.9, "maxIterations": 1000}}, "statistics": {"totalNodes": 0, "totalEdges": 0, "clusters": 4, "density": 0, "averageDegree": 0}}}