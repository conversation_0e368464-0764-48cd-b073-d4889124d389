# Pastel Colors Usage Guide

This guide demonstrates how to use the new soft pastel color classes for tables and forms in your project.

## Available Color Classes

The following new Tailwind classes are now available:

### Table Colors
- `bg-table-row` - Soft blue background for table rows
- `border-table-border` - Soft lavender border for tables

### Form Colors
- `bg-form-bg` - Soft mint background for forms
- `border-form-border` - Soft peach border for forms

## Color Values

### Light Mode
- **Table Row**: Very light blue (`hsl(210 100% 97%)`)
- **Table Border**: Very light lavender (`hsl(270 50% 95%)`)
- **Form Background**: Very light mint (`hsl(150 60% 96%)`)
- **Form Border**: Very light peach (`hsl(30 100% 95%)`)

### Dark Mode
- **Table Row**: Muted blue (`hsl(210 30% 18%)`)
- **Table Border**: Muted lavender (`hsl(270 20% 22%)`)
- **Form Background**: Muted mint (`hsl(150 25% 17%)`)
- **Form Border**: Muted peach (`hsl(30 40% 20%)`)

## Usage Examples

### Table Example

```jsx
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

function DataTable({ data }) {
  return (
    <Table className="border border-table-border">
      <TableHeader>
        <TableRow className="bg-table-row">
          <TableHead className="border-b border-table-border">Name</TableHead>
          <TableHead className="border-b border-table-border">Email</TableHead>
          <TableHead className="border-b border-table-border">Status</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((item, index) => (
          <TableRow
            key={item.id}
            className={index % 2 === 0 ? "bg-table-row" : ""}
          >
            <TableCell className="border-b border-table-border">{item.name}</TableCell>
            <TableCell className="border-b border-table-border">{item.email}</TableCell>
            <TableCell className="border-b border-table-border">{item.status}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
```

### Form Example

```jsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";

function ContactForm() {
  return (
    <Card className="bg-form-bg border-form-border">
      <CardHeader>
        <CardTitle>Contact Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            className="bg-form-bg border-form-border focus:ring-primary"
            placeholder="Enter your name"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            className="bg-form-bg border-form-border focus:ring-primary"
            placeholder="Enter your email"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="message">Message</Label>
          <Textarea
            id="message"
            className="bg-form-bg border-form-border focus:ring-primary"
            placeholder="Enter your message"
            rows={4}
          />
        </div>

        <Button type="submit" className="w-full">
          Submit
        </Button>
      </CardContent>
    </Card>
  );
}
```

### Advanced Table with Hover Effects

```jsx
function AdvancedTable({ data }) {
  return (
    <div className="overflow-hidden rounded-lg border border-table-border">
      <Table>
        <TableHeader>
          <TableRow className="bg-table-row hover:bg-table-row/80">
            <TableHead className="border-b border-table-border">Product</TableHead>
            <TableHead className="border-b border-table-border">Price</TableHead>
            <TableHead className="border-b border-table-border">Stock</TableHead>
            <TableHead className="border-b border-table-border">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((product, index) => (
            <TableRow
              key={product.id}
              className={`
                ${index % 2 === 0 ? "bg-table-row" : "bg-background"}
                hover:bg-table-row/60 transition-colors
              `}
            >
              <TableCell className="border-b border-table-border font-medium">
                {product.name}
              </TableCell>
              <TableCell className="border-b border-table-border">
                ${product.price}
              </TableCell>
              <TableCell className="border-b border-table-border">
                {product.stock}
              </TableCell>
              <TableCell className="border-b border-table-border">
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
```

### Form with Sections

```jsx
function UserProfileForm() {
  return (
    <div className="space-y-6">
      {/* Personal Information Section */}
      <Card className="bg-form-bg border-form-border">
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              className="bg-form-bg border-form-border"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              className="bg-form-bg border-form-border"
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information Section */}
      <Card className="bg-form-bg border-form-border">
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              className="bg-form-bg border-form-border"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              className="bg-form-bg border-form-border"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## Best Practices

1. **Consistency**: Use the same color scheme throughout your application for a cohesive look.

2. **Accessibility**: The pastel colors are designed to provide subtle contrast while maintaining readability. Always test with screen readers and ensure sufficient color contrast.

3. **Hover States**: Consider using opacity modifiers (e.g., `hover:bg-table-row/80`) for interactive elements.

4. **Responsive Design**: The colors work well across different screen sizes and maintain their subtle appearance.

5. **Dark Mode**: The colors automatically adapt to dark mode, providing appropriate contrast for both themes.

## Integration with Existing Components

These pastel colors integrate seamlessly with your existing UI components and can be used alongside the current color system without conflicts.
