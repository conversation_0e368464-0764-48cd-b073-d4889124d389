import axiosInstance from "@/lib/axiosInstance";

// Interface for request detail update data
export interface RequestDetailUpdateData {
  id?: string;
  tug_no?: string;
  project_no?: string;
  tire_size?: string;
  note?: string;
  disposition?: string;
  quantity?: number;
}

// Interface for request detail read data
export interface RequestDetailReadData {
  id: string;
  request_id: string;
  tug_number: string;
  section: string;
  project_number: string;
  spec_number: string;
  tire_size: string;
  pattern: string;
  note?: string;
  disposition: string;
  process_number: number;
}

// Get a specific request detail by ID
export const getRequestDetail = async (detailId: string) => {
  try {
    const response = await axiosInstance.get<RequestDetailReadData>(`/requests/details/${detailId}`);
    return response.data;
  } catch (error) {
    console.error(`[requestDetailService] Error fetching request detail with ID ${detailId}:`, error);
    throw error;
  }
};

// Update a request detail (tire)
export const updateRequestDetail = async (detailId: string, data: RequestDetailUpdateData) => {
  try {
    const response = await axiosInstance.put<RequestDetailReadData>(`/requests/details/${detailId}`, data);
    return response.data;
  } catch (error) {
    console.error(`[requestDetailService] Error updating request detail with ID ${detailId}:`, error);
    throw error;
  }
};

// Delete a request detail (tire)
export const deleteRequestDetail = async (detailId: string) => {
  try {
    await axiosInstance.delete(`/requests/details/${detailId}`);
  } catch (error) {
    console.error(`[requestDetailService] Error deleting request detail with ID ${detailId}:`, error);
    throw error;
  }
};
