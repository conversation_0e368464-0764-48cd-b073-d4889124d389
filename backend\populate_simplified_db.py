#!/usr/bin/env python3
"""
Populate the simplified database with sample data
"""

import sys
import os
import sqlite3
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def populate_database():
    """Populate the simplified database with comprehensive sample data"""
    print("🚀 Populating simplified database with sample data...")
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # 1. Create Users
        print("👥 Creating users...")
        users = [
            (1, "<EMAIL>", "$2b$12$LQv3c1yqBWVHxkd0LQ1Gv.6FqBVT4qvxDlO9jVr5KUEOmUKkN6eoO", "Admin User", "admin", 1),  # password: admin123
            (2, "<EMAIL>", "$2b$12$LQv3c1yqBWVHxkd0LQ1Gv.6FqBVT4qvxDlO9jVr5KUEOmUKkN6eoO", "Manager User", "editor", 1),  # password: admin123
            (3, "<EMAIL>", "$2b$12$LQv3c1yqBWVHxkd0LQ1Gv.6FqBVT4qvxDlO9jVr5KUEOmUKkN6eoO", "Viewer User", "viewer", 1),  # password: admin123
        ]
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                email VARCHAR UNIQUE NOT NULL,
                hashed_password VARCHAR NOT NULL,
                full_name VARCHAR,
                role VARCHAR,
                is_active INTEGER
            )
        ''')
        
        for user in users:
            cursor.execute('''
                INSERT OR REPLACE INTO users 
                (id, email, hashed_password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', user)
        
        # 2. Create Processing operations
        print("⚙️ Creating processing operations...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS processing (
                id VARCHAR PRIMARY KEY,
                tire_type VARCHAR NOT NULL,
                description1 VARCHAR NOT NULL,
                description2 VARCHAR NOT NULL,
                test_code1 VARCHAR NOT NULL,
                test_code2 VARCHAR NOT NULL,
                cost VARCHAR NOT NULL,
                picture INTEGER DEFAULT 0
            )
        ''')
        
        processing_ops = [
            ("PROC001", "Type A", "Standard Cut", "Basic sectioning", "TC1-001", "TC2-001", "75.50 €", 0),
            ("PROC002", "Type B", "Precision Cut", "High precision sectioning", "TC1-002", "TC2-002", "125.00 €", 1),
            ("PROC003", "Type C", "Analysis Cut", "For material analysis", "TC1-003", "TC2-003", "95.75 €", 0),
            ("PROC004", "Type D", "Performance Cut", "Performance testing", "TC1-004", "TC2-004", "150.25 €", 1),
            ("PROC005", "Type E", "Research Cut", "R&D purposes", "TC1-005", "TC2-005", "200.00 €", 1),
        ]
        
        for proc in processing_ops:
            cursor.execute('''
                INSERT OR REPLACE INTO processing 
                (id, tire_type, description1, description2, test_code1, test_code2, cost, picture)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', proc)
        
        # 3. Create Requests
        print("📋 Creating requests...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS requests (
                id VARCHAR PRIMARY KEY,
                request_by VARCHAR NOT NULL,
                project_no VARCHAR NOT NULL,
                pid_project VARCHAR,
                tire_size VARCHAR NOT NULL,
                request_date DATETIME NOT NULL,
                target_date DATETIME NOT NULL,
                status VARCHAR NOT NULL,
                type VARCHAR NOT NULL,
                total_n INTEGER,
                destination VARCHAR,
                internal INTEGER,
                wish_date DATETIME,
                num_pneus_set INTEGER,
                num_pneus_set_unit VARCHAR,
                in_charge_of VARCHAR,
                note VARCHAR
            )
        ''')
        
        requests = [
            ("REQ001", "<EMAIL>", "PRJ-2024-001", "PID-001", "225/45R17", "2024-01-15", "2024-02-15", "OPEN", "NEWDEV", 5, "PLANT_A", 1, "2024-02-01", 2, "SET", "Manager A", "New development project for summer tires"),
            ("REQ002", "<EMAIL>", "PRJ-2024-002", "PID-002", "235/50R18", "2024-01-16", "2024-02-16", "IN_PROGRESS", "REPAIR", 3, "PLANT_B", 0, "2024-02-02", 1, "SET", "Manager B", "Repair analysis for winter tires"),
            ("REQ003", "<EMAIL>", "PRJ-2024-003", "PID-003", "245/40R19", "2024-01-17", "2024-02-17", "COMPLETED", "RESEARCH", 8, "LAB_C", 1, "2024-02-03", 4, "SET", "Manager C", "Research project for performance tires"),
        ]
        
        for req in requests:
            cursor.execute('''
                INSERT OR REPLACE INTO requests 
                (id, request_by, project_no, pid_project, tire_size, request_date, target_date, status, type, total_n, destination, internal, wish_date, num_pneus_set, num_pneus_set_unit, in_charge_of, note)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', req)
        
        # 4. Create Attachments
        print("📎 Creating attachments...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attachments (
                id VARCHAR PRIMARY KEY,
                name VARCHAR NOT NULL,
                size INTEGER NOT NULL,
                type VARCHAR NOT NULL,
                upload_date DATETIME NOT NULL,
                status VARCHAR NOT NULL,
                request_id VARCHAR,
                FOREIGN KEY(request_id) REFERENCES requests(id)
            )
        ''')
        
        attachments = [
            ("ATT001", "project_specs.pdf", 2048576, "application/pdf", "2024-01-15 10:00:00", "UPLOADED", "REQ001"),
            ("ATT002", "tire_analysis.xlsx", 1024000, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "2024-01-16 11:00:00", "UPLOADED", "REQ002"),
            ("ATT003", "test_results.docx", 512000, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "2024-01-17 12:00:00", "UPLOADED", "REQ003"),
        ]
        
        for att in attachments:
            cursor.execute('''
                INSERT OR REPLACE INTO attachments 
                (id, name, size, type, upload_date, status, request_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', att)
        
        # 5. Create Request Items (using existing tires)
        print("🛞 Creating request items...")
        request_items = [
            ("ITEM001", "REQ001", "T001", 2, "AVAILABLE", "High performance tires for testing", 150.0, "SECTION_A"),
            ("ITEM002", "REQ001", "T002", 1, "AVAILABLE", "Comfort tires for comparison", 200.0, "SECTION_B"),
            ("ITEM003", "REQ002", "T003", 3, "RESERVED", "Sport tires for repair analysis", 250.0, "SECTION_C"),
            ("ITEM004", "REQ002", "T004", 2, "AVAILABLE", "Ultra high performance for testing", 300.0, "SECTION_D"),
            ("ITEM005", "REQ003", "T005", 4, "COMPLETED", "Racing tires for research", 350.0, "SECTION_E"),
            ("ITEM006", "REQ003", "T001", 2, "COMPLETED", "Additional high performance tires", 150.0, "SECTION_F"),
        ]
        
        for item in request_items:
            cursor.execute('''
                INSERT OR REPLACE INTO request_items 
                (id, request_id, tire_id, quantity, disposition, notes, unit_price, section)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', item)
        
        # 6. Create Cut Operations
        print("✂️ Creating cut operations...")
        cut_operations = [
            ("ITEM001", "PROC001", 2, 75.50, "COMPLETED", "Standard cut completed successfully", "2024-01-20 14:00:00"),
            ("ITEM001", "PROC002", 1, 125.00, "IN_PROGRESS", "Precision cut in progress", "2024-01-21 09:00:00"),
            ("ITEM002", "PROC003", 1, 95.75, "PENDING", "Analysis cut scheduled", "2024-01-22 10:00:00"),
            ("ITEM003", "PROC004", 3, 150.25, "COMPLETED", "Performance cut completed", "2024-01-18 16:00:00"),
            ("ITEM004", "PROC005", 2, 200.00, "IN_PROGRESS", "Research cut ongoing", "2024-01-19 11:00:00"),
            ("ITEM005", "PROC001", 4, 75.50, "COMPLETED", "Standard cuts for research project", "2024-01-17 13:00:00"),
            ("ITEM006", "PROC002", 2, 125.00, "COMPLETED", "Precision cuts completed", "2024-01-17 15:00:00"),
        ]
        
        for cut in cut_operations:
            cursor.execute('''
                INSERT OR REPLACE INTO cut_operations 
                (request_item_id, processing_id, quantity, cut_price, status, notes, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', cut)
        
        conn.commit()
        print("✅ Database populated successfully!")
        
        # Verify data
        verify_data(cursor)
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error populating database: {e}")
        raise
    finally:
        conn.close()

def verify_data(cursor):
    """Verify that data was inserted correctly"""
    print("\n🔍 Verifying data...")
    
    tables = ['users', 'processing', 'requests', 'attachments', 'tires', 'request_items', 'cut_operations']
    
    for table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"   📊 {table}: {count} records")
    
    # Show some relationships
    print("\n🔗 Sample relationships:")
    cursor.execute('''
        SELECT 
            r.id as request_id,
            r.project_no,
            ri.id as item_id,
            t.tug_no,
            ri.quantity,
            co.processing_id,
            co.status as cut_status
        FROM requests r
        JOIN request_items ri ON r.id = ri.request_id
        JOIN tires t ON ri.tire_id = t.id
        LEFT JOIN cut_operations co ON ri.id = co.request_item_id
        LIMIT 5
    ''')
    
    relationships = cursor.fetchall()
    for rel in relationships:
        print(f"   - Request {rel[0]} ({rel[1]}) -> Item {rel[2]} -> Tire {rel[3]} (qty: {rel[4]}) -> Cut: {rel[5]} ({rel[6]})")

if __name__ == "__main__":
    populate_database()
