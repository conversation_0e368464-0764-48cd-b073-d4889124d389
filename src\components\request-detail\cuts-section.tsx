"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Trash2, Edit, Plus, Settings } from "lucide-react";
import { RequestDetailCut, RequestDetailCutFormData, DialogProcessing, CutProcessing } from "@/types";
import { CutForm } from "./cut-form";
import { ProcessingSelectionDialog } from "./processing-selection-dialog";
import {
  getRequestDetailCutsByRequestDetail,
  createRequestDetailCut,
  updateRequestDetailCut,
  deleteRequestDetailCut
} from "@/services/requestDetailCutService";
import {
  getCutProcessingByCut,
  createCutProcessing,
  deleteCutProcessingByCutAndProcessing
} from "@/services/cutProcessingService";
import { useToast } from "@/hooks/use-toast";

interface CutsSectionProps {
  requestDetailId: string;
}

export function CutsSection({ requestDetailId }: CutsSectionProps) {
  const [cuts, setCuts] = useState<RequestDetailCut[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCut, setEditingCut] = useState<RequestDetailCut | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProcessingDialogOpen, setIsProcessingDialogOpen] = useState(false);
  const [selectedCutForProcessing, setSelectedCutForProcessing] = useState<RequestDetailCut | null>(null);
  const { toast } = useToast();

  // Carica i tagli per il request detail
  const loadCuts = async () => {
    try {
      setIsLoading(true);
      const response = await getRequestDetailCutsByRequestDetail(requestDetailId);
      setCuts(response.data);
    } catch (error) {
      console.error("Errore nel caricamento dei tagli:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i tagli",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (requestDetailId) {
      loadCuts();
    }
  }, [requestDetailId]);

  // Gestisce l'apertura del dialog per nuovo taglio
  const handleAddCut = () => {
    setEditingCut(null);
    setIsDialogOpen(true);
  };

  // Gestisce l'apertura del dialog per modifica taglio
  const handleEditCut = (cut: RequestDetailCut) => {
    setEditingCut(cut);
    setIsDialogOpen(true);
  };

  // Gestisce il salvataggio (creazione o modifica)
  const handleSubmit = async (formData: RequestDetailCutFormData) => {
    try {
      setIsSubmitting(true);

      const dataWithRequestDetail = {
        ...formData,
        idRequestDetails: requestDetailId
      };

      if (editingCut) {
        // Modifica
        await updateRequestDetailCut(editingCut.id, dataWithRequestDetail);
        toast({
          title: "Successo",
          description: "Taglio aggiornato con successo",
        });
      } else {
        // Creazione
        await createRequestDetailCut(dataWithRequestDetail);
        toast({
          title: "Successo",
          description: "Taglio creato con successo",
        });
      }

      setIsDialogOpen(false);
      setEditingCut(null);
      loadCuts(); // Ricarica la lista
    } catch (error) {
      console.error("Errore nel salvataggio del taglio:", error);
      toast({
        title: "Errore",
        description: "Impossibile salvare il taglio",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Gestisce l'eliminazione
  const handleDeleteCut = async (cutId: number) => {
    if (!confirm("Sei sicuro di voler eliminare questo taglio?")) {
      return;
    }

    try {
      await deleteRequestDetailCut(cutId);
      toast({
        title: "Successo",
        description: "Taglio eliminato con successo",
      });
      loadCuts(); // Ricarica la lista
    } catch (error) {
      console.error("Errore nell'eliminazione del taglio:", error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare il taglio",
        variant: "destructive",
      });
    }
  };

  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingCut(null);
  };

  // Gestisce l'apertura del dialog per gestire processing
  const handleManageProcessing = (cut: RequestDetailCut) => {
    setSelectedCutForProcessing(cut);
    setIsProcessingDialogOpen(true);
  };

  // Gestisce l'aggiunta di processing selezionati
  const handleAddProcessing = async (selectedProcessing: any[]) => {
    if (!selectedCutForProcessing) return;

    try {
      // Crea le associazioni cut-processing per ogni processing selezionato
      for (const proc of selectedProcessing) {
        await createCutProcessing({
          cutId: selectedCutForProcessing.id,
          processingId: proc.id,
          quantity: proc.quantity,
          notes: `Processing ${proc.id} aggiunto automaticamente`
        });
      }

      toast({
        title: "Successo",
        description: `${selectedProcessing.length} processing aggiunti al taglio`,
      });

      loadCuts(); // Ricarica la lista per mostrare i processing associati
    } catch (error) {
      console.error("Errore nell'aggiunta dei processing:", error);
      toast({
        title: "Errore",
        description: "Impossibile aggiungere i processing",
        variant: "destructive",
      });
    }
  };

  // Gestisce la chiusura del dialog processing
  const handleCloseProcessingDialog = () => {
    setIsProcessingDialogOpen(false);
    setSelectedCutForProcessing(null);
  };

  // Funzione per ottenere il colore del badge in base allo stato
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "default";
      case "IN_PROGRESS":
        return "secondary";
      case "PENDING":
        return "outline";
      case "CANCELLED":
        return "destructive";
      default:
        return "outline";
    }
  };

  // Funzione per ottenere il testo dello stato
  const getStatusText = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "Completato";
      case "IN_PROGRESS":
        return "In Corso";
      case "PENDING":
        return "In Attesa";
      case "CANCELLED":
        return "Annullato";
      default:
        return status;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Tagli</CardTitle>
          <Button onClick={handleAddCut} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Aggiungi Taglio
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-4">Caricamento...</div>
        ) : cuts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            Nessun taglio presente
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Prezzo</TableHead>
                <TableHead>Stato</TableHead>
                <TableHead>Note</TableHead>
                <TableHead className="w-[100px]">Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cuts.map((cut) => (
                <TableRow key={cut.id}>
                  <TableCell>{cut.id}</TableCell>
                  <TableCell>
                    {cut.cutPrice ? `€${cut.cutPrice.toFixed(2)}` : "-"}
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(cut.status || "")}>
                      {getStatusText(cut.status || "")}
                    </Badge>
                  </TableCell>
                  <TableCell className="max-w-[200px] truncate">
                    {cut.notes || "-"}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleManageProcessing(cut)}
                        title="Gestisci Processing"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCut(cut)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCut(cut.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* Dialog per creazione/modifica */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingCut ? "Modifica Taglio" : "Nuovo Taglio"}
              </DialogTitle>
            </DialogHeader>
            <CutForm
              initialData={editingCut ? {
                id: editingCut.id,
                idRequestDetails: editingCut.idRequestDetails,
                notes: editingCut.notes,
                cutPrice: editingCut.cutPrice,
                status: editingCut.status,
              } : undefined}
              onSubmit={handleSubmit}
              onCancel={handleCloseDialog}
              isLoading={isSubmitting}
            />
          </DialogContent>
        </Dialog>

        {/* Dialog per selezione processing */}
        <ProcessingSelectionDialog
          isOpen={isProcessingDialogOpen}
          onClose={handleCloseProcessingDialog}
          onAddSelectedProcessing={handleAddProcessing}
        />
      </CardContent>
    </Card>
  );
}
