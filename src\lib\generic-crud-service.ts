import axiosInstance from "@/lib/axiosInstance";

/**
 * Generic CRUD Service Interface
 * Defines the contract for all CRUD operations
 */
export interface ICrudService<T, CreateT = Partial<T>, UpdateT = Partial<T>> {
  getAll(params?: Record<string, any>): Promise<T[]>;
  getById(id: string): Promise<T>;
  create(data: CreateT): Promise<T>;
  update(id: string, data: UpdateT): Promise<T>;
  delete(id: string): Promise<void>;
}

/**
 * Configuration options for the Generic CRUD Service
 */
export interface CrudServiceConfig {
  /** Base API path (e.g., "/requests", "/tires") */
  basePath: string;
  /** Custom transformation function for request data (camelCase to snake_case) */
  transformRequest?: (data: any) => any;
  /** Custom transformation function for response data (snake_case to camelCase) */
  transformResponse?: (data: any) => any;
  /** Custom error handler */
  onError?: (error: any, operation: string, id?: string) => void;
}

/**
 * Data transformation utilities
 */
export class DataTransformer {
  /**
   * Convert camelCase object to snake_case
   */
  static camelToSnake(obj: any): any {
    if (obj === null || obj === undefined) return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => this.camelToSnake(item));
    }

    if (obj instanceof Date) {
      return obj.toISOString();
    }

    if (typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        result[snakeKey] = this.camelToSnake(value);
      }
      return result;
    }

    return obj;
  }

  /**
   * Convert snake_case object to camelCase
   */
  static snakeToCamel(obj: any): any {
    if (obj === null || obj === undefined) return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => this.snakeToCamel(item));
    }

    if (typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        result[camelKey] = this.snakeToCamel(value);
      }
      return result;
    }

    return obj;
  }
}

/**
 * Generic CRUD Service Implementation
 * Consolidates common CRUD operations with type safety and error handling
 *
 * @template T - The entity type (e.g., Request, Tire, User)
 * @template CreateT - The creation data type (defaults to Partial<T>)
 * @template UpdateT - The update data type (defaults to Partial<T>)
 *
 * @example
 * ```typescript
 * // Create a service for requests
 * const requestService = new GenericCrudService<AppRequest, RequestFormData>({
 *   basePath: '/requests',
 *   transformRequest: DataTransformer.camelToSnake,
 *   transformResponse: DataTransformer.snakeToCamel
 * });
 *
 * // Use the service
 * const requests = await requestService.getAll({ skip: 0, limit: 10 });
 * const newRequest = await requestService.create(requestData);
 * ```
 */
export class GenericCrudService<T, CreateT = Partial<T>, UpdateT = Partial<T>>
  implements ICrudService<T, CreateT, UpdateT> {

  private config: CrudServiceConfig;

  constructor(config: CrudServiceConfig) {
    this.config = config;
  }

  /**
   * Get all entities with optional query parameters
   * @param params - Query parameters (pagination, filters, etc.)
   * @returns Promise<T[]> - Array of entities
   */
  async getAll(params?: Record<string, any>): Promise<T[]> {
    try {
      const response = await axiosInstance.get<T[]>(this.config.basePath, { params });
      const data = this.config.transformResponse
        ? this.config.transformResponse(response.data)
        : response.data;
      return data;
    } catch (error) {
      this.handleError(error, 'getAll');
      throw error;
    }
  }

  /**
   * Get a single entity by ID
   * @param id - Entity identifier
   * @returns Promise<T> - The entity
   */
  async getById(id: string): Promise<T> {
    try {
      const response = await axiosInstance.get<T>(`${this.config.basePath}/${id}`);
      const data = this.config.transformResponse
        ? this.config.transformResponse(response.data)
        : response.data;
      return data;
    } catch (error) {
      this.handleError(error, 'getById', id);
      throw error;
    }
  }

  /**
   * Create a new entity
   * @param data - Entity creation data
   * @returns Promise<T> - The created entity
   */
  async create(data: CreateT): Promise<T> {
    try {
      const transformedData = this.config.transformRequest
        ? this.config.transformRequest(data)
        : data;

      const response = await axiosInstance.post<T>(this.config.basePath, transformedData);
      const responseData = this.config.transformResponse
        ? this.config.transformResponse(response.data)
        : response.data;
      return responseData;
    } catch (error) {
      this.handleError(error, 'create');
      throw error;
    }
  }

  /**
   * Update an existing entity
   * @param id - Entity identifier
   * @param data - Entity update data
   * @returns Promise<T> - The updated entity
   */
  async update(id: string, data: UpdateT): Promise<T> {
    try {
      const transformedData = this.config.transformRequest
        ? this.config.transformRequest(data)
        : data;

      const response = await axiosInstance.put<T>(`${this.config.basePath}/${id}`, transformedData);
      const responseData = this.config.transformResponse
        ? this.config.transformResponse(response.data)
        : response.data;
      return responseData;
    } catch (error) {
      this.handleError(error, 'update', id);
      throw error;
    }
  }

  /**
   * Delete an entity
   * @param id - Entity identifier
   * @returns Promise<void>
   */
  async delete(id: string): Promise<void> {
    try {
      await axiosInstance.delete(`${this.config.basePath}/${id}`);
    } catch (error) {
      this.handleError(error, 'delete', id);
      throw error;
    }
  }

  /**
   * Custom method for additional endpoints
   * @param endpoint - Additional endpoint path
   * @param method - HTTP method
   * @param data - Optional data for POST/PUT requests
   * @param params - Optional query parameters
   * @returns Promise<any>
   */
  async customRequest(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any,
    params?: Record<string, any>
  ): Promise<any> {
    try {
      const url = `${this.config.basePath}/${endpoint}`;
      let response;

      switch (method) {
        case 'GET':
          response = await axiosInstance.get(url, { params });
          break;
        case 'POST':
          const postData = this.config.transformRequest && data
            ? this.config.transformRequest(data)
            : data;
          response = await axiosInstance.post(url, postData);
          break;
        case 'PUT':
          const putData = this.config.transformRequest && data
            ? this.config.transformRequest(data)
            : data;
          response = await axiosInstance.put(url, putData);
          break;
        case 'DELETE':
          response = await axiosInstance.delete(url);
          break;
      }

      return this.config.transformResponse
        ? this.config.transformResponse(response.data)
        : response.data;
    } catch (error) {
      this.handleError(error, `customRequest:${method}:${endpoint}`);
      throw error;
    }
  }

  /**
   * Handle errors with optional custom error handler
   * @private
   */
  private handleError(error: any, operation: string, id?: string): void {
    const errorMessage = `[GenericCrudService] Error in ${operation}${id ? ` for ID ${id}` : ''}:`;
    console.error(errorMessage, error);

    if (this.config.onError) {
      this.config.onError(error, operation, id);
    }
  }
}

/**
 * Factory function to create pre-configured CRUD services
 */
export class CrudServiceFactory {
  /**
   * Create a standard CRUD service with snake_case/camelCase transformation
   */
  static createStandardService<T, CreateT = Partial<T>, UpdateT = Partial<T>>(
    basePath: string,
    customErrorHandler?: (error: any, operation: string, id?: string) => void
  ): GenericCrudService<T, CreateT, UpdateT> {
    return new GenericCrudService<T, CreateT, UpdateT>({
      basePath,
      transformRequest: DataTransformer.camelToSnake,
      transformResponse: DataTransformer.snakeToCamel,
      onError: customErrorHandler
    });
  }

  /**
   * Create a simple CRUD service without data transformation
   */
  static createSimpleService<T, CreateT = Partial<T>, UpdateT = Partial<T>>(
    basePath: string,
    customErrorHandler?: (error: any, operation: string, id?: string) => void
  ): GenericCrudService<T, CreateT, UpdateT> {
    return new GenericCrudService<T, CreateT, UpdateT>({
      basePath,
      onError: customErrorHandler
    });
  }
}