
"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import type { RequestFormData, AttachmentFile } from "@/types";
import { cn } from "@/lib/utils";

interface ReportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  reportData: RequestFormData | null;
}

const ReportItem: React.FC<{ label: string; value: React.ReactNode; className?: string }> = ({ label, value, className }) => (
  <div className={cn("grid grid-cols-3 gap-2 py-2 border-b border-border/30 last:border-b-0", className)}>
    <Label className="font-medium text-muted-foreground col-span-1">{label}:</Label>
    <div className="col-span-2 text-sm break-words">
      {value === undefined || value === null || (typeof value === 'string' && value.trim() === "") ?
        <span className="italic text-muted-foreground/80">N/A</span> :
        value
      }
    </div>
  </div>
);

export function ReportDialog({ isOpen, onClose, reportData }: ReportDialogProps) {
  if (!isOpen || !reportData) {
    return null;
  }

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return null; // Let ReportItem handle N/A
    try {
      return format(new Date(date), "PPP p");
    } catch (error) {
      return "Data non valida";
    }
  };

  const formatAttachments = (attachments?: AttachmentFile[]) => {
    if (!attachments || attachments.length === 0) return null; // Let ReportItem handle N/A
    return (
      <ul className="list-disc list-inside space-y-0.5">
        {attachments.map(att => {
          let dateStr = "N/A";
          if (att.uploadDate) {
            try {
              const d = new Date(att.uploadDate);
              if (!isNaN(d.getTime())) {
                dateStr = format(d, "dd/MM/yy");
              } else {
                dateStr = "Data non valida";
              }
            } catch {
              dateStr = "Data non valida";
            }
          }
          return (
            <li key={att.id} className="text-xs">
              {att.name} ({(att.size / 1024).toFixed(2)} KB) - {dateStr}
            </li>
          );
        })}
      </ul>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
      <DialogContent className="max-w-2xl w-[90vw] md:w-full max-h-[85vh] flex flex-col p-0">
        <DialogHeader className="p-4 border-b sticky top-0 bg-background z-10">
          <DialogTitle className="text-xl font-semibold text-center">
            Riepilogo Richiesta: {reportData.id || "Nuova Richiesta"}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-grow p-4">
          <div className="space-y-0">
            <ReportItem label="ID Richiesta" value={reportData.id || "Nuova Richiesta"} />
            <ReportItem label="Stato" value={reportData.status} />
            <ReportItem label="Richiesta Da" value={reportData.requestBy} />
            <ReportItem label="Tipo" value={reportData.type} />
            <ReportItem label="Data Richiesta" value={formatDate(reportData.requestDate)} />
            <ReportItem label="Data Target" value={formatDate(reportData.targetDate)} />
            <ReportItem label="Data Desiderata" value={formatDate(reportData.wishDate)} />

            <ReportItem label="PID/Progetto" value={reportData.pidProject || reportData.projectNo} />
            <ReportItem label="Misura Pneumatici" value={reportData.tireSize} />
            <ReportItem label="Totale N° Pneumatici" value={reportData.totalN} />
            <ReportItem label="Num. Pneus x Set" value={`${reportData.numPneusSet ?? 'N/A'} (${reportData.numPneusSetUnit || 'SET'})`} />

            <ReportItem label="Destinazione" value={reportData.destination} />
            <ReportItem label="Interna" value={reportData.internal ? "Sì" : "No"} />
            <ReportItem label="Responsabile" value={reportData.inChargeOf} />

            <ReportItem
              label="Note"
              value={reportData.note ? <p className="whitespace-pre-wrap text-sm bg-muted/30 p-2 rounded-sm">{reportData.note}</p> : null}
              className="items-start"
            />
            <ReportItem
              label="Allegati"
              value={formatAttachments(reportData.attachments)}
              className="items-start"
            />
          </div>
        </ScrollArea>

        <DialogFooter className="p-4 border-t sticky bottom-0 bg-background z-10">
          <DialogClose asChild>
            <Button variant="default">Chiudi</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
