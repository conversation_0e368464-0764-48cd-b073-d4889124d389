"use client";

import * as React from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Database, Copy, ListFilter, Settings } from "lucide-react";
import type { Tire, DialogTire } from "@/types";
import { cn } from "@/lib/utils";
import { TireManagementDialog } from "@/components/tire-management/tire-management-dialog";
import { TireManagementButton } from "@/components/tire-management/tire-management-button";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";
import { useToast } from "@/hooks/use-toast";
import { deleteRequestDetail } from "@/services/requestDetailService";

interface TiresSectionProps {
  tires: Tire[];
  selectedTireId: string | null;
  onRowClick: (id: string) => void;
  onProcessingType: () => void;
  onCopyTire: () => void;
  onManageCuts?: (tire: Tire) => void;
  processingNSums: Record<string, number>;
  onUpdateProcessingSums: (updater: (prev: Record<string, number>) => Record<string, number>) => void;
  onUpdateTires: (updater: (prev: Tire[]) => Tire[]) => void;
}

const DispositionBadge = ({ disposition }: { disposition: Tire["disposition"] }) => {
  let variant: "default" | "secondary" | "destructive" | "outline" | "accent" | "tested" | "available" | "processing" = "outline";
  switch (disposition) {
    case "AVAILABLE": variant = "available"; break;
    case "SCRAP": variant = "destructive"; break;
    case "TESTED": variant = "tested"; break;
    case "REPAIR": variant = "processing"; break;
    default: variant = "outline";
  }
  return <Badge variant={variant} className="font-medium">{disposition}</Badge>;
};

const ProcessingCountBadge = ({ count }: { count: number }) => {
  if (count === 0) {
    return <span className="text-gray-400 font-medium">0</span>;
  }
  return (
    <Badge variant="info" className="font-medium">
      {count}
    </Badge>
  );
};


export function TiresSection({
  tires,
  selectedTireId,
  onRowClick,
  onProcessingType,
  onCopyTire,
  onManageCuts,
  processingNSums,
  onUpdateProcessingSums,
  onUpdateTires
}: TiresSectionProps) {
  const [isTireManagementDialogOpen, setIsTireManagementDialogOpen] = React.useState(false);
  const { toast } = useToast();

  const handleAddTiresFromManagement = (selectedTires: DialogTire[]) => {
    if (selectedTires.length === 0) return;

    console.log('handleAddTiresFromManagement: Adding tires from management:', selectedTires);

    // Create new tire entries from the selected dialog tires
    const newTires: Tire[] = selectedTires.map((dialogTire, index) => {
      console.log(`Processing tire ${index}:`, {
        tugNo: dialogTire.tugNo,
        projectNo: dialogTire.projectNo,
        specNo: dialogTire.specNo,
        size: dialogTire.size,
        pattern: dialogTire.pattern
      });

      // Improved mapping with better fallback values and validation
      const mappedTire = {
        id: `MGT-${Date.now()}-${index}`,
        tugNo: dialogTire.tugNo && dialogTire.tugNo !== "N/A" ? dialogTire.tugNo : `TUG-${Date.now()}-${index}`,
        section: "FROM_MANAGEMENT",
        projectNo: dialogTire.projectNo && dialogTire.projectNo !== "N/A" ? dialogTire.projectNo : "",
        specNo: dialogTire.specNo && dialogTire.specNo !== "N/A" ? dialogTire.specNo : "",
        tireSize: dialogTire.size && dialogTire.size !== "N/A" ? dialogTire.size : "",
        pattern: dialogTire.pattern && dialogTire.pattern !== "N/A" ? dialogTire.pattern : "",
        note: `Added via management. Owner: ${dialogTire.owner || "Unknown"}, LI: ${dialogTire.loadIndex || "Unknown"}, Loc: ${dialogTire.location || "Unknown"}`,
        disposition: "AVAILABLE" as const,
        quantity: 1,
      };

      // Log any fields that are empty after mapping
      const emptyFields = [];
      if (!mappedTire.projectNo) emptyFields.push('projectNo');
      if (!mappedTire.specNo) emptyFields.push('specNo');
      if (!mappedTire.tireSize) emptyFields.push('tireSize');
      if (!mappedTire.pattern) emptyFields.push('pattern');

      if (emptyFields.length > 0) {
        console.warn(`Tire ${mappedTire.tugNo} has empty fields:`, emptyFields);
        console.warn('Original DialogTire data:', dialogTire);
      }

      return mappedTire;
    });

    console.log('handleAddTiresFromManagement: Created new tires:', newTires);

    // Add the new tires to the existing tires array
    onUpdateTires(prev => [...prev, ...newTires]);

    // Initialize processing sums for the new tires
    const newSums = newTires.reduce((acc, tire) => {
      acc[tire.id] = 0;
      return acc;
    }, {} as Record<string, number>);
    // Add the new sums to the existing sums
    processingNSums && onUpdateProcessingSums((prevSums: Record<string, number>) => ({ ...prevSums, ...newSums }));

    // Close the dialog
    setIsTireManagementDialogOpen(false);
  };

  // Handle tire deletion
  const handleDeleteTire = async (tireId: string) => {
    try {
      // Call the API to delete the tire
      await deleteRequestDetail(tireId);

      // Remove the tire from the local state
      onUpdateTires(prev => prev.filter(tire => tire.id !== tireId));

      // Remove the processing sum for this tire
      onUpdateProcessingSums(prev => {
        const newSums = { ...prev };
        delete newSums[tireId];
        return newSums;
      });

      // Show success message
      toast({
        title: "Tire Deleted",
        description: `Tire has been successfully removed.`
      });

      // If the deleted tire was selected, clear the selection
      if (selectedTireId === tireId) {
        onRowClick("");
      }
    } catch (error: any) {
      // Show error message
      toast({
        title: "Delete Failed",
        description: error?.message || "Error deleting tire.",
        variant: "destructive"
      });
    }
  };
  return (
    <section aria-labelledby="tires-table-heading" className="space-y-6">
      <div className="flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
        <div className="flex items-center gap-3">
          <div className="w-2 h-8 bg-blue-500 rounded-full"></div>
          <div>
            <h2 id="tires-table-heading" className="text-xl font-semibold text-gray-900">
              Request Details
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {tires.length} {tires.length === 1 ? 'tire' : 'tires'} in this request
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onProcessingType}
            aria-label="Processing type"
            className="rounded-md border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-colors"
          >
            <Database className="h-4 w-4 mr-2" />
            ZORRO
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsTireManagementDialogOpen(true)}
            className="text-blue-600 border-blue-300 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-400 rounded-md transition-colors"
            aria-label="Manage tires"
          >
            <ListFilter className="h-4 w-4 mr-2" />
            Manage
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onCopyTire}
            aria-label="Copy selected tire"
            disabled={!selectedTireId}
            className="rounded-md border-gray-300 hover:border-green-400 hover:bg-green-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy
          </Button>
        </div>
      </div>
      <div className="rounded-lg bg-card border shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b bg-muted/30">
              <TableHead className="font-semibold text-foreground">Tug#</TableHead>
              <TableHead className="font-semibold text-foreground">Section</TableHead>
              <TableHead className="font-semibold text-foreground">Project#</TableHead>
              <TableHead className="font-semibold text-foreground">Spec#</TableHead>
              <TableHead className="font-semibold text-foreground">Tire Size</TableHead>
              <TableHead className="font-semibold text-foreground">Pattern</TableHead>
              <TableHead className="font-semibold text-foreground">Note</TableHead>
              <TableHead className="font-semibold text-foreground">Disposition</TableHead>
              <TableHead className="font-semibold text-foreground text-center">N° (Process.)</TableHead>
              <TableHead className="w-[120px] font-semibold text-foreground">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tires.map((tire) => {
              const displayQuantity = processingNSums[tire.id] ?? 0;
              return (
                <TableRow
                  key={tire.id}
                  data-state={selectedTireId === tire.id ? "selected" : ""}
                  onClick={() => onRowClick(tire.id)}
                  className={cn(
                    "cursor-pointer transition-colors border-b border-border/50",
                    selectedTireId === tire.id
                      ? "bg-blue-50 hover:bg-blue-100 border-blue-200"
                      : "hover:bg-muted/50"
                  )}
                  aria-selected={selectedTireId === tire.id}
                >
                  <TableCell className="font-medium text-foreground">{tire.tugNo}</TableCell>
                  <TableCell className="text-muted-foreground">{tire.section}</TableCell>
                  <TableCell className="text-muted-foreground">{tire.projectNo}</TableCell>
                  <TableCell className="text-muted-foreground">{tire.specNo}</TableCell>
                  <TableCell className="font-medium">{tire.tireSize}</TableCell>
                  <TableCell className="text-muted-foreground">{tire.pattern}</TableCell>
                  <TableCell className="text-muted-foreground max-w-[150px] truncate" title={tire.note || 'N/A'}>
                    {tire.note ? (tire.note.length > 20 ? tire.note.substring(0, 20) + '...' : tire.note) : 'N/A'}
                  </TableCell>
                  <TableCell><DispositionBadge disposition={tire.disposition} /></TableCell>
                  <TableCell className="text-center">
                    <ProcessingCountBadge count={displayQuantity} />
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      {onManageCuts && (
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            onManageCuts(tire);
                          }}
                          title="Gestisci Tagli"
                          className="h-8 w-8 rounded-md border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-colors"
                        >
                          <Settings className="h-4 w-4 text-gray-600 hover:text-blue-600" />
                        </Button>
                      )}
                      <DeleteButton
                        onClick={() => handleDeleteTire(tire.id)}
                        title="Confirm Tire Deletion"
                        description={`Are you sure you want to delete this tire? This action cannot be undone.`}
                        itemType="tire"
                        itemName={tire.tugNo}
                        size="icon"
                        className="h-8 w-8 rounded-md border-red-300 hover:border-red-400 hover:bg-red-50 text-red-600 hover:text-red-700 transition-colors"
                      />
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
            {tires.length === 0 && (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-12">
                  <div className="flex flex-col items-center gap-3 text-muted-foreground">
                    <Database className="h-12 w-12 text-gray-300" />
                    <div>
                      <p className="font-medium text-gray-600">No tires found</p>
                      <p className="text-sm text-gray-500">This request doesn't have any tire details yet.</p>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Tire Management Dialog */}
      <TireManagementDialog
        open={isTireManagementDialogOpen}
        onOpenChange={setIsTireManagementDialogOpen}
        onAddSelectedTires={handleAddTiresFromManagement}
      />
    </section>
  );
}
