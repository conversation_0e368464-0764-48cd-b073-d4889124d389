/**
 * Utility functions for handling form inputs safely
 */

/**
 * Safely converts any value to a string for use in input components
 * Prevents React warnings about null/undefined values in controlled inputs
 * @param value - The value to convert
 * @returns Empty string if value is null/undefined, otherwise string representation
 */
export function safeStringValue(value: string | null | undefined): string {
  return value ?? "";
}

/**
 * Safely converts any value to a string for use in input components
 * with a fallback value
 * @param value - The value to convert
 * @param fallback - The fallback value to use if value is null/undefined
 * @returns Fallback if value is null/undefined, otherwise string representation
 */
export function safeStringValueWithFallback(
  value: string | null | undefined, 
  fallback: string = ""
): string {
  return value ?? fallback;
}

/**
 * Safely handles numeric input values
 * @param value - The numeric value
 * @returns Empty string if undefined/null, otherwise the number as string
 */
export function safeNumericValue(value: number | null | undefined): string {
  if (value === null || value === undefined) return "";
  return String(value);
}

/**
 * Safely handles boolean values for checkboxes
 * @param value - The boolean value
 * @returns false if null/undefined, otherwise the boolean value
 */
export function safeBooleanValue(value: boolean | null | undefined): boolean {
  return value ?? false;
}

/**
 * Safely handles date values for date inputs
 * @param value - The date value
 * @returns undefined if null, otherwise the date
 */
export function safeDateValue(value: Date | string | null | undefined): Date | undefined {
  if (!value) return undefined;
  if (value instanceof Date) return value;
  const date = new Date(value);
  return isNaN(date.getTime()) ? undefined : date;
}

/**
 * Validates and safely parses a numeric string input
 * @param value - The string value from input
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @returns Parsed number or undefined if invalid
 */
export function parseNumericInput(
  value: string, 
  min?: number, 
  max?: number
): number | undefined {
  if (value === "") return undefined;
  
  const numValue = parseFloat(value);
  if (isNaN(numValue)) return undefined;
  
  if (min !== undefined && numValue < min) return undefined;
  if (max !== undefined && numValue > max) return undefined;
  
  return numValue;
}

/**
 * Validates and safely parses an integer string input
 * @param value - The string value from input
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @returns Parsed integer or undefined if invalid
 */
export function parseIntegerInput(
  value: string, 
  min?: number, 
  max?: number
): number | undefined {
  if (value === "") return undefined;
  
  const numValue = parseInt(value, 10);
  if (isNaN(numValue)) return undefined;
  
  if (min !== undefined && numValue < min) return undefined;
  if (max !== undefined && numValue > max) return undefined;
  
  return numValue;
}

/**
 * Creates a safe input change handler that prevents null/undefined values
 * @param onChange - The original change handler
 * @returns A safe change handler that converts null/undefined to empty string
 */
export function createSafeInputHandler(
  onChange: (value: string) => void
): (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void {
  return (event) => {
    const value = event.target.value;
    onChange(safeStringValue(value));
  };
}

/**
 * Creates a safe numeric input change handler
 * @param onChange - The original change handler
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @returns A safe change handler for numeric inputs
 */
export function createSafeNumericHandler(
  onChange: (value: number | undefined) => void,
  min?: number,
  max?: number
): (event: React.ChangeEvent<HTMLInputElement>) => void {
  return (event) => {
    const value = event.target.value;
    const numValue = parseNumericInput(value, min, max);
    onChange(numValue);
  };
}

/**
 * Type guard to check if a value is not null or undefined
 * @param value - The value to check
 * @returns True if value is not null or undefined
 */
export function isNotNullOrUndefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Safely formats a value for display in read-only inputs
 * @param value - The value to format
 * @param fallback - Fallback text for null/undefined values
 * @returns Formatted string for display
 */
export function formatDisplayValue(
  value: string | number | null | undefined, 
  fallback: string = "N/A"
): string {
  if (value === null || value === undefined) return fallback;
  return String(value);
}
