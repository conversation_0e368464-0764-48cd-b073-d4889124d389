<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tire Empty Fields Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>Test Tire Empty Fields Fix</h1>
    <p>Questo test verifica che i campi vuoti nella tabella dei tire siano stati risolti.</p>

    <div class="test-section">
        <h2>Test 1: Trasformazione Snake Case → Camel Case</h2>
        <button onclick="testTransformation()">Esegui Test Trasformazione</button>
        <div id="transformation-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: API Call con Trasformazione</h2>
        <button onclick="testApiCall()">Test API Call</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Visualizzazione Dati Tire</h2>
        <button onclick="testTireDisplay()">Test Visualizzazione</button>
        <div id="display-result"></div>
        <table id="tire-table" style="display: none;">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>TUG Number</th>
                    <th>Spec Number</th>
                    <th>Size</th>
                    <th>Owner</th>
                    <th>Load Index</th>
                    <th>Pattern</th>
                    <th>Project Number</th>
                    <th>Location</th>
                </tr>
            </thead>
            <tbody id="tire-table-body">
            </tbody>
        </table>
    </div>

    <script>
        // Funzioni di trasformazione (copiate dal servizio)
        function transformSnakeToCamel(obj) {
            if (obj === null || obj === undefined) return obj;
            if (Array.isArray(obj)) return obj.map(transformSnakeToCamel);
            if (typeof obj !== 'object') return obj;

            const transformed = {};
            for (const [key, value] of Object.entries(obj)) {
                const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
                transformed[camelKey] = transformSnakeToCamel(value);
            }
            return transformed;
        }

        function testTransformation() {
            const resultDiv = document.getElementById('transformation-result');
            resultDiv.innerHTML = '<div class="loading">Eseguendo test...</div>';

            // Dati di test in formato snake_case (come arrivano dal backend)
            const snakeCaseData = [
                {
                    id: "T_TEST_NEW",
                    tug_no: "225/45R17",
                    spec_no: "SPEC001",
                    size: "225/45R17",
                    owner: "TestOwner",
                    load_index: "94",
                    pattern: "TEST_PATTERN",
                    project_no: "PROJ001",
                    location: "TEST_SHELF_1",
                    description: "Test tire",
                    is_active: true
                },
                {
                    id: "T001",
                    tug_no: "225/45R17",
                    spec_no: "",  // Campo vuoto per testare
                    size: "225/45R17",
                    owner: "Bridgestone",
                    load_index: "",  // Campo vuoto per testare
                    pattern: "Potenza S007",
                    project_no: "",  // Campo vuoto per testare
                    location: "Shelf 1",
                    description: null,
                    is_active: true
                }
            ];

            // Trasforma i dati
            const camelCaseData = transformSnakeToCamel(snakeCaseData);

            // Verifica i risultati
            let html = '<div class="success">✓ Trasformazione completata!</div>';
            html += '<h4>Dati Originali (snake_case):</h4>';
            html += '<pre>' + JSON.stringify(snakeCaseData, null, 2) + '</pre>';
            html += '<h4>Dati Trasformati (camelCase):</h4>';
            html += '<pre>' + JSON.stringify(camelCaseData, null, 2) + '</pre>';

            // Verifica che i campi siano stati trasformati correttamente
            const firstItem = camelCaseData[0];
            if (firstItem.tugNo && firstItem.specNo && firstItem.loadIndex && firstItem.projectNo) {
                html += '<div class="success">✓ Tutti i campi sono stati trasformati correttamente!</div>';
            } else {
                html += '<div class="error">✗ Alcuni campi non sono stati trasformati correttamente</div>';
            }

            resultDiv.innerHTML = html;
        }

        async function testApiCall() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="loading">Chiamando API...</div>';

            try {
                // Simula una chiamata API
                const response = await fetch('http://localhost:8002/tires?limit=5', {
                    headers: {
                        'Authorization': 'Bearer test-token',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    let html = '<div class="success">✓ API chiamata con successo!</div>';
                    html += '<div class="info">Dati ricevuti: ' + data.length + ' tire</div>';

                    if (data.length > 0) {
                        const firstTire = data[0];
                        html += '<h4>Primo tire ricevuto:</h4>';
                        html += '<pre>' + JSON.stringify(firstTire, null, 2) + '</pre>';

                        // Verifica se i campi sono presenti
                        const hasAllFields = firstTire.tugNo && firstTire.specNo &&
                                           firstTire.loadIndex && firstTire.projectNo;

                        if (hasAllFields) {
                            html += '<div class="success">✓ Tutti i campi sono presenti nei dati!</div>';
                        } else {
                            html += '<div class="error">✗ Alcuni campi sono ancora mancanti</div>';
                        }
                    }

                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">✗ Errore nella chiamata API: ${error.message}</div>
                    <div class="info">Assicurati che il backend sia in esecuzione su localhost:8000</div>
                `;
            }
        }

        async function testTireDisplay() {
            const resultDiv = document.getElementById('display-result');
            const table = document.getElementById('tire-table');
            const tbody = document.getElementById('tire-table-body');

            resultDiv.innerHTML = '<div class="loading">Caricando dati tire...</div>';
            table.style.display = 'none';

            try {
                const response = await fetch('http://localhost:8002/tires?limit=10', {
                    headers: {
                        'Authorization': 'Bearer test-token',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const rawData = await response.json();

                    // Applica la trasformazione (come fa il servizio)
                    const transformedData = transformSnakeToCamel(rawData);

                    let html = '<div class="success">✓ Dati caricati e trasformati con successo!</div>';
                    html += `<div class="info">Trovati ${transformedData.length} tire</div>`;

                    // Popola la tabella
                    tbody.innerHTML = '';
                    transformedData.forEach(tire => {
                        const row = tbody.insertRow();
                        row.insertCell(0).textContent = tire.id || 'N/A';
                        row.insertCell(1).textContent = tire.tugNo || 'N/A';
                        row.insertCell(2).textContent = tire.specNo || 'N/A';
                        row.insertCell(3).textContent = tire.size || 'N/A';
                        row.insertCell(4).textContent = tire.owner || 'N/A';
                        row.insertCell(5).textContent = tire.loadIndex || 'N/A';
                        row.insertCell(6).textContent = tire.pattern || 'N/A';
                        row.insertCell(7).textContent = tire.projectNo || 'N/A';
                        row.insertCell(8).textContent = tire.location || 'N/A';
                    });

                    // Verifica se ci sono campi vuoti
                    const emptyFields = transformedData.some(tire =>
                        !tire.tugNo || !tire.specNo || !tire.loadIndex || !tire.projectNo
                    );

                    if (!emptyFields) {
                        html += '<div class="success">✓ Nessun campo vuoto trovato nella tabella!</div>';
                    } else {
                        html += '<div class="error">✗ Alcuni campi sono ancora vuoti</div>';
                    }

                    resultDiv.innerHTML = html;
                    table.style.display = 'table';
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">✗ Errore nel caricamento: ${error.message}</div>
                    <div class="info">Assicurati che il backend sia in esecuzione su localhost:8000</div>
                `;
            }
        }

        // Esegui test automaticamente al caricamento della pagina
        window.onload = function() {
            console.log('Pagina di test caricata. Esegui i test manualmente cliccando i pulsanti.');
        };
    </script>
</body>
</html>
