/**
 * Request Service - PRIORITY 2 Refactored Version
 *
 * Demonstrates integration of PRIORITY 2 Data Transformation Layer
 * with PRIORITY 1 Generic CRUD Service patterns.
 *
 * This refactored version showcases:
 * - Enhanced Data Transformation Layer usage
 * - Integration with existing Generic CRUD Service
 * - Elimination of manual field mapping duplications
 * - Type-safe transformations with performance optimizations
 *
 * Author: Kilo Code
 * Date: 28 Maggio 2025
 * Version: 2.0 (PRIORITY 2 Implementation)
 */

import { CrudServiceFactory, GenericCrudService } from '@/lib/generic-crud-service';
import {
  EnhancedDataTransformer,
  TransformationPresets,
  TransformationUtils,
  TransformationOptions
} from '@/lib/data-transformer';
import { AppRequest, RequestFormData, AttachmentFile } from '@/types';

/**
 * Enhanced Request Service Configuration
 *
 * Extends the basic CRUD service with specialized transformation
 * and business logic for request management.
 */
class RequestServiceConfig {
  /** Base CRUD service instance */
  private crudService: GenericCrudService<AppRequest, RequestFormData>;

  /** Custom transformation options for requests */
  private transformationOptions: TransformationOptions;

  constructor() {
    // Initialize base CRUD service using PRIORITY 1 factory
    this.crudService = CrudServiceFactory.createStandardService<AppRequest, RequestFormData>(
      '/requests',
      this.handleServiceError.bind(this)
    );

    // Configure enhanced transformations for request-specific needs
    this.transformationOptions = {
      ...TransformationPresets.API_REQUEST,
      fieldMapping: {
        ...TransformationPresets.API_REQUEST.fieldMapping,
        fieldMappings: {
          // Request-specific field mappings (addresses duplication in requestService.ts:62)
          'requestBy': 'request_by',
          'projectNo': 'project_no',
          'tugNo': 'tug_no',
          'requestDate': 'request_date',
          'deliveryDate': 'delivery_date',
          'isUrgent': 'is_urgent',
          'requestStatus': 'request_status',
          'departmentId': 'department_id',
          'createdBy': 'created_by',
          'updatedBy': 'updated_by',
          // Attachment-specific mappings
          'fileName': 'file_name',
          'fileSize': 'file_size',
          'fileType': 'file_type',
          'uploadedAt': 'uploaded_at',
          'uploadedBy': 'uploaded_by'
        },
        customTransformers: {
          // Custom transformation for date fields
          requestDate: (value: any) => {
            if (value instanceof Date) return value.toISOString();
            if (typeof value === 'string') return new Date(value).toISOString();
            return value;
          },
          deliveryDate: (value: any) => {
            if (value instanceof Date) return value.toISOString();
            if (typeof value === 'string') return new Date(value).toISOString();
            return value;
          },
          // Custom transformation for attachments array
          attachments: (attachments: AttachmentFile[]) => {
            return TransformationUtils.transformAttachments(attachments);
          }
        }
      }
    };
  }

  /**
   * Get the configured CRUD service
   */
  getCrudService(): GenericCrudService<AppRequest, RequestFormData> {
    return this.crudService;
  }

  /**
   * Get transformation options
   */
  getTransformationOptions(): TransformationOptions {
    return this.transformationOptions;
  }

  /**
   * Custom error handler for service operations
   */
  private handleServiceError(error: any, operation: string, id?: string): void {
    console.error(`[RequestService] Error in ${operation}${id ? ` for ID ${id}` : ''}:`, error);

    // Add custom error handling logic here
    // e.g., analytics, user notifications, etc.
  }
}

// Initialize service configuration
const serviceConfig = new RequestServiceConfig();
const crudService = serviceConfig.getCrudService();
const transformationOptions = serviceConfig.getTransformationOptions();

/**
 * Enhanced Request Service API
 *
 * Provides a clean, type-safe interface for request operations
 * while leveraging both PRIORITY 1 and PRIORITY 2 improvements.
 */

/**
 * Get all requests with enhanced filtering and transformation
 *
 * @param params - Query parameters for filtering and pagination
 * @returns Promise<AppRequest[]> - Array of transformed requests
 *
 * @example
 * ```typescript
 * const requests = await getRequests({
 *   skip: 0,
 *   limit: 10,
 *   status: 'pending'
 * });
 * ```
 */
export const getRequests = async (params?: Record<string, any>): Promise<AppRequest[]> => {
  try {
    const requests = await crudService.getAll(params);

    // Apply enhanced response transformation
    return EnhancedDataTransformer.transformBatch(
      requests,
      (request) => EnhancedDataTransformer.snakeToCamel<any, AppRequest>(
        request,
        TransformationPresets.API_RESPONSE
      ),
      TransformationPresets.API_RESPONSE
    ) as AppRequest[];
  } catch (error) {
    console.error('[RequestService] Error getting requests:', error);
    throw error;
  }
};

/**
 * Get a single request by ID with enhanced transformation
 *
 * @param id - Request identifier
 * @returns Promise<AppRequest> - Transformed request
 */
export const getRequestById = async (id: string): Promise<AppRequest> => {
  try {
    const request = await crudService.getById(id);

    // Apply enhanced response transformation
    return TransformationUtils.transformApiResponse<AppRequest>(request);
  } catch (error) {
    console.error(`[RequestService] Error getting request ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new request with enhanced data transformation
 *
 * Addresses the duplication identified in requestService.ts:62 by using
 * the centralized transformation layer instead of manual field mapping.
 *
 * @param data - Request creation data
 * @returns Promise<AppRequest> - Created request
 */
export const createRequest = async (data: RequestFormData): Promise<AppRequest> => {
  try {
    // Apply enhanced request transformation (replaces manual mapping)
    const transformedData = EnhancedDataTransformer.camelToSnake(
      data,
      transformationOptions
    );

    const request = await crudService.create(transformedData);

    // Transform response back to camelCase
    return TransformationUtils.transformApiResponse<AppRequest>(request);
  } catch (error) {
    console.error('[RequestService] Error creating request:', error);
    throw error;
  }
};

/**
 * Update an existing request with enhanced transformation
 *
 * @param id - Request identifier
 * @param data - Request update data
 * @returns Promise<AppRequest> - Updated request
 */
export const updateRequest = async (id: string, data: Partial<RequestFormData>): Promise<AppRequest> => {
  try {
    // Apply enhanced request transformation
    const transformedData = EnhancedDataTransformer.camelToSnake(
      data,
      transformationOptions
    );

    const request = await crudService.update(id, transformedData as Partial<AppRequest>);

    // Transform response back to camelCase
    return TransformationUtils.transformApiResponse<AppRequest>(request);
  } catch (error) {
    console.error(`[RequestService] Error updating request ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a request
 *
 * @param id - Request identifier
 * @returns Promise<void>
 */
export const deleteRequest = async (id: string): Promise<void> => {
  try {
    await crudService.delete(id);
  } catch (error) {
    console.error(`[RequestService] Error deleting request ${id}:`, error);
    throw error;
  }
};

/**
 * Enhanced attachment management with centralized transformation
 *
 * Replaces the manual attachment mapping logic that was duplicated
 * across multiple services.
 */

/**
 * Upload attachments for a request
 *
 * @param requestId - Request identifier
 * @param attachments - Array of attachment data
 * @returns Promise<RequestAttachment[]> - Uploaded attachments
 */
export const uploadAttachments = async (
  requestId: string,
  attachments: AttachmentFile[]
): Promise<AttachmentFile[]> => {
  try {
    // Use centralized attachment transformation (addresses requestService.ts:62 duplication)
    const transformedAttachments = TransformationUtils.transformAttachments(attachments);

    const response = await crudService.customRequest(
      `${requestId}/attachments`,
      'POST',
      { attachments: transformedAttachments }
    );

    // Transform response attachments back to camelCase
    return EnhancedDataTransformer.transformBatch(
      response.attachments || [],
      (attachment) => EnhancedDataTransformer.snakeToCamel<any, AttachmentFile>(
        attachment,
        TransformationPresets.API_RESPONSE
      ),
      TransformationPresets.API_RESPONSE
    ) as AttachmentFile[];
  } catch (error) {
    console.error(`[RequestService] Error uploading attachments for request ${requestId}:`, error);
    throw error;
  }
};

/**
 * Get attachments for a request
 *
 * @param requestId - Request identifier
 * @returns Promise<RequestAttachment[]> - Request attachments
 */
export const getRequestAttachments = async (requestId: string): Promise<AttachmentFile[]> => {
  try {
    const response = await crudService.customRequest(`${requestId}/attachments`, 'GET');

    // Transform response attachments
    return EnhancedDataTransformer.transformBatch(
      response.attachments || [],
      (attachment) => EnhancedDataTransformer.snakeToCamel<any, AttachmentFile>(
        attachment,
        TransformationPresets.API_RESPONSE
      ),
      TransformationPresets.API_RESPONSE
    ) as AttachmentFile[];
  } catch (error) {
    console.error(`[RequestService] Error getting attachments for request ${requestId}:`, error);
    throw error;
  }
};

/**
 * Advanced request operations with performance optimizations
 */

/**
 * Bulk create requests with optimized batch processing
 *
 * @param requests - Array of request data
 * @returns Promise<AppRequest[]> - Created requests
 */
export const createRequestsBatch = async (requests: RequestFormData[]): Promise<AppRequest[]> => {
  try {
    // Use performance-optimized batch transformation
    const transformedRequests = EnhancedDataTransformer.transformBatch(
      requests,
      (request) => EnhancedDataTransformer.camelToSnake(request, transformationOptions),
      transformationOptions
    );

    const response = await crudService.customRequest('batch', 'POST', {
      requests: transformedRequests
    });

    // Transform response back to camelCase
    return EnhancedDataTransformer.transformBatch(
      response.requests || [],
      (request) => TransformationUtils.transformApiResponse<AppRequest>(request),
      TransformationPresets.API_RESPONSE
    ) as AppRequest[];
  } catch (error) {
    console.error('[RequestService] Error creating requests batch:', error);
    throw error;
  }
};

/**
 * Search requests with enhanced filtering
 *
 * @param query - Search query
 * @param filters - Additional filters
 * @returns Promise<AppRequest[]> - Matching requests
 */
export const searchRequests = async (
  query: string,
  filters?: Record<string, any>
): Promise<AppRequest[]> => {
  try {
    const params = {
      q: query,
      ...filters
    };

    const response = await crudService.customRequest('search', 'GET', undefined, params);

    // Transform search results
    return EnhancedDataTransformer.transformBatch(
      response.requests || [],
      (request) => TransformationUtils.transformApiResponse<AppRequest>(request),
      TransformationPresets.API_RESPONSE
    ) as AppRequest[];
  } catch (error) {
    console.error('[RequestService] Error searching requests:', error);
    throw error;
  }
};

/**
 * Get request statistics with transformation
 *
 * @param filters - Optional filters for statistics
 * @returns Promise<any> - Request statistics
 */
export const getRequestStatistics = async (filters?: Record<string, any>): Promise<any> => {
  try {
    const response = await crudService.customRequest('statistics', 'GET', undefined, filters);

    // Transform statistics response
    return TransformationUtils.transformApiResponse(response);
  } catch (error) {
    console.error('[RequestService] Error getting request statistics:', error);
    throw error;
  }
};

/**
 * Export request data with custom formatting
 *
 * @param format - Export format ('csv', 'excel', 'pdf')
 * @param filters - Optional filters for export
 * @returns Promise<Blob> - Export file blob
 */
export const exportRequests = async (
  format: 'csv' | 'excel' | 'pdf' = 'csv',
  filters?: Record<string, any>
): Promise<Blob> => {
  try {
    const params = {
      format,
      ...filters
    };

    const response = await crudService.customRequest('export', 'GET', undefined, params);

    // Return blob for file download
    return new Blob([response], {
      type: format === 'csv' ? 'text/csv' : 'application/octet-stream'
    });
  } catch (error) {
    console.error('[RequestService] Error exporting requests:', error);
    throw error;
  }
};

/**
 * Utility functions for request management
 */
export const RequestServiceUtils = {
  /**
   * Validate request data before submission
   */
  validateRequestData: (data: RequestFormData): boolean => {
    // Add validation logic here
    return true;
  },

  /**
   * Transform request for specific API endpoints
   */
  transformForEndpoint: (data: any, endpoint: string): any => {
    // Endpoint-specific transformations
    switch (endpoint) {
      case 'create':
        return EnhancedDataTransformer.camelToSnake(data, transformationOptions);
      case 'update':
        return EnhancedDataTransformer.camelToSnake(data, {
          ...transformationOptions,
          fieldMapping: {
            ...transformationOptions.fieldMapping,
            excludeFields: ['id', 'createdAt'] // Don't transform these on update
          }
        });
      default:
        return data;
    }
  },

  /**
   * Get transformation metadata for debugging
   */
  getTransformationMetadata: () => transformationOptions
};

// Export the service configuration for advanced usage
export { serviceConfig as RequestServiceConfig };

/**
 * Legacy compatibility exports
 *
 * These maintain backward compatibility with existing code
 * while providing the enhanced functionality under the hood.
 */
export {
  getRequests as getAllRequests,
  getRequestById as getRequest,
  createRequest as addRequest,
  updateRequest as modifyRequest,
  deleteRequest as removeRequest
};