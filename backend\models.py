from sqlalchemy import Column, <PERSON>te<PERSON>, String, <PERSON>olean, DateTime, ForeignKey, Float
from sqlalchemy.orm import relationship
from .database import Base

# ============================================================================
# SIMPLIFIED DATABASE MODELS - MIGRATION VERSION
# ============================================================================

class Processing(Base):
    __tablename__ = "processing"

    id = Column(String, primary_key=True, index=True)
    tire_type = Column(String, nullable=False)
    description1 = Column(String, nullable=False)
    description2 = Column(String, nullable=False)
    test_code1 = Column(String, nullable=False)
    test_code2 = Column(String, nullable=False)
    cost = Column(String, nullable=False)
    picture = Column(Boolean, default=False)

class Tire(Base):
    """Enhanced Tire model - Master catalog with additional fields"""
    __tablename__ = "tires"

    id = Column(String, primary_key=True, index=True)
    tug_no = Column(String, unique=True, nullable=False, index=True)  # Made unique
    spec_no = Column(String, nullable=False)
    size = Column(String, nullable=False, index=True)
    owner = Column(String, nullable=False, index=True)
    load_index = Column(String, nullable=False)
    pattern = Column(String, nullable=False, index=True)
    project_no = Column(String, nullable=False, index=True)
    location = Column(String, nullable=False)
    description = Column(String, nullable=True)  # New field
    is_active = Column(Boolean, default=True)    # New field

    # Relationships
    request_items = relationship("RequestItem", back_populates="tire")

class RequestDetail(Base):
    __tablename__ = "request_details"

    id = Column(String, primary_key=True, index=True)
    request_id = Column(String, ForeignKey("requests.id"))
    tug_number = Column(String, nullable=False)
    section = Column(String, nullable=False)
    project_number = Column(String, nullable=False)
    spec_number = Column(String, nullable=False)
    tire_size = Column(String, nullable=False)
    pattern = Column(String, nullable=False)
    note = Column(String, nullable=True)
    disposition = Column(String, nullable=False)
    process_number = Column(Integer, nullable=False)

    request = relationship("Request", back_populates="request_details")

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, nullable=True)
    role = Column(String, default="viewer") # Added role field
    is_active = Column(Integer, default=1)

class Request(Base):
    __tablename__ = "requests"

    id = Column(String, primary_key=True, index=True)
    request_by = Column(String, nullable=False)
    project_no = Column(String, nullable=False)
    pid_project = Column(String, nullable=True)
    tire_size = Column(String, nullable=False)
    request_date = Column(DateTime, nullable=False)
    target_date = Column(DateTime, nullable=False)
    status = Column(String, nullable=False)
    type = Column(String, nullable=False)
    total_n = Column(Integer, nullable=True)
    destination = Column(String, nullable=True)
    internal = Column(Boolean, nullable=True)
    wish_date = Column(DateTime, nullable=True)
    num_pneus_set = Column(Integer, nullable=True)
    num_pneus_set_unit = Column(String, nullable=True)
    in_charge_of = Column(String, nullable=True)
    note = Column(String, nullable=True)

    # Legacy relationships (for backward compatibility during migration)
    attachments = relationship("Attachment", back_populates="request", cascade="all, delete-orphan")
    request_details = relationship("RequestDetail", back_populates="request", cascade="all, delete-orphan")

    # New simplified relationships
    request_items = relationship("RequestItem", back_populates="request", cascade="all, delete-orphan")

class Attachment(Base):
    __tablename__ = "attachments"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    size = Column(Integer, nullable=False)
    type = Column(String, nullable=False)
    upload_date = Column(DateTime, nullable=False)
    status = Column(String, nullable=False)
    request_id = Column(String, ForeignKey("requests.id"))
    request = relationship("Request", back_populates="attachments")

class RequestDetailCut(Base):
    __tablename__ = "REQUEST_DETAIL_CUT"

    id = Column(Integer, primary_key=True, index=True)
    id_request_details = Column(String, ForeignKey("request_details.id"), nullable=True)
    notes = Column(String, nullable=True)
    cut_price = Column(Float, nullable=True)
    status = Column(String, nullable=True)

    request_detail = relationship("RequestDetail", backref="cuts")
    cut_processing = relationship("CutProcessing", back_populates="cut", cascade="all, delete-orphan")

class CutProcessing(Base):
    __tablename__ = "cut_processing"

    id = Column(Integer, primary_key=True, index=True)
    cut_id = Column(Integer, ForeignKey("REQUEST_DETAIL_CUT.id"), nullable=False)
    processing_id = Column(String, ForeignKey("processing.id"), nullable=False)
    quantity = Column(Integer, default=1)
    notes = Column(String, nullable=True)
    created_date = Column(DateTime, nullable=True)

    cut = relationship("RequestDetailCut", back_populates="cut_processing")
    processing = relationship("Processing", backref="cut_processing")


# ============================================================================
# NEW SIMPLIFIED MODELS - MIGRATION TARGET
# ============================================================================

class RequestItem(Base):
    """Simplified replacement for RequestDetail - references Tire catalog"""
    __tablename__ = "request_items"

    id = Column(String, primary_key=True, index=True)
    request_id = Column(String, ForeignKey("requests.id"), nullable=False)
    tire_id = Column(String, ForeignKey("tires.id"), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    disposition = Column(String, nullable=False)
    notes = Column(String, nullable=True)
    unit_price = Column(Float, nullable=True)
    section = Column(String, nullable=True)  # For compatibility

    # Relationships
    request = relationship("Request", back_populates="request_items")
    tire = relationship("Tire", back_populates="request_items")
    cut_operations = relationship("CutOperation", back_populates="request_item", cascade="all, delete-orphan")


class CutOperation(Base):
    """Simplified replacement for RequestDetailCut + CutProcessing"""
    __tablename__ = "cut_operations"

    id = Column(Integer, primary_key=True, index=True)
    request_item_id = Column(String, ForeignKey("request_items.id"), nullable=False)
    processing_id = Column(String, ForeignKey("processing.id"), nullable=False)
    quantity = Column(Integer, default=1)
    cut_price = Column(Float, nullable=True)
    status = Column(String, nullable=True)
    notes = Column(String, nullable=True)
    created_date = Column(DateTime, nullable=True)

    # Relationships
    request_item = relationship("RequestItem", back_populates="cut_operations")
    processing = relationship("Processing", backref="cut_operations")
