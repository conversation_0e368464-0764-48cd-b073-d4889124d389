/**
 * MCP Playwright Server Demonstration Script
 *
 * This script demonstrates the capabilities of the MCP Playwright server
 * with the CutRequestStudio application.
 *
 * Run this after VS Code restart and MCP server connection.
 */

// Demo scenarios for CutRequestStudio
const demoScenarios = {
  // Scenario 1: Basic Navigation and Screenshot
  basicDemo: async () => {
    console.log("🚀 Demo 1: Basic Navigation and Screenshot");

    // Navigate to CutRequestStudio
    await mcpTool("navigate_to", {
      url: "http://localhost:9002"
    });

    // Wait for page to load
    await mcpTool("wait_for_element", {
      selector: "[data-testid='main-content']",
      timeout: 5000
    });

    // Take a screenshot
    await mcpTool("take_screenshot", {
      filename: "cutrequestudio-homepage.png",
      fullPage: true
    });

    console.log("✅ Homepage screenshot captured");
  },

  // Scenario 2: Tire Management Workflow
  tireManagementDemo: async () => {
    console.log("🔧 Demo 2: Tire Management Workflow");

    // Navigate to dashboard
    await mcpTool("navigate_to", {
      url: "http://localhost:9002/dashboard"
    });

    // Take dashboard screenshot
    await mcpTool("take_screenshot", {
      filename: "dashboard-overview.png"
    });

    // Click tire management button
    await mcpTool("click_element", {
      selector: "[data-testid='tire-management-button']"
    });

    // Wait for dialog to open
    await mcpTool("wait_for_element", {
      selector: "[data-testid='tire-management-dialog']"
    });

    // Take dialog screenshot
    await mcpTool("take_element_screenshot", {
      selector: "[data-testid='tire-management-dialog']",
      filename: "tire-management-dialog.png"
    });

    console.log("✅ Tire management workflow captured");
  },

  // Scenario 3: Data Extraction
  dataExtractionDemo: async () => {
    console.log("📊 Demo 3: Data Extraction");

    // Navigate to request detail page
    await mcpTool("navigate_to", {
      url: "http://localhost:9002/dashboard/dettaglio"
    });

    // Extract tire specifications
    const tireSpecs = await mcpTool("extract_text", {
      selector: "[data-testid='tire-specifications']"
    });

    // Extract request status
    const requestStatus = await mcpTool("extract_text", {
      selector: "[data-testid='request-status']"
    });

    // Get page title
    const pageTitle = await mcpTool("get_page_title", {});

    console.log("📋 Extracted Data:");
    console.log("  - Page Title:", pageTitle);
    console.log("  - Tire Specs:", tireSpecs);
    console.log("  - Request Status:", requestStatus);

    console.log("✅ Data extraction completed");
  },

  // Scenario 4: Form Testing
  formTestingDemo: async () => {
    console.log("📝 Demo 4: Form Testing");

    // Navigate to request form
    await mcpTool("navigate_to", {
      url: "http://localhost:9002/dashboard/richiesta"
    });

    // Test form interactions
    await mcpTool("type_text", {
      selector: "[data-testid='tire-code-input']",
      text: "TEST123456"
    });

    await mcpTool("select_option", {
      selector: "[data-testid='tire-type-select']",
      value: "summer"
    });

    // Take form screenshot
    await mcpTool("take_screenshot", {
      filename: "form-filled.png"
    });

    // Validate form data
    const isValid = await mcpTool("execute_javascript", {
      script: `
        const codeInput = document.querySelector('[data-testid="tire-code-input"]');
        const typeSelect = document.querySelector('[data-testid="tire-type-select"]');
        return {
          codeValue: codeInput?.value,
          typeValue: typeSelect?.value,
          isCodeValid: /^[A-Z0-9]{6,12}$/.test(codeInput?.value || ''),
          isFormComplete: !!(codeInput?.value && typeSelect?.value)
        };
      `
    });

    console.log("📋 Form Validation Results:", isValid);
    console.log("✅ Form testing completed");
  },

  // Scenario 5: Performance Testing
  performanceDemo: async () => {
    console.log("⚡ Demo 5: Performance Testing");

    const startTime = Date.now();

    // Navigate to dashboard
    await mcpTool("navigate_to", {
      url: "http://localhost:9002/dashboard"
    });

    // Wait for all content to load
    await mcpTool("wait_for_element", {
      selector: "[data-testid='dashboard-loaded']"
    });

    const loadTime = Date.now() - startTime;

    // Check page performance
    const performanceMetrics = await mcpTool("execute_javascript", {
      script: `
        return {
          loadTime: ${loadTime},
          domElements: document.querySelectorAll('*').length,
          images: document.querySelectorAll('img').length,
          scripts: document.querySelectorAll('script').length,
          stylesheets: document.querySelectorAll('link[rel="stylesheet"]').length
        };
      `
    });

    console.log("📊 Performance Metrics:", performanceMetrics);
    console.log("✅ Performance testing completed");
  }
};

// Helper function to simulate MCP tool calls
async function mcpTool(toolName, args) {
  console.log(`🔧 MCP Tool: ${toolName}`, args);
  // In actual usage, this would be replaced with real MCP tool calls
  return { success: true, data: args };
}

// Main demo runner
async function runAllDemos() {
  console.log("🎭 MCP Playwright Server Demo for CutRequestStudio");
  console.log("=" .repeat(60));

  try {
    await demoScenarios.basicDemo();
    await demoScenarios.tireManagementDemo();
    await demoScenarios.dataExtractionDemo();
    await demoScenarios.formTestingDemo();
    await demoScenarios.performanceDemo();

    console.log("\n🎉 All demos completed successfully!");
    console.log("📸 Screenshots saved to project directory");
    console.log("📊 Test results logged to console");

  } catch (error) {
    console.error("❌ Demo failed:", error);
  }
}

// Export for use
module.exports = {
  demoScenarios,
  runAllDemos,
  mcpTool
};

// Run demos if called directly
if (require.main === module) {
  runAllDemos();
}