"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Edit, Trash2, Eye, Scissors } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { RequestItem, RequestItemFormData, initialRequestItemFormData, EnhancedTire } from "@/types";
import {
  getRequestItemsByRequest,
  createRequestItem,
  updateRequestItem,
  deleteRequestItem,
  getCutOperationsForItem,
  validateRequestItemData,
  calculateRequestItemTotals,
} from "@/services/requestItemService";
import { getEnhancedTires } from "@/services/enhancedTireService";

interface RequestItemsManagementProps {
  requestId: string;
  onItemsChange?: (items: RequestItem[]) => void;
  readOnly?: boolean;
}

const DISPOSITION_OPTIONS = [
  { value: "AVAILABLE", label: "Available", color: "bg-green-100 text-green-800" },
  { value: "SCRAP", label: "Scrap", color: "bg-red-100 text-red-800" },
  { value: "TESTED", label: "Tested", color: "bg-blue-100 text-blue-800" },
  { value: "REPAIR", label: "Repair", color: "bg-yellow-100 text-yellow-800" },
  { value: "RESERVED", label: "Reserved", color: "bg-purple-100 text-purple-800" },
  { value: "COMPLETED", label: "Completed", color: "bg-gray-100 text-gray-800" },
];

export function RequestItemsManagement({
  requestId,
  onItemsChange,
  readOnly = false
}: RequestItemsManagementProps) {
  const [items, setItems] = useState<RequestItem[]>([]);
  const [availableTires, setAvailableTires] = useState<EnhancedTire[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<RequestItemFormData>(initialRequestItemFormData);
  const [formErrors, setFormErrors] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [selectedItem, setSelectedItem] = useState<RequestItem | null>(null);

  const { toast } = useToast();

  // Load request items and available tires on component mount
  useEffect(() => {
    loadRequestItems();
    loadAvailableTires();
  }, [requestId]);

  const loadRequestItems = async () => {
    try {
      setLoading(true);
      const data = await getRequestItemsByRequest(requestId);
      setItems(data);
      onItemsChange?.(data);
    } catch (error) {
      console.error("Error loading request items:", error);
      toast({
        title: "Error",
        description: "Failed to load request items. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableTires = async () => {
    try {
      const data = await getEnhancedTires({ is_active: true, limit: 1000 });
      setAvailableTires(data);
    } catch (error) {
      console.error("Error loading available tires:", error);
    }
  };

  const handleCreateItem = () => {
    setFormData({
      ...initialRequestItemFormData,
      requestId: requestId,
    });
    setIsEditing(false);
    setFormErrors([]);
    setIsDialogOpen(true);
  };

  const handleEditItem = (item: RequestItem) => {
    setFormData({
      id: item.id,
      requestId: item.requestId,
      tireId: item.tireId,
      quantity: item.quantity,
      disposition: item.disposition,
      notes: item.notes || "",
      unitPrice: item.unitPrice || 0,
      section: item.section || "",
    });
    setIsEditing(true);
    setFormErrors([]);
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    const errors = validateRequestItemData(formData);
    if (errors.length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setSubmitting(true);
      if (isEditing && formData.id) {
        await updateRequestItem(formData.id, formData);
        toast({
          title: "Success",
          description: "Request item updated successfully.",
        });
      } else {
        await createRequestItem(formData);
        toast({
          title: "Success",
          description: "Request item created successfully.",
        });
      }
      setIsDialogOpen(false);
      loadRequestItems();
    } catch (error) {
      console.error("Error saving request item:", error);
      toast({
        title: "Error",
        description: "Failed to save request item. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteItem = async (item: RequestItem) => {
    if (!confirm(`Are you sure you want to delete this request item?`)) {
      return;
    }

    try {
      await deleteRequestItem(item.id);
      toast({
        title: "Success",
        description: "Request item deleted successfully.",
      });
      loadRequestItems();
    } catch (error) {
      console.error("Error deleting request item:", error);
      toast({
        title: "Error",
        description: "Failed to delete request item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleManageCuts = async (item: RequestItem) => {
    try {
      const cutOperations = await getCutOperationsForItem(item.id);
      // TODO: Open cut operations management dialog
      console.log("Cut operations for item:", cutOperations);
    } catch (error) {
      console.error("Error loading cut operations:", error);
    }
  };

  const getDispositionBadge = (disposition: string) => {
    const option = DISPOSITION_OPTIONS.find(opt => opt.value === disposition);
    return (
      <Badge className={option?.color || "bg-gray-100 text-gray-800"}>
        {option?.label || disposition}
      </Badge>
    );
  };

  const totals = calculateRequestItemTotals(items);

  return (
    <div className="space-y-6">
      {/* Header with Summary */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-xl font-semibold">Request Items</h3>
          <div className="flex gap-4 mt-2 text-sm text-gray-600">
            <span>Total Items: {items.length}</span>
            <span>Total Quantity: {totals.totalQuantity}</span>
            <span>Total Value: €{totals.totalValue.toFixed(2)}</span>
          </div>
        </div>
        {!readOnly && (
          <Button onClick={handleCreateItem} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Item
          </Button>
        )}
      </div>

      {/* Items Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tire</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Disposition</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Section</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow
                    key={item.id}
                    className={`cursor-pointer hover:bg-gray-50 ${
                      selectedItem?.id === item.id ? "bg-blue-50" : ""
                    }`}
                    onClick={() => setSelectedItem(item)}
                  >
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.tire?.tugNo || item.tireId}</div>
                        <div className="text-sm text-gray-500">{item.tire?.specNo}</div>
                      </div>
                    </TableCell>
                    <TableCell>{item.tire?.size}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{getDispositionBadge(item.disposition)}</TableCell>
                    <TableCell>€{(item.unitPrice || 0).toFixed(2)}</TableCell>
                    <TableCell>€{((item.unitPrice || 0) * item.quantity).toFixed(2)}</TableCell>
                    <TableCell>{item.section}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleManageCuts(item);
                          }}
                          title="Manage Cut Operations"
                        >
                          <Scissors className="h-4 w-4" />
                        </Button>
                        {!readOnly && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditItem(item);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteItem(item);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{isEditing ? "Edit Request Item" : "Create New Request Item"}</DialogTitle>
            <DialogDescription>
              {isEditing ? "Update request item information" : "Add a new item to this request"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="tireId">Tire *</Label>
              <Select
                value={formData.tireId}
                onValueChange={(value) => setFormData({ ...formData, tireId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a tire" />
                </SelectTrigger>
                <SelectContent>
                  {availableTires.map((tire) => (
                    <SelectItem key={tire.id} value={tire.id}>
                      {tire.tugNo} - {tire.size} ({tire.owner})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={formData.quantity}
                  onChange={(e) => setFormData({ ...formData, quantity: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <Label htmlFor="unitPrice">Unit Price (€)</Label>
                <Input
                  id="unitPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.unitPrice}
                  onChange={(e) => setFormData({ ...formData, unitPrice: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="disposition">Disposition *</Label>
              <Select
                value={formData.disposition}
                onValueChange={(value) => setFormData({ ...formData, disposition: value as any })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select disposition" />
                </SelectTrigger>
                <SelectContent>
                  {DISPOSITION_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="section">Section</Label>
              <Input
                id="section"
                value={formData.section}
                onChange={(e) => setFormData({ ...formData, section: e.target.value })}
                placeholder="e.g., Section A, Shelf 1"
              />
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Additional notes"
              />
            </div>
          </div>

          {formErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <ul className="text-red-600 text-sm space-y-1">
                {formErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={submitting}>
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
