/**
 * DEVELOPER EXPERIENCE ENHANCEMENTS - PRIORITÀ 3
 *
 * Strumenti per migliorare l'esperienza di sviluppo:
 * - Auto-generation di nuovi CRUD entities utilizzando i pattern consolidati
 * - CLI tools per scaffolding di componenti
 * - Type generation automatica da backend schemas
 * - Documentation generator per API
 * - Testing utilities per componenti generici
 */

// ============================================================================
// CORE TYPES
// ============================================================================

export interface EntityConfig {
  /** Entity name (e.g., 'User', 'Product') */
  name: string;
  /** Plural form (e.g., 'users', 'products') */
  plural: string;
  /** API endpoint path */
  endpoint: string;
  /** Database table name */
  tableName?: string;
  /** Entity fields */
  fields: EntityField[];
  /** Additional options */
  options?: EntityOptions;
}

export interface EntityField {
  /** Field name */
  name: string;
  /** Field type */
  type: 'string' | 'number' | 'boolean' | 'date' | 'email' | 'url' | 'text' | 'select';
  /** Whether field is required */
  required?: boolean;
  /** Default value */
  defaultValue?: any;
  /** Validation rules */
  validation?: ValidationRule[];
  /** Select options (for select type) */
  options?: string[];
  /** Field description */
  description?: string;
}

export interface EntityOptions {
  /** Generate API routes */
  generateRoutes?: boolean;
  /** Generate frontend service */
  generateService?: boolean;
  /** Generate React components */
  generateComponents?: boolean;
  /** Generate tests */
  generateTests?: boolean;
  /** Use authentication */
  useAuth?: boolean;
  /** Custom permissions */
  permissions?: string[];
}

export interface ValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message?: string;
}

export interface ComponentTemplate {
  /** Template name */
  name: string;
  /** Template description */
  description: string;
  /** Template files */
  files: TemplateFile[];
  /** Template variables */
  variables: TemplateVariable[];
}

export interface TemplateFile {
  /** File path relative to output directory */
  path: string;
  /** File content template */
  content: string;
  /** Whether to overwrite existing file */
  overwrite?: boolean;
}

export interface TemplateVariable {
  /** Variable name */
  name: string;
  /** Variable description */
  description: string;
  /** Variable type */
  type: 'string' | 'boolean' | 'number' | 'select';
  /** Default value */
  defaultValue?: any;
  /** Select options (for select type) */
  options?: string[];
  /** Whether variable is required */
  required?: boolean;
}

// ============================================================================
// CODE GENERATORS
// ============================================================================

/**
 * CRUD Entity Generator
 * Genera automaticamente tutti i file necessari per una nuova entità CRUD
 */
class CrudEntityGenerator {

  /**
   * Generates complete CRUD entity
   */
  static generateEntity(config: EntityConfig, outputDir: string = './generated'): void {
    console.log(`Generating CRUD entity: ${config.name}`);

    // Create output directory
    if (typeof window === 'undefined') {
      // Node.js environment
      const fs = require('fs');
      const path = require('path');

      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
    }

    // Generate TypeScript types
    if (config.options?.generateService !== false) {
      this.generateTypes(config, outputDir);
      this.generateService(config, outputDir);
    }

    // Generate backend routes
    if (config.options?.generateRoutes) {
      this.generateBackendRouter(config, outputDir);
      this.generateBackendSchemas(config, outputDir);
    }

    // Generate React components
    if (config.options?.generateComponents) {
      this.generateReactComponents(config, outputDir);
    }

    // Generate tests
    if (config.options?.generateTests) {
      this.generateTests(config, outputDir);
    }

    console.log(`✅ Generated CRUD entity: ${config.name}`);
  }

  /**
   * Generates TypeScript types
   */
  private static generateTypes(config: EntityConfig, outputDir: string): void {
    const typesContent = `/**
 * ${config.name} Types
 * Auto-generated by CrudEntityGenerator
 */

export interface ${config.name} {
${config.fields.map(field => `  ${field.name}${field.required ? '' : '?'}: ${this.getTypeScriptType(field.type)};`).join('\n')}
}

export interface ${config.name}FormData {
${config.fields.map(field => `  ${field.name}${field.required ? '' : '?'}: ${this.getTypeScriptType(field.type)};`).join('\n')}
}

export interface ${config.name}CreateData extends Omit<${config.name}FormData, 'id'> {}

export interface ${config.name}UpdateData extends Partial<${config.name}FormData> {
  id: string;
}`;

    this.writeFile(outputDir, `${config.name.toLowerCase()}-types.ts`, typesContent);
  }

  /**
   * Generates frontend service
   */
  private static generateService(config: EntityConfig, outputDir: string): void {
    const serviceContent = `/**
 * ${config.name} Service
 * Auto-generated by CrudEntityGenerator
 */

import { CrudServiceFactory } from '@/lib/generic-crud-service';
import { ${config.name}, ${config.name}FormData } from './${config.name.toLowerCase()}-types';

// Create CRUD service instance
export const ${config.name.toLowerCase()}Service = CrudServiceFactory.createStandardService<
  ${config.name},
  ${config.name}FormData
>('${config.endpoint}');

// Export individual methods for convenience
export const {
  getAll: get${config.plural},
  getById: get${config.name}ById,
  create: create${config.name},
  update: update${config.name},
  delete: delete${config.name}
} = ${config.name.toLowerCase()}Service;

// Custom methods can be added here
export const search${config.plural} = async (query: string) => {
  return ${config.name.toLowerCase()}Service.customRequest('GET', '/search', { q: query });
};`;

    this.writeFile(outputDir, `${config.name.toLowerCase()}Service.ts`, serviceContent);
  }

  /**
   * Helper method to write files
   */
  private static writeFile(outputDir: string, filename: string, content: string): void {
    if (typeof window === 'undefined') {
      // Node.js environment
      const fs = require('fs');
      const path = require('path');
      const filePath = path.join(outputDir, filename);
      fs.writeFileSync(filePath, content);
      console.log(`Generated: ${filePath}`);
    } else {
      // Browser environment - just log the content
      console.log(`Generated ${filename}:`, content);
    }
  }

  /**
   * Helper methods
   */
  private static getTypeScriptType(type: string): string {
    switch (type) {
      case 'string':
      case 'email':
      case 'url':
      case 'text':
      case 'select':
        return 'string';
      case 'number':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'date':
        return 'Date';
      default:
        return 'string';
    }
  }

  /**
   * Generates backend router
   */
  private static generateBackendRouter(config: EntityConfig, outputDir: string): void {
    const routerContent = `"""
${config.name} Router
Auto-generated by CrudEntityGenerator
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from ..database import get_db
from ..router_factory import GenericRouterFactory, RouterConfig
from .. import models, schemas
${config.options?.useAuth ? 'from ..auth import require_viewer_role, require_editor_role' : ''}

# Create router configuration
${config.name.toLowerCase()}_router_config = RouterConfig(
    prefix="/${config.endpoint}",
    tags=["${config.plural}"],
    model=models.${config.name},
    create_schema=schemas.${config.name}Create,
    update_schema=schemas.${config.name}Update,
    read_schema=schemas.${config.name}Read,
    entity_name="${config.name}",
    ${config.options?.useAuth ? 'require_auth=True,' : ''}
)

# Generate CRUD router
router = GenericRouterFactory.create_crud_router(${config.name.toLowerCase()}_router_config)

# Custom endpoints can be added here
@router.get("/search", response_model=List[schemas.${config.name}Read])
def search_${config.plural.toLowerCase()}(
    q: str,
    db: Session = Depends(get_db)${config.options?.useAuth ? ',\\n    current_user = Depends(require_viewer_role)' : ''}
):
    """Search ${config.plural.toLowerCase()} by query"""
    # Implement search logic here
    return []`;

    this.writeFile(outputDir, `${config.name.toLowerCase()}_router.py`, routerContent);
  }

  /**
   * Generates backend schemas
   */
  private static generateBackendSchemas(config: EntityConfig, outputDir: string): void {
    const schemasContent = `"""
${config.name} Schemas
Auto-generated by CrudEntityGenerator
"""

from pydantic import BaseModel${config.fields.some(f => f.type === 'email') ? ', EmailStr' : ''}
from typing import Optional
from datetime import datetime

class ${config.name}Base(BaseModel):
${config.fields.map(field => `    ${field.name}: ${this.getPydanticType(field)}`).join('\n')}

class ${config.name}Create(${config.name}Base):
    pass

class ${config.name}Update(BaseModel):
${config.fields.map(field => `    ${field.name}: Optional[${this.getPydanticType(field, false)}] = None`).join('\n')}

class ${config.name}Read(${config.name}Base):
    id: str

    class Config:
        orm_mode = True`;

    this.writeFile(outputDir, `${config.name.toLowerCase()}_schemas.py`, schemasContent);
  }

  private static getPydanticType(field: EntityField, required: boolean = true): string {
    const optional = !required || !field.required ? 'Optional[' : '';
    const close = !required || !field.required ? ']' : '';

    switch (field.type) {
      case 'string':
      case 'text':
      case 'url':
      case 'select':
        return `${optional}str${close}`;
      case 'email':
        return `${optional}EmailStr${close}`;
      case 'number':
        return `${optional}int${close}`;
      case 'boolean':
        return `${optional}bool${close}`;
      case 'date':
        return `${optional}datetime${close}`;
      default:
        return `${optional}str${close}`;
    }
  }

  /**
   * Generates React components (simplified version)
   */
  private static generateReactComponents(config: EntityConfig, outputDir: string): void {
    // Generate basic form component
    const formContent = `/**
 * ${config.name} Form Component
 * Auto-generated by CrudEntityGenerator
 */

import React from 'react';
import { useUniversalForm } from '@/hooks/useUniversalForm';
import { ${config.name}FormData } from './${config.name.toLowerCase()}-types';

interface ${config.name}FormProps {
  initialData?: Partial<${config.name}FormData>;
  onSubmit: (data: ${config.name}FormData) => Promise<void>;
  onCancel?: () => void;
}

export function ${config.name}Form({ initialData = {}, onSubmit, onCancel }: ${config.name}FormProps) {
  const { formData, errors, isSubmitting, handleChange, handleSubmit } = useUniversalForm<${config.name}FormData>({
    initialData: { ...initialData } as ${config.name}FormData,
    onSubmit,
    showSuccessToast: true,
    successMessage: '${config.name} saved successfully!'
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Form fields would be generated here */}
      <div className="flex gap-2 pt-4">
        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save'}
        </button>
        {onCancel && (
          <button type="button" onClick={onCancel}>
            Cancel
          </button>
        )}
      </div>
    </form>
  );
}`;

    this.writeFile(outputDir, `${config.name}Form.tsx`, formContent);
  }

  /**
   * Generates tests (simplified version)
   */
  private static generateTests(config: EntityConfig, outputDir: string): void {
    const testContent = `/**
 * ${config.name} Tests
 * Auto-generated by CrudEntityGenerator
 */

import { describe, test, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ${config.name}Form } from './${config.name}Form';

describe('${config.name}Form', () => {
  test('renders form', () => {
    const mockSubmit = jest.fn();
    render(<${config.name}Form onSubmit={mockSubmit} />);
    expect(screen.getByText('Save')).toBeInTheDocument();
  });
});`;

    this.writeFile(outputDir, `${config.name}.test.tsx`, testContent);
  }
}

// ============================================================================
// COMPONENT SCAFFOLDING
// ============================================================================

/**
 * Component scaffolding generator
 */
class ComponentScaffolder {

  /**
   * Generates component from template
   */
  static generateComponent(
    templateName: string,
    variables: Record<string, any>,
    outputDir: string = './generated'
  ): void {
    console.log(`Generating component from template: ${templateName}`);

    // Basic React component template
    if (templateName === 'react-component') {
      const componentContent = `import React from 'react';

interface ${variables.componentName}Props {
  // Add props here
}

export function ${variables.componentName}({}: ${variables.componentName}Props) {
  return (
    <div>
      <h1>${variables.componentName}</h1>
    </div>
  );
}`;

      CrudEntityGenerator['writeFile'](outputDir, `${variables.componentName}.tsx`, componentContent);
    }

    console.log(`✅ Generated component: ${variables.componentName}`);
  }
}

// ============================================================================
// TESTING UTILITIES
// ============================================================================

/**
 * Testing utilities for generated components
 */
class TestingUtils {

  /**
   * Creates mock data for entity
   */
  static createMockData(config: EntityConfig): any {
    const mockData: any = { id: 'test-id' };

    config.fields.forEach(field => {
      switch (field.type) {
        case 'string':
        case 'email':
        case 'url':
        case 'text':
          mockData[field.name] = `test-${field.name}`;
          break;
        case 'number':
          mockData[field.name] = 123;
          break;
        case 'boolean':
          mockData[field.name] = true;
          break;
        case 'date':
          mockData[field.name] = new Date('2023-01-01');
          break;
        case 'select':
          mockData[field.name] = field.options?.[0] || 'option1';
          break;
        default:
          mockData[field.name] = `test-${field.name}`;
      }
    });

    return mockData;
  }

  /**
   * Creates test suite template
   */
  static generateTestSuite(config: EntityConfig): string {
    return `
import { describe, test, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ${config.name}Form } from './${config.name}Form';

const mockData = ${JSON.stringify(this.createMockData(config), null, 2)};

describe('${config.name}Form', () => {
  test('renders form fields', () => {
    const mockSubmit = vi.fn();
    render(<${config.name}Form onSubmit={mockSubmit} />);
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  test('submits form with valid data', async () => {
    const mockSubmit = vi.fn().mockResolvedValue(undefined);
    render(<${config.name}Form onSubmit={mockSubmit} initialData={mockData} />);

    fireEvent.click(screen.getByText('Save'));

    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalled();
    });
  });
});`;
  }
}

// Export classes
export { CrudEntityGenerator, ComponentScaffolder, TestingUtils };