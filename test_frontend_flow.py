#!/usr/bin/env python3
"""
Test script to simulate the complete frontend tire management flow
"""

import requests
import json

def test_complete_tire_management_flow():
    """Test the complete flow that the frontend should follow"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Complete Tire Management Flow")
    print("=" * 50)
    
    # Step 1: Login
    print("1️⃣ Testing user authentication...")
    login_data = {
        "username": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/v1/users/login", data=login_data)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
            return False
            
        token_data = login_response.json()
        token = token_data.get("access_token")
        print(f"✅ Login successful")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Step 2: Test enhanced tires endpoint (what frontend should call)
        print("\n2️⃣ Testing enhanced tires endpoint...")
        tires_response = requests.get(f"{base_url}/api/v1/tires", headers=headers)
        
        if tires_response.status_code != 200:
            print(f"❌ Enhanced tires API failed: {tires_response.status_code} - {tires_response.text}")
            return False
            
        tires_data = tires_response.json()
        print(f"✅ Enhanced tires API working: {len(tires_data)} tires found")
        
        # Step 3: Test filtering (what frontend dialog uses)
        print("\n3️⃣ Testing tire filtering...")
        filter_params = {
            "owner": "Bridgestone",
            "limit": 5,
            "is_active": True
        }
        
        filtered_response = requests.get(f"{base_url}/api/v1/tires", headers=headers, params=filter_params)
        if filtered_response.status_code != 200:
            print(f"❌ Filtered tires API failed: {filtered_response.status_code}")
            return False
            
        filtered_data = filtered_response.json()
        print(f"✅ Filtering working: {len(filtered_data)} Bridgestone tires found")
        
        # Step 4: Test pagination (what frontend uses for large datasets)
        print("\n4️⃣ Testing pagination...")
        paginated_params = {
            "skip": 0,
            "limit": 10
        }
        
        paginated_response = requests.get(f"{base_url}/api/v1/tires", headers=headers, params=paginated_params)
        if paginated_response.status_code != 200:
            print(f"❌ Paginated tires API failed: {paginated_response.status_code}")
            return False
            
        paginated_data = paginated_response.json()
        print(f"✅ Pagination working: {len(paginated_data)} tires in first page")
        
        # Step 5: Display sample data (what frontend dialog shows)
        print("\n5️⃣ Sample tire data (what frontend should display):")
        print("-" * 50)
        for i, tire in enumerate(tires_data[:5]):
            print(f"  {i+1}. ID: {tire.get('id')}")
            print(f"     TUG: {tire.get('tug_no')}")
            print(f"     Owner: {tire.get('owner')}")
            print(f"     Pattern: {tire.get('pattern')}")
            print(f"     Size: {tire.get('size')}")
            print(f"     Location: {tire.get('location')}")
            print(f"     Active: {tire.get('is_active', True)}")
            print()
        
        # Step 6: Test unique values for dropdowns
        print("6️⃣ Testing unique values for filter dropdowns...")
        unique_owners = list(set(tire.get('owner') for tire in tires_data))
        unique_patterns = list(set(tire.get('pattern') for tire in tires_data))
        unique_sizes = list(set(tire.get('size') for tire in tires_data))
        
        print(f"✅ Unique owners: {len(unique_owners)} ({', '.join(unique_owners[:3])}...)")
        print(f"✅ Unique patterns: {len(unique_patterns)} ({', '.join(unique_patterns[:3])}...)")
        print(f"✅ Unique sizes: {len(unique_sizes)} ({', '.join(unique_sizes[:3])}...)")
        
        print("\n" + "=" * 50)
        print("✅ All tests passed! The tire management system is ready for frontend use.")
        print("\nFrontend should be able to:")
        print("- <NAME_EMAIL> / password123")
        print("- Call /api/v1/tires to get tire catalog")
        print("- Filter by owner, pattern, size, project_no")
        print("- Paginate with skip/limit parameters")
        print("- Display all tire fields including enhanced ones")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 8000?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_complete_tire_management_flow()
    if not success:
        print("\n❌ Test failed. Check the backend server and database.")
        exit(1)
    else:
        print("\n🎉 Ready for frontend testing!")
