
"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckCircle2, XCircle } from "lucide-react";
import type { TireProcessingFormData } from "@/types";
import { cn } from "@/lib/utils";

interface FormFieldProps {
  id: string;
  label: string;
  children: React.ReactNode;
  className?: string;
}

const FormField: React.FC<FormFieldProps> = ({ id, label, children, className }) => (
  <div className={cn("space-y-1.5", className)}>
    <Label htmlFor={id}>{label}</Label>
    {children}
  </div>
);

interface ProcessingEditFormProps {
  formData: TireProcessingFormData;
  onFormChange: (field: keyof TireProcessingFormData, value: any) => void;
  onSave: () => void;
  onCancel: () => void;
}

export function ProcessingEditForm({ formData, onFormChange, onSave, onCancel }: ProcessingEditFormProps) {
  // Utility function to safely handle null/undefined values for inputs
  const safeStringValue = (value: string | null | undefined): string => {
    return value ?? "";
  };

  const handleInputChange = (field: keyof TireProcessingFormData, value: string | number | boolean | undefined) => {
    onFormChange(field, value);
  };

  const handleNumericInputChange = (field: 'n', value: string) => {
    const numValue = value === '' ? undefined : parseInt(value, 10);
    if (value === '' || (numValue !== undefined && !isNaN(numValue) && numValue >= 0)) {
       onFormChange(field, numValue);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">PROCESSING: (Editing {formData.description1 || 'Item'})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-12 gap-x-6 gap-y-4">
          <div className="md:col-span-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-4">
            <FormField id="procEdit_desc1" label="Description 1:">
              <div className="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                {safeStringValue(formData.description1)}
              </div>
            </FormField>
            <FormField id="procEdit_desc2" label="Description 2:">
              <div className="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                {safeStringValue(formData.description2)}
              </div>
            </FormField>
            <FormField id="procEdit_tyreType" label="Tire Type:">
              <div className="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                {safeStringValue(formData.tyreType)}
              </div>
            </FormField>
            <FormField id="procEdit_n" label="N°:">
              <Input
                id="procEdit_n_input"
                type="number"
                value={formData.n === undefined ? '' : formData.n}
                onChange={(e) => handleNumericInputChange('n', e.target.value)}
                min="0"
              />
            </FormField>
            <div className="flex items-center space-x-2 pt-7 sm:col-span-1">
              <Checkbox
                id="procEdit_picture"
                checked={formData.picture || false}
                onCheckedChange={(checked) => handleInputChange('picture', checked as boolean)}
              />
              <Label htmlFor="procEdit_picture" className="font-normal">Picture</Label>
            </div>
          </div>
          <div className="md:col-span-2 flex flex-col md:flex-row md:items-start justify-end md:justify-start pt-0 md:pt-7 gap-2">
            <Button variant="outline" size="icon" onClick={onSave} aria-label="Save processing details" className="text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700 w-full md:w-10">
              <CheckCircle2 className="h-5 w-5" />
            </Button>
            <Button variant="outline" size="icon" onClick={onCancel} aria-label="Cancel processing edits" className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700 w-full md:w-10">
              <XCircle className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
