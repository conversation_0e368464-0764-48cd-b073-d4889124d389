#!/usr/bin/env python3
"""
Test login functionality directly to debug authentication issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from backend.main import app
import requests

def test_login_direct():
    """Test login functionality directly"""
    print("🔐 Testing Login Functionality")
    print("=" * 50)
    
    client = TestClient(app)
    
    # Test health endpoint first
    print("\n🏥 Testing Health Endpoint")
    response = client.get("/health")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   ✅ Health check: {response.json()}")
    else:
        print(f"   ❌ Health check failed")
        return
    
    # Test login endpoint with correct credentials
    print("\n🔐 Testing Login with Correct Credentials")
    
    # Test credentials from our database
    test_credentials = [
        ("<EMAIL>", "password123", "admin"),
        ("<EMAIL>", "password123", "editor"),
        ("<EMAIL>", "password123", "viewer"),
    ]
    
    for email, password, expected_role in test_credentials:
        print(f"\n   Testing: {email} ({expected_role})")
        
        # Create form data exactly as the frontend does
        form_data = {
            "username": email,  # OAuth2PasswordRequestForm uses 'username'
            "password": password
        }
        
        response = client.post("/api/v1/users/login", data=form_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            print(f"   ✅ Login successful!")
            print(f"   🎫 Token type: {token_data.get('token_type')}")
            print(f"   🎫 Token: {token_data.get('access_token', '')[:50]}...")
            
            # Test the token by fetching user profile
            token = token_data.get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            
            profile_response = client.get("/api/v1/users/me", headers=headers)
            print(f"   Profile Status: {profile_response.status_code}")
            
            if profile_response.status_code == 200:
                user_data = profile_response.json()
                print(f"   ✅ Profile: {user_data.get('email')} - Role: {user_data.get('role')}")
            else:
                print(f"   ❌ Profile fetch failed: {profile_response.text}")
                
        else:
            print(f"   ❌ Login failed: {response.text}")
    
    # Test with wrong credentials
    print(f"\n❌ Testing with Wrong Credentials")
    wrong_form_data = {
        "username": "<EMAIL>",
        "password": "wrongpassword"
    }
    
    response = client.post("/api/v1/users/login", data=wrong_form_data)
    print(f"   Status: {response.status_code}")
    if response.status_code == 401:
        print(f"   ✅ Correctly rejected wrong credentials")
    else:
        print(f"   ⚠️ Unexpected response: {response.text}")
    
    # Test the exact frontend request format
    print(f"\n🌐 Testing Frontend Request Format")
    
    # Simulate exactly what the frontend sends
    import requests
    
    try:
        # Test if server is running on port 8000
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"   Server health: {health_response.status_code}")
        
        if health_response.status_code == 200:
            # Test login with requests (like frontend)
            login_data = {
                "username": "<EMAIL>",
                "password": "password123"
            }
            
            login_response = requests.post(
                "http://localhost:8000/api/v1/users/login",
                data=login_data,
                timeout=10
            )
            
            print(f"   Login via requests: {login_response.status_code}")
            if login_response.status_code == 200:
                print(f"   ✅ External login successful!")
                token_data = login_response.json()
                print(f"   🎫 Token: {token_data.get('access_token', '')[:50]}...")
            else:
                print(f"   ❌ External login failed: {login_response.text}")
        
    except requests.exceptions.ConnectionError:
        print(f"   ⚠️ Server not running on localhost:8000")
        print(f"   💡 Start server with: python -m uvicorn main:app --reload --port 8000")
    except Exception as e:
        print(f"   ❌ Error testing external connection: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Login testing completed!")
    
    print("\n🎯 Summary:")
    print("   - Use credentials: <EMAIL> / password123")
    print("   - Make sure backend server is running on port 8000")
    print("   - Frontend should send FormData with 'username' and 'password' fields")
    print("   - Check browser network tab for actual request details")

if __name__ == "__main__":
    test_login_direct()
