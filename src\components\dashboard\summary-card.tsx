"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import type { DashboardRequestSummary } from '@/types/dashboard';
import { useRouter } from "next/navigation";

interface SummaryCardProps {
  request: DashboardRequestSummary;
}

export function SummaryCard({ request }: SummaryCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    // Navigate to the Richiesta page, passing the request.id as a query parameter
    router.push(`/dashboard/richiesta?requestId=${request.id}`);
  };

  // Get status color based on status
  const getStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'completato':
      case 'completo':
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100';
      case 'in corso':
      case 'in progress':
      case 'processing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100';
      case 'in attesa':
      case 'pending':
      case 'waiting':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100';
    }
  };

  return (
    <Card
      onClick={handleCardClick}
      className="cursor-pointer hover:shadow-lg transition-all flex flex-col h-full overflow-hidden hover:translate-y-[-2px]"
    >
      <div className="h-2 bg-primary w-full"></div>
      <CardHeader className="pb-2">
        <CardTitle>{request.title}</CardTitle>
        <CardDescription className="flex items-center justify-between">
          <span>ID: {request.id}</span>
          <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(request.status)}`}>
            {request.status}
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2 flex-grow">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <p className="text-xs text-muted-foreground">Tipo</p>
            <p className="text-sm font-medium">{request.type}</p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Data Richiesta</p>
            <p className="text-sm font-medium">{request.requestDate}</p>
          </div>
          <div className="col-span-2">
            <p className="text-xs text-muted-foreground">Target Date</p>
            <p className="text-sm font-medium">{request.targetDate}</p>
          </div>
        </div>
        {request.description && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs text-muted-foreground">Descrizione</p>
            <p className="text-sm">{request.description}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
