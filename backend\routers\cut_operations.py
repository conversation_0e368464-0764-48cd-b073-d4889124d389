"""
Cut Operations Router - Simplified Database Structure

This router handles cut operations (simplified replacement for RequestDetailCut + CutProcessing).
Cut operations combine cutting specifications with processing operations in a single entity.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from .. import crud, models, schemas
from ..database import get_db
from ..auth import get_current_user

router = APIRouter(
    prefix="/api/cut-operations",
    tags=["cut-operations"],
    responses={404: {"description": "Not found"}},
)

@router.get("/request/{request_id}", response_model=List[schemas.CutOperationRead])
def get_cut_operations_by_request(
    request_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all cut operations for a specific request (across all items).
    
    Returns operations with processing details and tire information.
    """
    # Verify request exists
    request = crud.get_request(db=db, request_id=request_id)
    if request is None:
        raise HTTPException(status_code=404, detail="Request not found")
    
    operations = crud.get_cut_operations_by_request(db=db, request_id=request_id)
    return operations

@router.get("/item/{item_id}", response_model=List[schemas.CutOperationRead])
def get_cut_operations_by_item(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all cut operations for a specific request item.
    """
    # Verify item exists
    item = crud.get_request_item(db=db, item_id=item_id)
    if item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    
    operations = crud.get_cut_operations_by_request_item(db=db, request_item_id=item_id)
    return operations

@router.get("/{operation_id}", response_model=schemas.CutOperationRead)
def get_cut_operation(
    operation_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a specific cut operation by ID.
    """
    operation = crud.get_cut_operation(db=db, operation_id=operation_id)
    if operation is None:
        raise HTTPException(status_code=404, detail="Cut operation not found")
    return operation

@router.post("/", response_model=schemas.CutOperationRead)
def create_cut_operation(
    operation: schemas.CutOperationCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new cut operation.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    # Verify request item exists
    item = crud.get_request_item(db=db, item_id=operation.request_item_id)
    if item is None:
        raise HTTPException(status_code=404, detail="Request item not found")
    
    # Verify processing exists
    processing = crud.get_processing(db=db, processing_id=operation.processing_id)
    if processing is None:
        raise HTTPException(status_code=404, detail="Processing not found")
    
    return crud.create_cut_operation(db=db, operation=operation)

@router.put("/{operation_id}", response_model=schemas.CutOperationRead)
def update_cut_operation(
    operation_id: int,
    operation: schemas.CutOperationUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Update a cut operation.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    updated_operation = crud.update_cut_operation(db=db, operation_id=operation_id, operation=operation)
    if updated_operation is None:
        raise HTTPException(status_code=404, detail="Cut operation not found")
    return updated_operation

@router.delete("/{operation_id}", response_model=schemas.CutOperationRead)
def delete_cut_operation(
    operation_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Delete a cut operation.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    deleted_operation = crud.delete_cut_operation(db=db, operation_id=operation_id)
    if deleted_operation is None:
        raise HTTPException(status_code=404, detail="Cut operation not found")
    return deleted_operation

@router.get("/", response_model=List[schemas.CutOperationRead])
def get_cut_operations(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = Query(None, description="Filter by status"),
    processing_id: Optional[str] = Query(None, description="Filter by processing ID"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all cut operations with optional filtering.
    
    - **skip**: Number of records to skip (pagination)
    - **limit**: Maximum number of records to return
    - **status**: Filter by operation status
    - **processing_id**: Filter by processing ID
    """
    from sqlalchemy.orm import joinedload
    
    query = db.query(models.CutOperation).options(
        joinedload(models.CutOperation.processing),
        joinedload(models.CutOperation.request_item).joinedload(models.RequestItem.tire)
    )
    
    if status:
        query = query.filter(models.CutOperation.status == status)
    if processing_id:
        query = query.filter(models.CutOperation.processing_id == processing_id)
    
    operations = query.offset(skip).limit(limit).all()
    return operations

@router.get("/stats/summary")
def get_cut_operations_stats(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get cut operations statistics.
    """
    from sqlalchemy import func
    
    # Total operations
    total_operations = db.query(models.CutOperation).count()
    
    # Operations by status
    status_stats = db.query(
        models.CutOperation.status,
        func.count(models.CutOperation.id).label('count')
    ).group_by(models.CutOperation.status).all()
    
    # Operations by processing type
    processing_stats = db.query(
        models.Processing.tire_type,
        func.count(models.CutOperation.id).label('count')
    ).join(models.CutOperation).group_by(models.Processing.tire_type).all()
    
    # Total cost
    total_cost = db.query(func.sum(models.CutOperation.cut_price)).scalar() or 0
    
    # Average cost
    avg_cost = db.query(func.avg(models.CutOperation.cut_price)).scalar() or 0
    
    # Operations per month (last 12 months)
    from datetime import datetime, timedelta
    twelve_months_ago = datetime.utcnow() - timedelta(days=365)
    
    monthly_stats = db.query(
        func.strftime('%Y-%m', models.CutOperation.created_date).label('month'),
        func.count(models.CutOperation.id).label('count')
    ).filter(
        models.CutOperation.created_date >= twelve_months_ago
    ).group_by(
        func.strftime('%Y-%m', models.CutOperation.created_date)
    ).order_by('month').all()
    
    return {
        "total_operations": total_operations,
        "status_breakdown": [{"status": status or "UNKNOWN", "count": count} for status, count in status_stats],
        "processing_breakdown": [{"tire_type": tire_type, "count": count} for tire_type, count in processing_stats],
        "financial": {
            "total_cost": float(total_cost),
            "average_cost": float(avg_cost)
        },
        "monthly_trend": [{"month": month, "count": count} for month, count in monthly_stats]
    }

@router.post("/batch", response_model=List[schemas.CutOperationRead])
def create_batch_cut_operations(
    operations: List[schemas.CutOperationCreate],
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create multiple cut operations in a batch.
    
    Requires admin or editor role.
    Useful for creating multiple operations for the same request item.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    if len(operations) > 50:
        raise HTTPException(status_code=400, detail="Batch size cannot exceed 50 operations")
    
    created_operations = []
    
    try:
        for operation in operations:
            # Verify request item exists
            item = crud.get_request_item(db=db, item_id=operation.request_item_id)
            if item is None:
                raise HTTPException(status_code=404, detail=f"Request item {operation.request_item_id} not found")
            
            # Verify processing exists
            processing = crud.get_processing(db=db, processing_id=operation.processing_id)
            if processing is None:
                raise HTTPException(status_code=404, detail=f"Processing {operation.processing_id} not found")
            
            created_operation = crud.create_cut_operation(db=db, operation=operation)
            created_operations.append(created_operation)
        
        return created_operations
        
    except Exception as e:
        # Rollback any created operations
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Batch creation failed: {str(e)}")

@router.put("/batch/status", response_model=List[schemas.CutOperationRead])
def update_batch_status(
    operation_ids: List[int],
    new_status: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Update status for multiple cut operations.
    
    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    if len(operation_ids) > 100:
        raise HTTPException(status_code=400, detail="Batch size cannot exceed 100 operations")
    
    updated_operations = []
    
    for operation_id in operation_ids:
        operation_update = schemas.CutOperationUpdate(status=new_status)
        updated_operation = crud.update_cut_operation(db=db, operation_id=operation_id, operation=operation_update)
        if updated_operation:
            updated_operations.append(updated_operation)
    
    return updated_operations
