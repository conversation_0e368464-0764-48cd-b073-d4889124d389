"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Edit, Trash2, BarChart3 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CutOperation, CutOperationFormData, initialCutOperationFormData, DialogProcessing } from "@/types";
import {
  getCutOperationsByRequest,
  getCutOperationsByItem,
  createCutOperation,
  updateCutOperation,
  deleteCutOperation,
  validateCutOperationData,
  calculateCutOperationTotals,
  generateCutOperationSummary,
} from "@/services/cutOperationService";
import { getProcessing } from "@/services/processingService";

interface CutOperationsManagementProps {
  requestId?: string;
  requestItemId?: string;
  onOperationsChange?: (operations: CutOperation[]) => void;
  readOnly?: boolean;
}

const STATUS_OPTIONS = [
  { value: "PENDING", label: "Pending", color: "bg-yellow-100 text-yellow-800" },
  { value: "IN_PROGRESS", label: "In Progress", color: "bg-blue-100 text-blue-800" },
  { value: "COMPLETED", label: "Completed", color: "bg-green-100 text-green-800" },
  { value: "CANCELLED", label: "Cancelled", color: "bg-red-100 text-red-800" },
];

export function CutOperationsManagement({
  requestId,
  requestItemId,
  onOperationsChange,
  readOnly = false
}: CutOperationsManagementProps) {
  const [operations, setOperations] = useState<CutOperation[]>([]);
  const [availableProcessing, setAvailableProcessing] = useState<DialogProcessing[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<CutOperationFormData>(initialCutOperationFormData);
  const [formErrors, setFormErrors] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState<CutOperation | null>(null);
  const [showSummary, setShowSummary] = useState(false);

  const { toast } = useToast();

  // Load cut operations and available processing on component mount
  useEffect(() => {
    loadCutOperations();
    loadAvailableProcessing();
  }, [requestId, requestItemId]);

  const loadCutOperations = async () => {
    try {
      setLoading(true);
      let data: CutOperation[] = [];
      
      if (requestItemId) {
        data = await getCutOperationsByItem(requestItemId);
      } else if (requestId) {
        data = await getCutOperationsByRequest(requestId);
      }
      
      setOperations(data);
      onOperationsChange?.(data);
    } catch (error) {
      console.error("Error loading cut operations:", error);
      toast({
        title: "Error",
        description: "Failed to load cut operations. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableProcessing = async () => {
    try {
      const data = await getProcessing();
      setAvailableProcessing(data);
    } catch (error) {
      console.error("Error loading available processing:", error);
    }
  };

  const handleCreateOperation = () => {
    setFormData({
      ...initialCutOperationFormData,
      requestItemId: requestItemId || "",
    });
    setIsEditing(false);
    setFormErrors([]);
    setIsDialogOpen(true);
  };

  const handleEditOperation = (operation: CutOperation) => {
    setFormData({
      requestItemId: operation.requestItemId,
      processingId: operation.processingId,
      quantity: operation.quantity,
      cutPrice: operation.cutPrice || 0,
      status: operation.status || "PENDING",
      notes: operation.notes || "",
    });
    setIsEditing(true);
    setFormErrors([]);
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    const errors = validateCutOperationData(formData);
    if (errors.length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setSubmitting(true);
      if (isEditing && selectedOperation) {
        await updateCutOperation(selectedOperation.id, formData);
        toast({
          title: "Success",
          description: "Cut operation updated successfully.",
        });
      } else {
        await createCutOperation(formData);
        toast({
          title: "Success",
          description: "Cut operation created successfully.",
        });
      }
      setIsDialogOpen(false);
      loadCutOperations();
    } catch (error) {
      console.error("Error saving cut operation:", error);
      toast({
        title: "Error",
        description: "Failed to save cut operation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteOperation = async (operation: CutOperation) => {
    if (!confirm(`Are you sure you want to delete this cut operation?`)) {
      return;
    }

    try {
      await deleteCutOperation(operation.id);
      toast({
        title: "Success",
        description: "Cut operation deleted successfully.",
      });
      loadCutOperations();
    } catch (error) {
      console.error("Error deleting cut operation:", error);
      toast({
        title: "Error",
        description: "Failed to delete cut operation. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const option = STATUS_OPTIONS.find(opt => opt.value === status);
    return (
      <Badge className={option?.color || "bg-gray-100 text-gray-800"}>
        {option?.label || status}
      </Badge>
    );
  };

  const totals = calculateCutOperationTotals(operations);
  const summary = generateCutOperationSummary(operations);

  return (
    <div className="space-y-6">
      {/* Header with Summary */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-xl font-semibold">Cut Operations</h3>
          <div className="flex gap-4 mt-2 text-sm text-gray-600">
            <span>Total Operations: {totals.totalOperations}</span>
            <span>Total Cost: €{totals.totalCost.toFixed(2)}</span>
            <span>Avg Cost: €{(totals.totalCost / Math.max(totals.totalOperations, 1)).toFixed(2)}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowSummary(!showSummary)}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            {showSummary ? "Hide" : "Show"} Summary
          </Button>
          {!readOnly && requestItemId && (
            <Button onClick={handleCreateOperation} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Operation
            </Button>
          )}
        </div>
      </div>

      {/* Summary Card */}
      {showSummary && (
        <Card>
          <CardHeader>
            <CardTitle>Operations Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summary.details.completed}</div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{summary.details.inProgress}</div>
                <div className="text-sm text-gray-600">In Progress</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{summary.details.pending}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{summary.details.cancelled}</div>
                <div className="text-sm text-gray-600">Cancelled</div>
              </div>
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded">
              <p className="text-sm">{summary.summary}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Operations Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Processing</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {operations.map((operation) => (
                  <TableRow
                    key={operation.id}
                    className={`cursor-pointer hover:bg-gray-50 ${
                      selectedOperation?.id === operation.id ? "bg-blue-50" : ""
                    }`}
                    onClick={() => setSelectedOperation(operation)}
                  >
                    <TableCell className="font-medium">{operation.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{operation.processing?.description1}</div>
                        <div className="text-sm text-gray-500">{operation.processing?.tireType}</div>
                      </div>
                    </TableCell>
                    <TableCell>{operation.quantity}</TableCell>
                    <TableCell>€{(operation.cutPrice || 0).toFixed(2)}</TableCell>
                    <TableCell>€{((operation.cutPrice || 0) * operation.quantity).toFixed(2)}</TableCell>
                    <TableCell>{getStatusBadge(operation.status || "PENDING")}</TableCell>
                    <TableCell>
                      {operation.createdDate ? new Date(operation.createdDate).toLocaleDateString() : "-"}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {!readOnly && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditOperation(operation);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteOperation(operation);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{isEditing ? "Edit Cut Operation" : "Create New Cut Operation"}</DialogTitle>
            <DialogDescription>
              {isEditing ? "Update cut operation information" : "Add a new cut operation"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="processingId">Processing *</Label>
              <Select
                value={formData.processingId}
                onValueChange={(value) => setFormData({ ...formData, processingId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select processing" />
                </SelectTrigger>
                <SelectContent>
                  {availableProcessing.map((processing) => (
                    <SelectItem key={processing.id} value={processing.id}>
                      {processing.description1} - {processing.tireType}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={formData.quantity}
                  onChange={(e) => setFormData({ ...formData, quantity: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <Label htmlFor="cutPrice">Cut Price (€)</Label>
                <Input
                  id="cutPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cutPrice}
                  onChange={(e) => setFormData({ ...formData, cutPrice: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value as any })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Additional notes"
              />
            </div>
          </div>

          {formErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <ul className="text-red-600 text-sm space-y-1">
                {formErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={submitting}>
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
