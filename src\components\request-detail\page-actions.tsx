
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface DetailPageActionsProps {
  onHomeClick: () => void;
  onCopySendClick: () => void;
  onSaveClick: () => void;
  isSaving?: boolean;
}

export function DetailPageActions({ onHomeClick, onCopySendClick, onSaveClick, isSaving = false }: DetailPageActionsProps) {
  return (
    <section aria-labelledby="detail-page-actions-heading" className="flex flex-wrap justify-start gap-2">
      <h2 id="detail-page-actions-heading" className="sr-only">Page Actions</h2>
      <Button variant="outline" onClick={onHomeClick}>HOME</Button>
      <Button variant="default" className="bg-accent hover:bg-accent/90" onClick={onCopySendClick}>Copy + Send</Button>
      <Button variant="default" onClick={onSaveClick} disabled={isSaving}>
        {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {isSaving ? "Saving..." : "Save"}
      </Button>
    </section>
  );
}
