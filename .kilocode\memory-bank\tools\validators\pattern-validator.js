/**
 * Pattern Validator for CutRequestStudio Memory Bank
 * Validates extracted patterns and ensures knowledge quality
 */

const fs = require('fs');
const path = require('path');

class PatternValidator {
  constructor(memoryBankPath = '.kilocode/memory-bank') {
    this.memoryBankPath = memoryBankPath;
    this.validationRules = {
      technical: this.getTechnicalValidationRules(),
      development: this.getDevelopmentValidationRules(),
      business: this.getBusinessValidationRules(),
      operational: this.getOperationalValidationRules()
    };
    this.validationResults = {
      passed: [],
      failed: [],
      warnings: []
    };
  }

  /**
   * Validate all knowledge in the memory bank
   */
  async validateAll() {
    console.log('🔍 Starting comprehensive knowledge validation...');

    await this.validateTechnicalKnowledge();
    await this.validateDevelopmentHistory();
    await this.validateBusinessDomain();
    await this.validateOperationalKnowledge();
    await this.validateCrossReferences();
    await this.validateSearchIndex();

    const report = this.generateValidationReport();
    await this.saveValidationReport(report);

    console.log('✅ Knowledge validation completed');
    return report;
  }

  /**
   * Technical knowledge validation rules
   */
  getTechnicalValidationRules() {
    return {
      architecture: {
        required: ['patterns', 'components', 'services', 'integrations'],
        patterns: {
          required: ['name', 'type', 'description', 'file', 'usage', 'benefits'],
          validation: {
            name: (value) => typeof value === 'string' && value.length > 0,
            type: (value) => ['Service Pattern', 'Component Pattern', 'Hook Pattern', 'Middleware Pattern'].includes(value),
            description: (value) => typeof value === 'string' && value.length > 10,
            file: (value) => typeof value === 'string' && value.includes('.'),
            usage: (value) => typeof value === 'string' && value.length > 0,
            benefits: (value) => Array.isArray(value) && value.length > 0
          }
        }
      },
      api: {
        required: ['schemas', 'endpoints', 'types', 'validation'],
        endpoints: {
          required: ['path', 'method', 'description', 'parameters', 'responses'],
          validation: {
            path: (value) => typeof value === 'string' && value.startsWith('/'),
            method: (value) => ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].includes(value),
            description: (value) => typeof value === 'string' && value.length > 5
          }
        }
      },
      frontend: {
        required: ['components', 'hooks', 'contexts', 'utilities'],
        components: {
          validation: {
            name: (value) => typeof value === 'string' && /^[A-Z]/.test(value),
            file: (value) => typeof value === 'string' && value.includes('.tsx')
          }
        }
      },
      backend: {
        required: ['models', 'crud', 'routers', 'middleware'],
        models: {
          validation: {
            name: (value) => typeof value === 'string' && /^[A-Z]/.test(value),
            file: (value) => typeof value === 'string' && value.includes('.py')
          }
        }
      },
      performance: {
        required: ['optimizations', 'monitoring', 'caching', 'benchmarks'],
        benchmarks: {
          validation: {
            metric: (value) => typeof value === 'string',
            value: (value) => typeof value === 'number' && value >= 0,
            unit: (value) => typeof value === 'string'
          }
        }
      }
    };
  }

  /**
   * Development history validation rules
   */
  getDevelopmentValidationRules() {
    return {
      history: {
        required: ['priority-1', 'priority-2', 'priority-3', 'timeline'],
        priorities: {
          required: ['priority', 'document', 'summary', 'components', 'results'],
          validation: {
            priority: (value) => [1, 2, 3].includes(value),
            document: (value) => typeof value === 'string' && value.includes('.md'),
            summary: (value) => typeof value === 'string' && value.length > 50
          }
        }
      },
      decisions: {
        required: ['architecture', 'technology', 'patterns', 'trade-offs'],
        decision: {
          required: ['title', 'date', 'context', 'decision', 'rationale', 'consequences'],
          validation: {
            title: (value) => typeof value === 'string' && value.length > 5,
            date: (value) => !isNaN(Date.parse(value)),
            context: (value) => typeof value === 'string' && value.length > 20,
            decision: (value) => typeof value === 'string' && value.length > 10,
            rationale: (value) => typeof value === 'string' && value.length > 20
          }
        }
      }
    };
  }

  /**
   * Business domain validation rules
   */
  getBusinessValidationRules() {
    return {
      domain: {
        required: ['tire-processing', 'request-management', 'quality-control', 'reporting'],
        workflow: {
          required: ['name', 'description', 'steps', 'actors', 'inputs', 'outputs'],
          validation: {
            name: (value) => typeof value === 'string' && value.length > 0,
            description: (value) => typeof value === 'string' && value.length > 20,
            steps: (value) => Array.isArray(value) && value.length > 0,
            actors: (value) => Array.isArray(value) && value.length > 0
          }
        }
      },
      rules: {
        required: ['validation', 'business-logic', 'permissions', 'constraints'],
        rule: {
          required: ['name', 'description', 'condition', 'action'],
          validation: {
            name: (value) => typeof value === 'string' && value.length > 0,
            description: (value) => typeof value === 'string' && value.length > 10,
            condition: (value) => typeof value === 'string' && value.length > 5,
            action: (value) => typeof value === 'string' && value.length > 5
          }
        }
      }
    };
  }

  /**
   * Operational knowledge validation rules
   */
  getOperationalValidationRules() {
    return {
      development: {
        required: ['setup', 'guidelines', 'tools', 'workflows'],
        procedure: {
          required: ['title', 'description', 'steps', 'prerequisites', 'expected-outcome'],
          validation: {
            title: (value) => typeof value === 'string' && value.length > 0,
            description: (value) => typeof value === 'string' && value.length > 20,
            steps: (value) => Array.isArray(value) && value.length > 0,
            prerequisites: (value) => Array.isArray(value)
          }
        }
      },
      troubleshooting: {
        required: ['common-issues', 'debugging', 'performance', 'recovery'],
        issue: {
          required: ['problem', 'symptoms', 'causes', 'solutions'],
          validation: {
            problem: (value) => typeof value === 'string' && value.length > 10,
            symptoms: (value) => Array.isArray(value) && value.length > 0,
            causes: (value) => Array.isArray(value) && value.length > 0,
            solutions: (value) => Array.isArray(value) && value.length > 0
          }
        }
      }
    };
  }

  /**
   * Validate technical knowledge
   */
  async validateTechnicalKnowledge() {
    console.log('📐 Validating technical knowledge...');

    const technicalPath = path.join(this.memoryBankPath, 'technical');
    if (!fs.existsSync(technicalPath)) {
      this.validationResults.failed.push({
        category: 'technical',
        issue: 'Technical knowledge directory not found',
        severity: 'error'
      });
      return;
    }

    const rules = this.validationRules.technical;

    // Validate architecture patterns
    await this.validateSubcategory('technical', 'architecture', rules.architecture);

    // Validate API documentation
    await this.validateSubcategory('technical', 'api', rules.api);

    // Validate frontend patterns
    await this.validateSubcategory('technical', 'frontend', rules.frontend);

    // Validate backend patterns
    await this.validateSubcategory('technical', 'backend', rules.backend);

    // Validate performance data
    await this.validateSubcategory('technical', 'performance', rules.performance);
  }

  /**
   * Validate development history
   */
  async validateDevelopmentHistory() {
    console.log('📚 Validating development history...');

    const developmentPath = path.join(this.memoryBankPath, 'development');
    if (!fs.existsSync(developmentPath)) {
      this.validationResults.failed.push({
        category: 'development',
        issue: 'Development history directory not found',
        severity: 'error'
      });
      return;
    }

    const rules = this.validationRules.development;

    // Validate priority implementations
    await this.validateSubcategory('development', 'history', rules.history);

    // Validate technical decisions
    await this.validateSubcategory('development', 'decisions', rules.decisions);
  }

  /**
   * Validate business domain knowledge
   */
  async validateBusinessDomain() {
    console.log('🏢 Validating business domain knowledge...');

    const businessPath = path.join(this.memoryBankPath, 'business');
    if (!fs.existsSync(businessPath)) {
      this.validationResults.failed.push({
        category: 'business',
        issue: 'Business domain directory not found',
        severity: 'error'
      });
      return;
    }

    const rules = this.validationRules.business;

    // Validate domain knowledge
    await this.validateSubcategory('business', 'domain', rules.domain);

    // Validate business rules
    await this.validateSubcategory('business', 'rules', rules.rules);
  }

  /**
   * Validate operational knowledge
   */
  async validateOperationalKnowledge() {
    console.log('⚙️ Validating operational knowledge...');

    const operationalPath = path.join(this.memoryBankPath, 'operational');
    if (!fs.existsSync(operationalPath)) {
      this.validationResults.failed.push({
        category: 'operational',
        issue: 'Operational knowledge directory not found',
        severity: 'error'
      });
      return;
    }

    const rules = this.validationRules.operational;

    // Validate development procedures
    await this.validateSubcategory('operational', 'development', rules.development);

    // Validate troubleshooting guides
    await this.validateSubcategory('operational', 'troubleshooting', rules.troubleshooting);
  }

  /**
   * Validate a subcategory against rules
   */
  async validateSubcategory(category, subcategory, rules) {
    const subcategoryPath = path.join(this.memoryBankPath, category, subcategory);

    if (!fs.existsSync(subcategoryPath)) {
      this.validationResults.warnings.push({
        category,
        subcategory,
        issue: `Subcategory directory not found: ${subcategoryPath}`,
        severity: 'warning'
      });
      return;
    }

    const knowledgeFile = path.join(subcategoryPath, 'extracted-knowledge.json');
    if (!fs.existsSync(knowledgeFile)) {
      this.validationResults.warnings.push({
        category,
        subcategory,
        issue: 'No extracted knowledge file found',
        severity: 'warning'
      });
      return;
    }

    try {
      const knowledge = JSON.parse(fs.readFileSync(knowledgeFile, 'utf8'));
      this.validateKnowledgeStructure(knowledge, rules, category, subcategory);
    } catch (error) {
      this.validationResults.failed.push({
        category,
        subcategory,
        issue: `Invalid JSON in knowledge file: ${error.message}`,
        severity: 'error'
      });
    }
  }

  /**
   * Validate knowledge structure against rules
   */
  validateKnowledgeStructure(knowledge, rules, category, subcategory) {
    // Check required top-level properties
    if (rules.required) {
      for (const required of rules.required) {
        if (!knowledge.hasOwnProperty(required)) {
          this.validationResults.failed.push({
            category,
            subcategory,
            issue: `Missing required property: ${required}`,
            severity: 'error'
          });
        }
      }
    }

    // Validate specific structures
    for (const [key, value] of Object.entries(knowledge)) {
      if (rules[key] && rules[key].validation) {
        this.validateItem(value, rules[key], category, subcategory, key);
      }
    }

    this.validationResults.passed.push({
      category,
      subcategory,
      message: 'Knowledge structure validation passed'
    });
  }

  /**
   * Validate individual knowledge items
   */
  validateItem(item, rules, category, subcategory, itemKey) {
    if (Array.isArray(item)) {
      item.forEach((element, index) => {
        this.validateSingleItem(element, rules, category, subcategory, `${itemKey}[${index}]`);
      });
    } else if (typeof item === 'object') {
      this.validateSingleItem(item, rules, category, subcategory, itemKey);
    }
  }

  /**
   * Validate a single knowledge item
   */
  validateSingleItem(item, rules, category, subcategory, itemPath) {
    // Check required properties
    if (rules.required) {
      for (const required of rules.required) {
        if (!item.hasOwnProperty(required)) {
          this.validationResults.failed.push({
            category,
            subcategory,
            item: itemPath,
            issue: `Missing required property: ${required}`,
            severity: 'error'
          });
        }
      }
    }

    // Validate properties
    if (rules.validation) {
      for (const [prop, validator] of Object.entries(rules.validation)) {
        if (item.hasOwnProperty(prop)) {
          if (!validator(item[prop])) {
            this.validationResults.failed.push({
              category,
              subcategory,
              item: itemPath,
              property: prop,
              issue: `Validation failed for property: ${prop}`,
              value: item[prop],
              severity: 'error'
            });
          }
        }
      }
    }
  }

  /**
   * Validate cross-references
   */
  async validateCrossReferences() {
    console.log('🔗 Validating cross-references...');

    const crossRefPath = path.join(this.memoryBankPath, 'index', 'cross-references.json');
    if (!fs.existsSync(crossRefPath)) {
      this.validationResults.failed.push({
        category: 'index',
        issue: 'Cross-references file not found',
        severity: 'error'
      });
      return;
    }

    try {
      const crossRefs = JSON.parse(fs.readFileSync(crossRefPath, 'utf8'));

      // Validate structure
      if (!crossRefs.crossReferences) {
        this.validationResults.failed.push({
          category: 'index',
          issue: 'Invalid cross-references structure',
          severity: 'error'
        });
        return;
      }

      this.validationResults.passed.push({
        category: 'index',
        message: 'Cross-references validation passed'
      });
    } catch (error) {
      this.validationResults.failed.push({
        category: 'index',
        issue: `Invalid cross-references JSON: ${error.message}`,
        severity: 'error'
      });
    }
  }

  /**
   * Validate search index
   */
  async validateSearchIndex() {
    console.log('🔍 Validating search index...');

    const searchIndexPath = path.join(this.memoryBankPath, 'index', 'search-index.json');
    if (!fs.existsSync(searchIndexPath)) {
      this.validationResults.failed.push({
        category: 'index',
        issue: 'Search index file not found',
        severity: 'error'
      });
      return;
    }

    try {
      const searchIndex = JSON.parse(fs.readFileSync(searchIndexPath, 'utf8'));

      // Validate structure
      if (!searchIndex.searchIndex) {
        this.validationResults.failed.push({
          category: 'index',
          issue: 'Invalid search index structure',
          severity: 'error'
        });
        return;
      }

      this.validationResults.passed.push({
        category: 'index',
        message: 'Search index validation passed'
      });
    } catch (error) {
      this.validationResults.failed.push({
        category: 'index',
        issue: `Invalid search index JSON: ${error.message}`,
        severity: 'error'
      });
    }
  }

  /**
   * Generate validation report
   */
  generateValidationReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.validationResults.passed.length + this.validationResults.failed.length + this.validationResults.warnings.length,
        passed: this.validationResults.passed.length,
        failed: this.validationResults.failed.length,
        warnings: this.validationResults.warnings.length,
        successRate: this.validationResults.passed.length / (this.validationResults.passed.length + this.validationResults.failed.length) * 100
      },
      results: this.validationResults,
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  /**
   * Generate recommendations based on validation results
   */
  generateRecommendations() {
    const recommendations = [];

    // Analyze failed validations
    const failuresByCategory = {};
    this.validationResults.failed.forEach(failure => {
      if (!failuresByCategory[failure.category]) {
        failuresByCategory[failure.category] = [];
      }
      failuresByCategory[failure.category].push(failure);
    });

    // Generate category-specific recommendations
    for (const [category, failures] of Object.entries(failuresByCategory)) {
      if (failures.length > 5) {
        recommendations.push({
          priority: 'high',
          category,
          recommendation: `High number of validation failures in ${category}. Consider reviewing extraction logic and data quality.`,
          action: 'Review and improve knowledge extraction for this category'
        });
      }
    }

    // Check for missing directories
    const missingDirs = this.validationResults.failed.filter(f => f.issue.includes('directory not found'));
    if (missingDirs.length > 0) {
      recommendations.push({
        priority: 'medium',
        recommendation: 'Some knowledge categories are missing. Run knowledge extraction to populate missing areas.',
        action: 'Execute knowledge extraction for missing categories'
      });
    }

    return recommendations;
  }

  /**
   * Save validation report
   */
  async saveValidationReport(report) {
    const reportPath = path.join(this.memoryBankPath, 'validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 Validation report saved to: ${reportPath}`);
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`⚠️  Warnings: ${report.summary.warnings}`);
    console.log(`📈 Success Rate: ${report.summary.successRate.toFixed(1)}%`);
  }
}

module.exports = PatternValidator;

// CLI usage
if (require.main === module) {
  const validator = new PatternValidator();
  validator.validateAll().catch(console.error);
}