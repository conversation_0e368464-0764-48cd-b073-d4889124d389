#!/usr/bin/env python3
"""
Test API endpoints with authentication
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from backend.main import app

def test_api_with_auth():
    """Test API endpoints with authentication"""
    print("🚀 Testing API Endpoints with Authentication")
    print("=" * 60)

    client = TestClient(app)

    # First, login to get a token
    print("\n🔐 Testing Authentication")
    login_data = {
        "username": "<EMAIL>",
        "password": "password123"
    }

    response = client.post("/api/v1/users/login", data=login_data)
    print(f"   Login Status: {response.status_code}")

    if response.status_code != 200:
        print(f"   ❌ Login failed: {response.text}")
        print("   Cannot test authenticated endpoints without valid login")
        return

    token_data = response.json()
    token = token_data.get("access_token")
    print(f"   ✅ Login successful, token obtained")

    # Set up headers with authentication
    headers = {"Authorization": f"Bearer {token}"}

    # Test enhanced tires endpoint
    print("\n🛞 Testing Enhanced Tires API (Authenticated)")
    response = client.get("/api/v1/tires", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        tires = response.json()
        print(f"   ✅ Found {len(tires)} tires")
        if tires:
            tire = tires[0]
            print(f"   📋 Sample tire: {tire.get('tugNo', 'N/A')} - {tire.get('size', 'N/A')} ({tire.get('owner', 'N/A')})")
    else:
        print(f"   ❌ Error: {response.text}")

    # Test tire statistics
    print("\n📊 Testing Tire Statistics API")
    response = client.get("/api/v1/tires/stats/summary", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"   ✅ Statistics loaded")
        print(f"   📋 Total Tires: {stats.get('total_tires', 0)}")
        print(f"   ✅ Active Tires: {stats.get('active_tires', 0)}")
        print(f"   ❌ Inactive Tires: {stats.get('inactive_tires', 0)}")

        top_owners = stats.get('top_owners', [])
        if top_owners:
            print(f"   🏆 Top Owner: {top_owners[0].get('owner', 'N/A')} ({top_owners[0].get('count', 0)} tires)")
    else:
        print(f"   ❌ Error: {response.text}")

    # Test request items endpoint
    print("\n📦 Testing Request Items API")
    response = client.get("/api/v1/request-items/request/REQ001", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        items = response.json()
        print(f"   ✅ Found {len(items)} request items for REQ001")
        if items:
            item = items[0]
            print(f"   📋 Sample item: {item.get('id', 'N/A')} - Qty: {item.get('quantity', 'N/A')}")
            tire = item.get('tire', {})
            if tire:
                print(f"   🛞 Tire: {tire.get('tugNo', 'N/A')} - {tire.get('size', 'N/A')}")
    else:
        print(f"   ❌ Error: {response.text}")

    # Test cut operations endpoint
    print("\n✂️ Testing Cut Operations API")
    response = client.get("/api/v1/cut-operations/request/REQ001", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        operations = response.json()
        print(f"   ✅ Found {len(operations)} cut operations for REQ001")
        if operations:
            operation = operations[0]
            print(f"   📋 Sample operation: ID {operation.get('id', 'N/A')} - Status: {operation.get('status', 'N/A')}")
            processing = operation.get('processing', {})
            if processing:
                print(f"   ⚙️ Processing: {processing.get('description1', 'N/A')} - {processing.get('tireType', 'N/A')}")
    else:
        print(f"   ❌ Error: {response.text}")

    # Test cut operations statistics
    print("\n📊 Testing Cut Operations Statistics API")
    response = client.get("/api/v1/cut-operations/stats/summary", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"   ✅ Statistics loaded")
        print(f"   📋 Total Operations: {stats.get('total_operations', 0)}")
        financial = stats.get('financial', {})
        print(f"   💰 Total Cost: €{financial.get('total_cost', 0):.2f}")
        print(f"   💰 Average Cost: €{financial.get('average_cost', 0):.2f}")

        status_breakdown = stats.get('status_breakdown', [])
        if status_breakdown:
            print(f"   📊 Status Breakdown:")
            for status_info in status_breakdown:
                print(f"      - {status_info.get('status', 'N/A')}: {status_info.get('count', 0)}")
    else:
        print(f"   ❌ Error: {response.text}")

    # Test simplified request endpoint
    print("\n📋 Testing Simplified Request API")
    response = client.get("/api/v1/requests/REQ001/simplified", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        request = response.json()
        print(f"   ✅ Request: {request.get('id', 'N/A')} - {request.get('projectNo', 'N/A')}")
        print(f"   📋 Status: {request.get('status', 'N/A')} - Type: {request.get('type', 'N/A')}")
        print(f"   📦 Items: {len(request.get('requestItems', []))}")
        print(f"   📎 Attachments: {len(request.get('attachments', []))}")
    else:
        print(f"   ❌ Error: {response.text}")

    # Test request summary endpoint
    print("\n📊 Testing Request Summary API")
    response = client.get("/api/v1/requests/REQ001/summary", headers=headers)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        summary = response.json()
        stats = summary.get('statistics', {})
        print(f"   ✅ Summary loaded")
        print(f"   📋 Total Items: {stats.get('total_items', 0)}")
        print(f"   ✂️ Total Cuts: {stats.get('total_cuts', 0)}")
        print(f"   💰 Total Cost: €{stats.get('total_cost', 0):.2f}")

        status_breakdown = stats.get('status_breakdown', {})
        if status_breakdown:
            print(f"   📊 Cut Status Breakdown:")
            for status, count in status_breakdown.items():
                print(f"      - {status}: {count}")

        processing_breakdown = stats.get('processing_breakdown', {})
        if processing_breakdown:
            print(f"   ⚙️ Processing Breakdown:")
            for proc_type, count in processing_breakdown.items():
                print(f"      - {proc_type}: {count}")
    else:
        print(f"   ❌ Error: {response.text}")

    print("\n" + "=" * 60)
    print("✅ Authenticated API endpoint testing completed!")
    print("\n🎯 Summary:")
    print("   - Authentication system working correctly")
    print("   - All new simplified API endpoints are functional")
    print("   - Database relationships are properly maintained")
    print("   - Statistics and summary endpoints provide valuable insights")

if __name__ == "__main__":
    test_api_with_auth()
