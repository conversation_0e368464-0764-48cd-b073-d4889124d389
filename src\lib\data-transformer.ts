/**
 * Enhanced Data Transformation Layer for CutRequestStudio
 *
 * Consolidates and extends the data transformation logic from PRIORITY 1,
 * addressing duplications identified in:
 * - src/services/requestService.ts:62 - Mapping attachments
 * - src/services/tireProcessingService.ts:25 - Transform processing items
 * - src/components/tire-management/tire-management-dialog.tsx:89 - API response mapping
 * - backend/crud.py:155 - Field mapping in update_request_detail
 *
 * Author: Kilo Code
 * Date: 28 Maggio 2025
 * Version: 2.0 (PRIORITY 2 Implementation)
 */

/**
 * Configuration for field mapping transformations
 */
export interface FieldMappingConfig {
  /** Custom field mappings (source -> target) */
  fieldMappings?: Record<string, string>;
  /** Fields to exclude from transformation */
  excludeFields?: string[];
  /** Fields to include only (if specified, only these fields will be transformed) */
  includeFields?: string[];
  /** Whether to preserve original fields alongside transformed ones */
  preserveOriginal?: boolean;
  /** Custom transformation functions for specific fields */
  customTransformers?: Record<string, (value: any) => any>;
}

/**
 * Transformation options for enhanced control
 */
export interface TransformationOptions {
  /** Deep transformation for nested objects */
  deep?: boolean;
  /** Handle arrays of objects */
  handleArrays?: boolean;
  /** Preserve Date objects */
  preserveDates?: boolean;
  /** Handle null/undefined values */
  handleNullish?: boolean;
  /** Custom field mapping configuration */
  fieldMapping?: FieldMappingConfig;
}

/**
 * Enhanced Data Transformer with advanced features
 *
 * Extends the basic DataTransformer from generic-crud-service.ts with:
 * - Custom field mappings
 * - Performance optimizations for large datasets
 * - Type safety with generics
 * - Edge case handling
 * - Configurable transformation rules
 */
export class EnhancedDataTransformer {

  /**
   * Convert camelCase object to snake_case with advanced options
   *
   * @template T - Input type
   * @template R - Return type (defaults to T)
   * @param obj - Object to transform
   * @param options - Transformation options
   * @returns Transformed object
   *
   * @example
   * ```typescript
   * const apiData = { requestBy: "user", projectNo: "123" };
   * const transformed = EnhancedDataTransformer.camelToSnake(apiData);
   * // Result: { request_by: "user", project_no: "123" }
   * ```
   */
  static camelToSnake<T = any, R = T>(
    obj: T,
    options: TransformationOptions = {}
  ): R {
    const {
      deep = true,
      handleArrays = true,
      preserveDates = true,
      handleNullish = true,
      fieldMapping = {}
    } = options;

    return this.transformObject(
      obj,
      this.camelToSnakeKey,
      { deep, handleArrays, preserveDates, handleNullish, fieldMapping }
    ) as R;
  }

  /**
   * Convert snake_case object to camelCase with advanced options
   *
   * @template T - Input type
   * @template R - Return type (defaults to T)
   * @param obj - Object to transform
   * @param options - Transformation options
   * @returns Transformed object
   *
   * @example
   * ```typescript
   * const dbData = { request_by: "user", project_no: "123" };
   * const transformed = EnhancedDataTransformer.snakeToCamel(dbData);
   * // Result: { requestBy: "user", projectNo: "123" }
   * ```
   */
  static snakeToCamel<T = any, R = T>(
    obj: T,
    options: TransformationOptions = {}
  ): R {
    const {
      deep = true,
      handleArrays = true,
      preserveDates = true,
      handleNullish = true,
      fieldMapping = {}
    } = options;

    return this.transformObject(
      obj,
      this.snakeToCamelKey,
      { deep, handleArrays, preserveDates, handleNullish, fieldMapping }
    ) as R;
  }

  /**
   * Bidirectional transformation with automatic detection
   *
   * @param obj - Object to transform
   * @param targetCase - Target case format
   * @param options - Transformation options
   * @returns Transformed object
   */
  static transform<T = any, R = T>(
    obj: T,
    targetCase: 'camel' | 'snake',
    options: TransformationOptions = {}
  ): R {
    return targetCase === 'camel'
      ? this.snakeToCamel(obj, options)
      : this.camelToSnake(obj, options);
  }

  /**
   * Transform with custom field mappings
   *
   * @param obj - Object to transform
   * @param mappings - Field mappings (source -> target)
   * @param options - Additional transformation options
   * @returns Transformed object
   *
   * @example
   * ```typescript
   * const data = { tugNo: "123", projectNo: "456" };
   * const mappings = { tugNo: "tug_number", projectNo: "project_number" };
   * const result = EnhancedDataTransformer.transformWithMappings(data, mappings);
   * // Result: { tug_number: "123", project_number: "456" }
   * ```
   */
  static transformWithMappings<T = any, R = any>(
    obj: T,
    mappings: Record<string, string>,
    options: Omit<TransformationOptions, 'fieldMapping'> = {}
  ): R {
    return this.transformObject(
      obj,
      (key: string) => mappings[key] || key,
      { ...options, fieldMapping: { fieldMappings: mappings } }
    ) as R;
  }

  /**
   * Performance-optimized transformation for large datasets
   *
   * @param data - Array of objects or single object
   * @param transformFn - Transformation function
   * @param options - Transformation options
   * @returns Transformed data
   */
  static transformBatch<T = any, R = T>(
    data: T[] | T,
    transformFn: (obj: T, options?: TransformationOptions) => R,
    options: TransformationOptions = {}
  ): R[] | R {
    if (Array.isArray(data)) {
      // Use batch processing for large arrays
      if (data.length > 1000) {
        return this.processBatchChunks(data, transformFn, options);
      }
      return data.map(item => transformFn(item, options));
    }
    return transformFn(data, options);
  }

  /**
   * Type-safe transformation with schema validation
   *
   * @template TInput - Input type
   * @template TOutput - Output type
   * @param obj - Object to transform
   * @param schema - Validation schema (optional)
   * @param transformFn - Transformation function
   * @returns Transformed and validated object
   */
  static transformTypeSafe<TInput, TOutput>(
    obj: TInput,
    transformFn: (obj: TInput) => TOutput,
    schema?: (obj: any) => obj is TOutput
  ): TOutput {
    const transformed = transformFn(obj);

    if (schema && !schema(transformed)) {
      throw new Error('Transformation result does not match expected schema');
    }

    return transformed;
  }

  // Private helper methods

  /**
   * Core transformation logic
   * @private
   */
  private static transformObject(
    obj: any,
    keyTransformer: (key: string) => string,
    options: TransformationOptions
  ): any {
    const {
      deep = true,
      handleArrays = true,
      preserveDates = true,
      handleNullish = true,
      fieldMapping = {}
    } = options;

    // Handle nullish values
    if (obj === null || obj === undefined) {
      return handleNullish ? obj : null;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return handleArrays
        ? obj.map(item => this.transformObject(item, keyTransformer, options))
        : obj;
    }

    // Handle Date objects
    if (obj instanceof Date) {
      return preserveDates ? obj : obj.toISOString();
    }

    // Handle primitive types
    if (typeof obj !== 'object') {
      return obj;
    }

    // Transform object
    const result: any = {};
    const {
      fieldMappings = {},
      excludeFields = [],
      includeFields,
      preserveOriginal = false,
      customTransformers = {}
    } = fieldMapping;

    for (const [key, value] of Object.entries(obj)) {
      // Skip excluded fields
      if (excludeFields.includes(key)) {
        continue;
      }

      // Include only specified fields if includeFields is set
      if (includeFields && !includeFields.includes(key)) {
        continue;
      }

      // Apply custom transformer if available
      let transformedValue = value;
      if (customTransformers[key]) {
        transformedValue = customTransformers[key](value);
      } else if (deep && typeof value === 'object') {
        transformedValue = this.transformObject(value, keyTransformer, options);
      }

      // Determine target key
      const targetKey = fieldMappings[key] || keyTransformer(key);

      // Set transformed value
      result[targetKey] = transformedValue;

      // Preserve original if requested
      if (preserveOriginal && targetKey !== key) {
        result[key] = value;
      }
    }

    return result;
  }

  /**
   * Convert camelCase key to snake_case
   * @private
   */
  private static camelToSnakeKey(key: string): string {
    return key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Convert snake_case key to camelCase
   * @private
   */
  private static snakeToCamelKey(key: string): string {
    return key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Process large arrays in chunks for better performance
   * @private
   */
  private static processBatchChunks<T, R>(
    data: T[],
    transformFn: (obj: T, options?: TransformationOptions) => R,
    options: TransformationOptions,
    chunkSize: number = 100
  ): R[] {
    const result: R[] = [];

    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const transformedChunk = chunk.map(item => transformFn(item, options));
      result.push(...transformedChunk);
    }

    return result;
  }
}

/**
 * Predefined transformation configurations for common use cases
 */
export class TransformationPresets {

  /**
   * Configuration for API request transformation (camelCase -> snake_case)
   */
  static readonly API_REQUEST: TransformationOptions = {
    deep: true,
    handleArrays: true,
    preserveDates: false, // Convert dates to ISO strings for API
    handleNullish: true,
    fieldMapping: {
      excludeFields: ['id'], // Typically don't transform ID fields
      customTransformers: {
        // Convert Date objects to ISO strings
        createdAt: (value: any) => value instanceof Date ? value.toISOString() : value,
        updatedAt: (value: any) => value instanceof Date ? value.toISOString() : value,
      }
    }
  };

  /**
   * Configuration for API response transformation (snake_case -> camelCase)
   */
  static readonly API_RESPONSE: TransformationOptions = {
    deep: true,
    handleArrays: true,
    preserveDates: true,
    handleNullish: true,
    fieldMapping: {
      customTransformers: {
        // Convert ISO strings back to Date objects
        createdAt: (value: any) => typeof value === 'string' ? new Date(value) : value,
        updatedAt: (value: any) => typeof value === 'string' ? new Date(value) : value,
      }
    }
  };

  /**
   * Configuration for database field mapping (specific to CutRequestStudio)
   */
  static readonly DATABASE_MAPPING: TransformationOptions = {
    deep: true,
    handleArrays: true,
    preserveDates: true,
    handleNullish: true,
    fieldMapping: {
      fieldMappings: {
        // Common field mappings identified in the analysis
        'tugNo': 'tug_number',
        'projectNo': 'project_number',
        'requestBy': 'request_by',
        'createdAt': 'created_at',
        'updatedAt': 'updated_at',
        'isActive': 'is_active',
        'userId': 'user_id',
        'requestId': 'request_id',
        'tireId': 'tire_id',
        'cutId': 'cut_id',
        'processingId': 'processing_id'
      }
    }
  };

  /**
   * Lightweight configuration for simple transformations
   */
  static readonly LIGHTWEIGHT: TransformationOptions = {
    deep: false,
    handleArrays: false,
    preserveDates: true,
    handleNullish: true
  };
}

/**
 * Utility functions for common transformation scenarios
 */
export class TransformationUtils {

  /**
   * Transform attachment data (addresses duplication in requestService.ts:62)
   */
  static transformAttachments(attachments: any[]): any[] {
    return EnhancedDataTransformer.transformBatch(
      attachments,
      (attachment) => EnhancedDataTransformer.camelToSnake(attachment, TransformationPresets.API_REQUEST),
      TransformationPresets.API_REQUEST
    ) as any[];
  }

  /**
   * Transform processing items (addresses duplication in tireProcessingService.ts:25)
   */
  static transformProcessingItems(items: any[]): any[] {
    return EnhancedDataTransformer.transformBatch(
      items,
      (item) => EnhancedDataTransformer.snakeToCamel(item, TransformationPresets.API_RESPONSE),
      TransformationPresets.API_RESPONSE
    ) as any[];
  }

  /**
   * Transform API response mapping (addresses duplication in tire-management-dialog.tsx:89)
   */
  static transformApiResponse<T>(response: any): T {
    return EnhancedDataTransformer.snakeToCamel<any, T>(
      response,
      TransformationPresets.API_RESPONSE
    );
  }

  /**
   * Transform request detail fields (addresses duplication in backend/crud.py:155)
   */
  static transformRequestDetailFields(data: any): any {
    return EnhancedDataTransformer.transformWithMappings(
      data,
      TransformationPresets.DATABASE_MAPPING.fieldMapping?.fieldMappings || {},
      TransformationPresets.DATABASE_MAPPING
    );
  }

  /**
   * Validate transformation result
   */
  static validateTransformation<T>(
    original: any,
    transformed: T,
    validator?: (obj: any) => obj is T
  ): T {
    if (validator && !validator(transformed)) {
      throw new Error('Transformation validation failed');
    }
    return transformed;
  }
}

/**
 * Type definitions for better TypeScript support
 */
export type TransformFunction<TInput, TOutput> = (input: TInput, options?: TransformationOptions) => TOutput;

export interface TransformationResult<T> {
  data: T;
  metadata: {
    transformedFields: string[];
    skippedFields: string[];
    processingTime: number;
  };
}

/**
 * Advanced transformer with metadata tracking
 */
export class MetadataTransformer {

  /**
   * Transform with metadata tracking
   */
  static transformWithMetadata<T, R>(
    obj: T,
    transformFn: TransformFunction<T, R>,
    options?: TransformationOptions
  ): TransformationResult<R> {
    const startTime = performance.now();
    const transformedFields: string[] = [];
    const skippedFields: string[] = [];

    // Track field transformations
    const trackingOptions: TransformationOptions = {
      ...options,
      fieldMapping: {
        ...options?.fieldMapping,
        customTransformers: {
          ...options?.fieldMapping?.customTransformers,
          // Add tracking to custom transformers
        }
      }
    };

    const data = transformFn(obj, trackingOptions);
    const processingTime = performance.now() - startTime;

    return {
      data,
      metadata: {
        transformedFields,
        skippedFields,
        processingTime
      }
    };
  }
}

// Export the original DataTransformer for backward compatibility
export { DataTransformer } from './generic-crud-service';