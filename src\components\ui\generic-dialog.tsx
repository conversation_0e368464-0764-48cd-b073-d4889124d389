"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  Search,
  Filter,
  X,
  ArrowUpDown,
  RefreshCw,
  Calculator,
  Loader2
} from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * Configuration for table columns in the generic dialog
 */
export interface ColumnConfig<T> {
  /** Unique identifier for the column */
  key: keyof T;
  /** Display label for the column header */
  label: string;
  /** Whether this column is sortable */
  sortable?: boolean;
  /** Custom render function for the cell content */
  render?: (value: any, item: T) => React.ReactNode;
  /** CSS class for the column */
  className?: string;
  /** Column width */
  width?: string;
}

/**
 * Configuration for search filters
 */
export interface FilterConfig<T> {
  /** Unique identifier for the filter */
  key: keyof T;
  /** Display label for the filter */
  label: string;
  /** Type of filter input */
  type: 'text' | 'number' | 'select' | 'checkbox';
  /** Placeholder text for input fields */
  placeholder?: string;
  /** Options for select filters */
  options?: Array<{ value: string; label: string }>;
  /** Grid column span for layout */
  colSpan?: number;
}

/**
 * Configuration for pagination
 */
export interface PaginationConfig {
  /** Current page number (1-based) */
  currentPage: number;
  /** Total number of pages */
  totalPages: number;
  /** Items per page */
  itemsPerPage: number;
  /** Total number of items */
  totalItems?: number;
}

/**
 * Configuration for sorting
 */
export interface SortConfig<T> {
  /** Column key to sort by */
  key: keyof T | null;
  /** Sort direction */
  direction: 'asc' | 'desc';
}

/**
 * Props for the GenericDialog component
 */
export interface GenericDialogProps<T extends { id: string }> {
  /** Whether the dialog is open */
  isOpen: boolean;
  /** Function to call when dialog should close */
  onClose: () => void;
  /** Dialog title */
  title: string;
  /** Array of data items to display */
  data: T[];
  /** Column configuration for the table */
  columns: ColumnConfig<T>[];
  /** Filter configuration for search */
  filters?: FilterConfig<T>[];
  /** Current filter values */
  filterValues?: Record<string, any>;
  /** Function to handle filter changes */
  onFilterChange?: (key: string, value: any) => void;
  /** Function to handle search action */
  onSearch?: () => void;
  /** Function to handle filter reset */
  onResetFilters?: () => void;
  /** Whether to show the filter section */
  showFilters?: boolean;
  /** Whether to allow multiple selection */
  multiSelect?: boolean;
  /** Currently selected item IDs */
  selectedIds?: Set<string>;
  /** Function to handle selection changes */
  onSelectionChange?: (selectedIds: Set<string>) => void;
  /** Function to handle adding selected items */
  onAddSelected?: (selectedItems: T[]) => void;
  /** Function to handle adding all items */
  onAddAll?: (allItems: T[]) => void;
  /** Pagination configuration */
  pagination?: PaginationConfig;
  /** Function to handle page changes */
  onPageChange?: (page: number) => void;
  /** Sort configuration */
  sortConfig?: SortConfig<T>;
  /** Function to handle sort changes */
  onSortChange?: (key: keyof T) => void;
  /** Whether data is currently loading */
  loading?: boolean;
  /** Custom footer content */
  customFooter?: React.ReactNode;
  /** Additional CSS classes for the dialog */
  className?: string;
  /** Maximum dialog width */
  maxWidth?: string;
  /** Maximum dialog height */
  maxHeight?: string;
  /** Whether to show quantity inputs for selected items */
  showQuantityInputs?: boolean;
  /** Function to handle quantity changes */
  onQuantityChange?: (itemId: string, quantity: number) => void;
  /** Function to get quantity for an item */
  getQuantity?: (itemId: string) => number;
  /** Function to calculate total cost */
  calculateTotal?: (selectedItems: T[]) => number;
  /** Custom search section content */
  customSearchSection?: React.ReactNode;
  /** Custom table section content */
  customTableSection?: React.ReactNode;
}

/**
 * Sortable header component for table columns
 */
interface SortableHeaderProps<T> {
  children: React.ReactNode;
  columnKey: keyof T;
  onSort?: (key: keyof T) => void;
  sortConfig?: SortConfig<T>;
}

function SortableHeader<T>({
  children,
  columnKey,
  onSort,
  sortConfig
}: SortableHeaderProps<T>) {
  if (!onSort) {
    return <>{children}</>;
  }

  return (
    <Button
      variant="ghost"
      onClick={() => onSort(columnKey)}
      className="px-1 py-1 h-auto hover:bg-transparent group"
    >
      <span className="group-hover:opacity-100">{children}</span>
      <ArrowUpDown
        className={cn(
          "ml-2 h-4 w-4 opacity-50 group-hover:opacity-100 transition-opacity",
          {
            "opacity-100": sortConfig?.key === columnKey,
            "rotate-180": sortConfig?.key === columnKey && sortConfig.direction === 'desc'
          }
        )}
      />
    </Button>
  );
}

/**
 * Generic Dialog Component
 *
 * A reusable dialog component that provides common functionality for data selection,
 * filtering, sorting, and pagination. Designed to eliminate code duplication across
 * similar dialog components in the application.
 *
 * @template T - The type of data items, must extend { id: string }
 *
 * @example
 * ```tsx
 * // Basic usage with tire data
 * <GenericDialog
 *   isOpen={isOpen}
 *   onClose={onClose}
 *   title="Select Tires"
 *   data={tires}
 *   columns={tireColumns}
 *   multiSelect={true}
 *   selectedIds={selectedTireIds}
 *   onSelectionChange={setSelectedTireIds}
 *   onAddSelected={handleAddSelected}
 * />
 * ```
 *
 * @example
 * ```tsx
 * // Advanced usage with filters and pagination
 * <GenericDialog
 *   isOpen={isOpen}
 *   onClose={onClose}
 *   title="Processing Selection"
 *   data={processing}
 *   columns={processingColumns}
 *   filters={processingFilters}
 *   filterValues={filterValues}
 *   onFilterChange={handleFilterChange}
 *   onSearch={handleSearch}
 *   pagination={paginationConfig}
 *   onPageChange={handlePageChange}
 *   showQuantityInputs={true}
 *   calculateTotal={calculateTotalCost}
 * />
 * ```
 */
export function GenericDialog<T extends { id: string }>({
  isOpen,
  onClose,
  title,
  data,
  columns,
  filters = [],
  filterValues = {},
  onFilterChange,
  onSearch,
  onResetFilters,
  showFilters = true,
  multiSelect = false,
  selectedIds = new Set(),
  onSelectionChange,
  onAddSelected,
  onAddAll,
  pagination,
  onPageChange,
  sortConfig,
  onSortChange,
  loading = false,
  customFooter,
  className,
  maxWidth = "max-w-6xl",
  maxHeight = "max-h-[90vh]",
  showQuantityInputs = false,
  onQuantityChange,
  getQuantity,
  calculateTotal,
  customSearchSection,
  customTableSection,
}: GenericDialogProps<T>) {
  const { toast } = useToast();
  const [internalShowFilters, setInternalShowFilters] = React.useState(false);

  // Handle selection toggle for individual items
  const handleItemToggle = React.useCallback((itemId: string) => {
    if (!onSelectionChange) return;

    const newSelected = new Set(selectedIds);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    onSelectionChange(newSelected);
  }, [selectedIds, onSelectionChange]);

  // Handle select all toggle
  const handleSelectAll = React.useCallback(() => {
    if (!onSelectionChange) return;

    if (selectedIds.size === data.length && data.length > 0) {
      onSelectionChange(new Set());
    } else {
      onSelectionChange(new Set(data.map(item => item.id)));
    }
  }, [data, selectedIds, onSelectionChange]);

  // Handle add selected items
  const handleAddSelected = React.useCallback(() => {
    if (!onAddSelected) return;

    const selectedItems = data.filter(item => selectedIds.has(item.id));
    if (selectedItems.length === 0) {
      toast({
        title: "No Items Selected",
        description: "Please select items to add.",
        variant: "destructive",
      });
      return;
    }

    onAddSelected(selectedItems);
    toast({
      title: "Items Added",
      description: `${selectedItems.length} items added successfully.`,
    });
  }, [data, selectedIds, onAddSelected, toast]);

  // Handle add all items
  const handleAddAll = React.useCallback(() => {
    if (!onAddAll) return;

    if (data.length === 0) {
      toast({
        title: "No Items to Add",
        description: "The current result is empty.",
        variant: "destructive",
      });
      return;
    }

    onAddAll(data);
    toast({
      title: `Added All ${data.length} Items`,
      description: "All currently displayed items have been added.",
    });
  }, [data, onAddAll, toast]);

  // Handle filter input changes
  const handleFilterChange = React.useCallback((key: string, value: any) => {
    if (onFilterChange) {
      onFilterChange(key, value);
    }
  }, [onFilterChange]);

  // Handle search action
  const handleSearch = React.useCallback(() => {
    if (onSearch) {
      onSearch();
    }
  }, [onSearch]);

  // Handle filter reset
  const handleResetFilters = React.useCallback(() => {
    if (onResetFilters) {
      onResetFilters();
    }
    if (onSelectionChange) {
      onSelectionChange(new Set());
    }
  }, [onResetFilters, onSelectionChange]);

  // Calculate total if function provided
  const totalCost = React.useMemo(() => {
    if (!calculateTotal) return null;
    const selectedItems = data.filter(item => selectedIds.has(item.id));
    return calculateTotal(selectedItems);
  }, [data, selectedIds, calculateTotal]);

  // Check if all displayed items are selected
  const areAllSelected = data.length > 0 && selectedIds.size === data.length;

  // Render filter input based on type
  const renderFilterInput = (filter: FilterConfig<T>) => {
    const value = filterValues[filter.key as string];

    switch (filter.type) {
      case 'text':
        return (
          <Input
            id={`filter-${String(filter.key)}`}
            placeholder={filter.placeholder}
            value={value || ""}
            onChange={(e) => handleFilterChange(String(filter.key), e.target.value)}
          />
        );

      case 'number':
        return (
          <Input
            id={`filter-${String(filter.key)}`}
            type="number"
            placeholder={filter.placeholder}
            value={value || ""}
            onChange={(e) => handleFilterChange(String(filter.key), parseFloat(e.target.value) || undefined)}
          />
        );

      case 'select':
        return (
          <Select
            value={value || "all"}
            onValueChange={(val) => handleFilterChange(String(filter.key), val === "all" ? "" : val)}
          >
            <SelectTrigger id={`filter-${String(filter.key)}`}>
              <SelectValue placeholder={filter.placeholder} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {filter.options?.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2 pt-6">
            <Checkbox
              id={`filter-${String(filter.key)}`}
              checked={!!value}
              onCheckedChange={(checked) => handleFilterChange(String(filter.key), !!checked)}
            />
            <Label htmlFor={`filter-${String(filter.key)}`}>{filter.label}</Label>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
      <DialogContent className={cn("overflow-hidden flex flex-col", maxWidth, maxHeight, className)}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col space-y-4 flex-1 overflow-hidden">
          {/* Custom Search Section or Default Filters */}
          {customSearchSection || (showFilters && filters.length > 0 && (
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">Search Filters</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setInternalShowFilters(!internalShowFilters)}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {internalShowFilters ? "Hide" : "Show"} Filters
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleResetFilters}>
                    <X className="h-4 w-4 mr-2" />
                    Clear
                  </Button>
                  <Button onClick={handleSearch} disabled={loading}>
                    <Search className="h-4 w-4 mr-2" />
                    {loading ? "Searching..." : "Search"}
                  </Button>
                </div>
              </div>

              {internalShowFilters && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {filters.map((filter) => (
                    <div
                      key={String(filter.key)}
                      className={cn("space-y-2", filter.colSpan && `col-span-${filter.colSpan}`)}
                    >
                      {filter.type !== 'checkbox' && (
                        <Label htmlFor={`filter-${String(filter.key)}`}>
                          {filter.label}
                        </Label>
                      )}
                      {renderFilterInput(filter)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}

          {/* Custom Table Section or Default Table */}
          {customTableSection || (
            <div className="flex-1 overflow-hidden border rounded-lg">
              <div className="p-4 border-b bg-muted/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-muted-foreground">
                      {data.length} items found
                    </span>
                    {multiSelect && (
                      <span className="text-sm text-muted-foreground">
                        {selectedIds.size} selected
                      </span>
                    )}
                  </div>
                  {multiSelect && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAll}
                      disabled={data.length === 0}
                    >
                      {areAllSelected ? "Deselect All" : "Select All"}
                    </Button>
                  )}
                </div>
              </div>

              <div className="overflow-auto max-h-96">
                {loading ? (
                  <div className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                    <p className="mt-2">Loading...</p>
                  </div>
                ) : data.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No items found
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {multiSelect && (
                          <TableHead className="w-12">
                            <Checkbox
                              checked={areAllSelected}
                              onCheckedChange={handleSelectAll}
                            />
                          </TableHead>
                        )}
                        {columns.map((column) => (
                          <TableHead
                            key={String(column.key)}
                            className={column.className}
                            style={column.width ? { width: column.width } : undefined}
                          >
                            {column.sortable ? (
                              <SortableHeader
                                columnKey={column.key}
                                onSort={onSortChange}
                                sortConfig={sortConfig}
                              >
                                {column.label}
                              </SortableHeader>
                            ) : (
                              column.label
                            )}
                          </TableHead>
                        ))}
                        {showQuantityInputs && <TableHead className="w-20">Quantity</TableHead>}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.map((item) => (
                        <TableRow
                          key={item.id}
                          className={selectedIds.has(item.id) ? "bg-muted/50" : ""}
                        >
                          {multiSelect && (
                            <TableCell>
                              <Checkbox
                                checked={selectedIds.has(item.id)}
                                onCheckedChange={() => handleItemToggle(item.id)}
                              />
                            </TableCell>
                          )}
                          {columns.map((column) => (
                            <TableCell key={String(column.key)} className={column.className}>
                              {column.render
                                ? column.render(item[column.key], item)
                                : String(item[column.key] || "")
                              }
                            </TableCell>
                          ))}
                          {showQuantityInputs && (
                            <TableCell>
                              <Input
                                type="number"
                                min="1"
                                max="999"
                                value={getQuantity ? getQuantity(item.id) : 1}
                                onChange={(e) => onQuantityChange?.(item.id, parseInt(e.target.value) || 1)}
                                className="w-16 h-8 text-center"
                                disabled={!selectedIds.has(item.id)}
                              />
                            </TableCell>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex justify-center">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                  className="h-8 w-8 p-0"
                >
                  &lt;
                </Button>

                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                  .filter(page =>
                    page === 1 ||
                    page === pagination.totalPages ||
                    (page >= pagination.currentPage - 1 && page <= pagination.currentPage + 1)
                  )
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="flex items-center justify-center">...</span>
                      )}
                      <Button
                        variant={pagination.currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => onPageChange?.(page)}
                        className="h-8 w-8 p-0"
                      >
                        {page}
                      </Button>
                    </React.Fragment>
                  ))}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                  className="h-8 w-8 p-0"
                >
                  &gt;
                </Button>
              </div>
            </div>
          )}

          {/* Total Cost Display */}
          {totalCost !== null && selectedIds.size > 0 && (
            <div className="flex justify-between items-center pt-4 border-t">
              <div className="flex items-center space-x-2 bg-primary/10 px-4 py-2 rounded-lg">
                <Calculator className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">
                  Total Cost: <span className="text-primary font-bold">€{totalCost.toFixed(2)}</span>
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <DialogFooter className="flex-shrink-0">
          {customFooter || (
            <div className="flex justify-between items-center w-full">
              <div />
              <div className="flex space-x-2">
                {onAddSelected && (
                  <Button
                    variant="outline"
                    onClick={handleAddSelected}
                    disabled={selectedIds.size === 0}
                  >
                    Add Selected ({selectedIds.size})
                  </Button>
                )}
                {onAddAll && (
                  <Button
                    variant="outline"
                    onClick={handleAddAll}
                    disabled={data.length === 0}
                  >
                    Add All ({data.length})
                  </Button>
                )}
                <DialogClose asChild>
                  <Button variant="default">Close</Button>
                </DialogClose>
              </div>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}