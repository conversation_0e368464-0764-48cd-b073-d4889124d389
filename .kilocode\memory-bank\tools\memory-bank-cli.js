#!/usr/bin/env node

/**
 * Memory Bank CLI Tool for CutRequestStudio
 * Command-line interface for managing and querying the memory bank
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class MemoryBankCLI {
  constructor() {
    this.memoryBankPath = path.join(process.cwd(), '.kilocode', 'memory-bank');
    this.commands = {
      'extract': this.extractKnowledge.bind(this),
      'validate': this.validateKnowledge.bind(this),
      'search': this.searchKnowledge.bind(this),
      'stats': this.showStats.bind(this),
      'init': this.initializeMemoryBank.bind(this),
      'help': this.showHelp.bind(this),
      'interactive': this.startInteractiveMode.bind(this),
      'export': this.exportKnowledge.bind(this),
      'update': this.updateIndexes.bind(this)
    };
  }

  /**
   * Main CLI entry point
   */
  async run() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
      this.showHelp();
      return;
    }

    const command = args[0];
    const commandArgs = args.slice(1);

    if (this.commands[command]) {
      try {
        await this.commands[command](commandArgs);
      } catch (error) {
        console.error(`❌ Error executing command '${command}':`, error.message);
        process.exit(1);
      }
    } else {
      console.error(`❌ Unknown command: ${command}`);
      this.showHelp();
      process.exit(1);
    }
  }

  /**
   * Extract knowledge from the codebase
   */
  async extractKnowledge(args) {
    console.log('🔍 Starting knowledge extraction...');

    const KnowledgeExtractor = require('./extractors/knowledge-extractor');
    const extractor = new KnowledgeExtractor();

    const categories = args.length > 0 ? args : ['all'];

    if (categories.includes('all')) {
      await extractor.extractAll();
    } else {
      for (const category of categories) {
        await this.extractCategory(extractor, category);
      }
    }

    console.log('✅ Knowledge extraction completed');
  }

  /**
   * Extract specific category
   */
  async extractCategory(extractor, category) {
    switch (category) {
      case 'technical':
        await extractor.extractTechnicalKnowledge();
        break;
      case 'development':
        await extractor.extractDevelopmentHistory();
        break;
      case 'business':
        await extractor.extractBusinessDomain();
        break;
      case 'operational':
        await extractor.extractOperationalKnowledge();
        break;
      default:
        console.warn(`⚠️  Unknown category: ${category}`);
    }
  }

  /**
   * Validate knowledge quality
   */
  async validateKnowledge(args) {
    console.log('🔍 Starting knowledge validation...');

    const PatternValidator = require('./validators/pattern-validator');
    const validator = new PatternValidator(this.memoryBankPath);

    const report = await validator.validateAll();

    console.log('\n📊 Validation Summary:');
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`⚠️  Warnings: ${report.summary.warnings}`);
    console.log(`📈 Success Rate: ${report.summary.successRate.toFixed(1)}%`);

    if (report.summary.failed > 0) {
      console.log('\n❌ Failed Validations:');
      report.results.failed.forEach(failure => {
        console.log(`  - ${failure.category}/${failure.subcategory}: ${failure.issue}`);
      });
    }

    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`  - [${rec.priority.toUpperCase()}] ${rec.recommendation}`);
      });
    }
  }

  /**
   * Search knowledge base
   */
  async searchKnowledge(args) {
    if (args.length === 0) {
      console.error('❌ Please provide a search query');
      return;
    }

    const query = args.join(' ');
    console.log(`🔍 Searching for: "${query}"`);

    const results = await this.performSearch(query);

    if (results.length === 0) {
      console.log('📭 No results found');
      return;
    }

    console.log(`\n📋 Found ${results.length} results:\n`);

    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.title}`);
      console.log(`   📁 ${result.category}/${result.subcategory}`);
      console.log(`   📄 ${result.file}`);
      console.log(`   📝 ${result.description}`);
      console.log('');
    });
  }

  /**
   * Perform search across knowledge base
   */
  async performSearch(query) {
    const results = [];
    const searchTerms = query.toLowerCase().split(' ');

    // Search through all knowledge files
    await this.searchDirectory(this.memoryBankPath, searchTerms, results);

    // Sort by relevance (simple scoring based on term matches)
    results.sort((a, b) => b.score - a.score);

    return results.slice(0, 10); // Return top 10 results
  }

  /**
   * Recursively search directory for knowledge
   */
  async searchDirectory(dirPath, searchTerms, results, category = '', subcategory = '') {
    if (!fs.existsSync(dirPath)) return;

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        const newCategory = category || item;
        const newSubcategory = category ? (subcategory || item) : subcategory;
        await this.searchDirectory(itemPath, searchTerms, results, newCategory, newSubcategory);
      } else if (item.endsWith('.md') || item.endsWith('.json')) {
        const content = fs.readFileSync(itemPath, 'utf8');
        const score = this.calculateSearchScore(content, searchTerms);

        if (score > 0) {
          results.push({
            title: this.extractTitle(content, item),
            category: category,
            subcategory: subcategory,
            file: path.relative(this.memoryBankPath, itemPath),
            description: this.extractDescription(content),
            score: score
          });
        }
      }
    }
  }

  /**
   * Calculate search relevance score
   */
  calculateSearchScore(content, searchTerms) {
    const lowerContent = content.toLowerCase();
    let score = 0;

    searchTerms.forEach(term => {
      const matches = (lowerContent.match(new RegExp(term, 'g')) || []).length;
      score += matches;

      // Boost score for title matches
      if (lowerContent.includes(`# ${term}`) || lowerContent.includes(`## ${term}`)) {
        score += 5;
      }
    });

    return score;
  }

  /**
   * Extract title from content
   */
  extractTitle(content, filename) {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1] : filename.replace(/\.[^/.]+$/, '');
  }

  /**
   * Extract description from content
   */
  extractDescription(content) {
    // Look for overview or description section
    const overviewMatch = content.match(/## Overview\s*\n\n(.+?)(?=\n\n|\n#|$)/s);
    if (overviewMatch) {
      return overviewMatch[1].trim().substring(0, 150) + '...';
    }

    // Fallback to first paragraph
    const firstParagraph = content.split('\n\n')[1];
    return firstParagraph ? firstParagraph.trim().substring(0, 150) + '...' : 'No description available';
  }

  /**
   * Show memory bank statistics
   */
  async showStats(args) {
    console.log('📊 Memory Bank Statistics\n');

    const stats = await this.calculateStats();

    console.log('📁 Knowledge Categories:');
    Object.entries(stats.categories).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} documents`);
    });

    console.log(`\n📄 Total Documents: ${stats.totalDocuments}`);
    console.log(`📦 Total Size: ${this.formatBytes(stats.totalSize)}`);
    console.log(`🔗 Cross-references: ${stats.crossReferences}`);
    console.log(`🔍 Search Index Entries: ${stats.searchIndexEntries}`);

    if (stats.lastUpdate) {
      console.log(`🕒 Last Updated: ${new Date(stats.lastUpdate).toLocaleString()}`);
    }
  }

  /**
   * Calculate memory bank statistics
   */
  async calculateStats() {
    const stats = {
      categories: {},
      totalDocuments: 0,
      totalSize: 0,
      crossReferences: 0,
      searchIndexEntries: 0,
      lastUpdate: null
    };

    // Read metadata
    const metadataPath = path.join(this.memoryBankPath, 'index', 'metadata.json');
    if (fs.existsSync(metadataPath)) {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      stats.lastUpdate = metadata.memoryBank.lastUpdated;
    }

    // Count documents by category
    await this.countDocuments(this.memoryBankPath, stats);

    // Count cross-references
    const crossRefPath = path.join(this.memoryBankPath, 'index', 'cross-references.json');
    if (fs.existsSync(crossRefPath)) {
      const crossRefs = JSON.parse(fs.readFileSync(crossRefPath, 'utf8'));
      stats.crossReferences = Object.values(crossRefs.crossReferences.relationships).flat().length;
    }

    return stats;
  }

  /**
   * Count documents recursively
   */
  async countDocuments(dirPath, stats, category = '') {
    if (!fs.existsSync(dirPath)) return;

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory() && item !== 'index' && item !== 'tools') {
        const newCategory = category || item;
        await this.countDocuments(itemPath, stats, newCategory);
      } else if (item.endsWith('.md') || item.endsWith('.json')) {
        if (category) {
          stats.categories[category] = (stats.categories[category] || 0) + 1;
          stats.totalDocuments++;
          stats.totalSize += stat.size;
        }
      }
    }
  }

  /**
   * Initialize memory bank structure
   */
  async initializeMemoryBank(args) {
    console.log('🚀 Initializing memory bank structure...');

    const directories = [
      'technical/architecture/patterns',
      'technical/architecture/components',
      'technical/architecture/services',
      'technical/architecture/integrations',
      'technical/api/schemas',
      'technical/api/endpoints',
      'technical/api/types',
      'technical/api/validation',
      'technical/frontend/components',
      'technical/frontend/hooks',
      'technical/frontend/contexts',
      'technical/frontend/utilities',
      'technical/backend/models',
      'technical/backend/crud',
      'technical/backend/routers',
      'technical/backend/middleware',
      'technical/performance/optimizations',
      'technical/performance/monitoring',
      'technical/performance/caching',
      'technical/performance/benchmarks',
      'development/history/priority-1',
      'development/history/priority-2',
      'development/history/priority-3',
      'development/history/timeline',
      'development/decisions/architecture',
      'development/decisions/technology',
      'development/decisions/patterns',
      'development/decisions/trade-offs',
      'development/migrations/code-refactoring',
      'development/migrations/database',
      'development/migrations/api-changes',
      'development/migrations/deployment',
      'development/lessons-learned/successes',
      'development/lessons-learned/challenges',
      'development/lessons-learned/improvements',
      'development/lessons-learned/best-practices',
      'business/domain/tire-processing',
      'business/domain/request-management',
      'business/domain/quality-control',
      'business/domain/reporting',
      'business/workflows/user-journeys',
      'business/workflows/approval-processes',
      'business/workflows/data-flows',
      'business/workflows/integrations',
      'business/rules/validation',
      'business/rules/business-logic',
      'business/rules/permissions',
      'business/rules/constraints',
      'business/standards/industry',
      'business/standards/compliance',
      'business/standards/quality',
      'business/standards/security',
      'operational/development/setup',
      'operational/development/guidelines',
      'operational/development/tools',
      'operational/development/workflows',
      'operational/deployment/environments',
      'operational/deployment/procedures',
      'operational/deployment/rollback',
      'operational/deployment/monitoring',
      'operational/maintenance/updates',
      'operational/maintenance/backups',
      'operational/maintenance/performance',
      'operational/maintenance/security',
      'operational/troubleshooting/common-issues',
      'operational/troubleshooting/debugging',
      'operational/troubleshooting/performance',
      'operational/troubleshooting/recovery'
    ];

    for (const dir of directories) {
      const fullPath = path.join(this.memoryBankPath, dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`📁 Created: ${dir}`);
      }
    }

    console.log('✅ Memory bank structure initialized');
  }

  /**
   * Start interactive mode
   */
  async startInteractiveMode(args) {
    console.log('🎯 Memory Bank Interactive Mode');
    console.log('Type "help" for available commands, "exit" to quit\n');

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: 'memory-bank> '
    });

    rl.prompt();

    rl.on('line', async (line) => {
      const input = line.trim();

      if (input === 'exit') {
        console.log('👋 Goodbye!');
        rl.close();
        return;
      }

      if (input === '') {
        rl.prompt();
        return;
      }

      const args = input.split(' ');
      const command = args[0];
      const commandArgs = args.slice(1);

      if (this.commands[command] && command !== 'interactive') {
        try {
          await this.commands[command](commandArgs);
        } catch (error) {
          console.error(`❌ Error: ${error.message}`);
        }
      } else {
        console.log(`❌ Unknown command: ${command}`);
        this.showHelp();
      }

      rl.prompt();
    });

    rl.on('close', () => {
      process.exit(0);
    });
  }

  /**
   * Export knowledge to different formats
   */
  async exportKnowledge(args) {
    const format = args[0] || 'json';
    const outputPath = args[1] || `memory-bank-export.${format}`;

    console.log(`📤 Exporting memory bank to ${format.toUpperCase()} format...`);

    const knowledge = await this.collectAllKnowledge();

    switch (format.toLowerCase()) {
      case 'json':
        fs.writeFileSync(outputPath, JSON.stringify(knowledge, null, 2));
        break;
      case 'markdown':
        await this.exportToMarkdown(knowledge, outputPath);
        break;
      case 'html':
        await this.exportToHtml(knowledge, outputPath);
        break;
      default:
        console.error(`❌ Unsupported format: ${format}`);
        return;
    }

    console.log(`✅ Export completed: ${outputPath}`);
  }

  /**
   * Update search and cross-reference indexes
   */
  async updateIndexes(args) {
    console.log('🔄 Updating memory bank indexes...');

    // Update metadata
    const metadataPath = path.join(this.memoryBankPath, 'index', 'metadata.json');
    if (fs.existsSync(metadataPath)) {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      metadata.memoryBank.lastUpdated = new Date().toISOString();

      const stats = await this.calculateStats();
      metadata.memoryBank.statistics.totalDocuments = stats.totalDocuments;

      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
      console.log('📊 Metadata updated');
    }

    // Update search index
    await this.rebuildSearchIndex();
    console.log('🔍 Search index updated');

    // Update knowledge graph
    await this.rebuildKnowledgeGraph();
    console.log('🕸️ Knowledge graph updated');

    console.log('✅ All indexes updated successfully');
  }

  /**
   * Rebuild search index
   */
  async rebuildSearchIndex() {
    const searchIndexPath = path.join(this.memoryBankPath, 'index', 'search-index.json');
    const searchIndex = JSON.parse(fs.readFileSync(searchIndexPath, 'utf8'));

    // Clear existing index
    searchIndex.searchIndex.documents = {};
    searchIndex.searchIndex.invertedIndex = {};

    // Rebuild index from all documents
    await this.indexDocuments(this.memoryBankPath, searchIndex);

    // Update statistics
    searchIndex.searchIndex.statistics.totalDocuments = Object.keys(searchIndex.searchIndex.documents).length;
    searchIndex.searchIndex.statistics.lastIndexTime = new Date().toISOString();

    fs.writeFileSync(searchIndexPath, JSON.stringify(searchIndex, null, 2));
  }

  /**
   * Rebuild knowledge graph
   */
  async rebuildKnowledgeGraph() {
    const graphPath = path.join(this.memoryBankPath, 'index', 'knowledge-graph.json');
    const graph = JSON.parse(fs.readFileSync(graphPath, 'utf8'));

    // Clear existing graph
    graph.knowledgeGraph.nodes = {};
    graph.knowledgeGraph.edges = {};

    // Rebuild graph from documents
    await this.buildKnowledgeGraph(this.memoryBankPath, graph);

    // Update statistics
    graph.knowledgeGraph.statistics.totalNodes = Object.keys(graph.knowledgeGraph.nodes).length;
    graph.knowledgeGraph.statistics.totalEdges = Object.keys(graph.knowledgeGraph.edges).length;

    fs.writeFileSync(graphPath, JSON.stringify(graph, null, 2));
  }

  /**
   * Show help information
   */
  showHelp() {
    console.log(`
🧠 Memory Bank CLI - CutRequestStudio Knowledge Management

Usage: memory-bank <command> [options]

Commands:
  extract [category]     Extract knowledge from codebase
                        Categories: technical, development, business, operational, all

  validate              Validate knowledge quality and structure

  search <query>        Search knowledge base for specific content

  stats                 Show memory bank statistics and metrics

  init                  Initialize memory bank directory structure

  interactive           Start interactive mode for multiple commands

  export <format> [file] Export knowledge base
                        Formats: json, markdown, html

  update                Update search indexes and cross-references

  help                  Show this help message

Examples:
  memory-bank extract technical
  memory-bank search "CRUD service"
  memory-bank validate
  memory-bank stats
  memory-bank export markdown knowledge-export.md
  memory-bank interactive

For more information, visit: https://github.com/your-org/CutRequestStudio
`);
  }

  /**
   * Utility methods
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async collectAllKnowledge() {
    // Implementation for collecting all knowledge
    return {};
  }

  async exportToMarkdown(knowledge, outputPath) {
    // Implementation for markdown export
  }

  async exportToHtml(knowledge, outputPath) {
    // Implementation for HTML export
  }

  async indexDocuments(dirPath, searchIndex) {
    // Implementation for document indexing
  }

  async buildKnowledgeGraph(dirPath, graph) {
    // Implementation for knowledge graph building
  }
}

// CLI execution
if (require.main === module) {
  const cli = new MemoryBankCLI();
  cli.run().catch(error => {
    console.error('❌ CLI Error:', error.message);
    process.exit(1);
  });
}

module.exports = MemoryBankCLI;