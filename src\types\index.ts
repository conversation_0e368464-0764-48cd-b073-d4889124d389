

// --- Enhanced Request Types with simplified structure ---
export interface SimplifiedAppRequest {
  id: string;
  requestBy: string;
  projectNo: string;
  pidProject?: string;
  tireSize: string;
  requestDate: Date;
  targetDate: Date;
  status: string;
  type: string;
  totalN?: number;
  destination?: string;
  internal?: boolean;
  wishDate?: Date | null;
  numPneusSet?: number;
  numPneusSetUnit?: "SET" | "PCS";
  inChargeOf?: string;
  note?: string;
  attachments?: AttachmentFile[];
  requestItems?: RequestItem[]; // New simplified structure
}

// Legacy AppRequest interface for backward compatibility
export interface AppRequest {
  id: string;
  requestBy: string;
  projectNo: string;
  pidProject?: string;
  tireSize: string;
  requestDate: Date;
  targetDate: Date;
  status: string;
  type: string;
  totalN?: number;
  destination?: string;
  internal?: boolean;
  wishDate?: Date | null;
  numPneusSet?: number;
  numPneusSetUnit?: "SET" | "PCS";
  inChargeOf?: string;
  note?: string;
  attachments?: AttachmentFile[]; // Added
  request_details?: Tire[]; // Legacy structure
  requestItems?: RequestItem[]; // Optional new structure for transition
}

export interface RequestFormData {
  id?: string;
  requestBy?: string;
  projectNo?: string;
  pidProject?: string;
  tireSize?: string;
  requestDate?: Date | null;
  targetDate?: Date | null;
  status?: string;
  type?: string;
  totalN?: number | undefined;
  destination?: string;
  internal?: boolean;
  wishDate?: Date | null;
  numPneusSet?: number | undefined;
  numPneusSetUnit?: "SET" | "PCS";
  inChargeOf?: string;
  note?: string;
  attachments?: AttachmentFile[]; // Added
}

export const initialFormData: RequestFormData = {
  id: undefined,
  requestBy: "",
  projectNo: "",
  pidProject: "",
  tireSize: "",
  requestDate: undefined,
  targetDate: undefined,
  status: "DRAFT",
  type: "NEWDEV",
  totalN: undefined,
  destination: "PLANT_A",
  internal: false,
  wishDate: undefined,
  numPneusSet: undefined,
  numPneusSetUnit: "SET",
  inChargeOf: "",
  note: "",
  attachments: [], // Added
};

export interface Tire {
  id: string;
  tugNo: string;
  section: string;
  projectNo: string;
  specNo: string;
  tireSize: string;
  pattern: string;
  note?: string;
  disposition: "AVAILABLE" | "SCRAP" | "TESTED" | "REPAIR";
  quantity: number;
}

export interface TireFormData {
  id?: string;
  tugNo?: string;
  projectN?: string;
  tireSize?: string;
  note?: string;
  disposition?: "AVAILABLE" | "SCRAP" | "TESTED" | "REPAIR";
  quantity?: number | undefined;
  set?: string;
  direction?: number;
}

export const initialTireFormData: TireFormData = {
  id: undefined,
  tugNo: "",
  projectN: "",
  tireSize: "",
  note: "",
  disposition: "AVAILABLE",
  quantity: undefined,
  set: "",
  direction: 0,
};

// ============================================================================
// NEW SIMPLIFIED TYPES - MIGRATION TARGET
// ============================================================================

// --- Enhanced Tire Types (Master Catalog) ---
export interface EnhancedTire {
  id: string;
  tugNo: string;
  specNo: string;
  size: string;
  owner: string;
  loadIndex: string;
  pattern: string;
  projectNo: string;
  location: string;
  description?: string;
  isActive?: boolean;
}

export interface EnhancedTireFormData {
  id?: string;
  tugNo: string;
  specNo: string;
  size: string;
  owner: string;
  loadIndex: string;
  pattern: string;
  projectNo: string;
  location: string;
  description?: string;
  isActive?: boolean;
}

export const initialEnhancedTireFormData: EnhancedTireFormData = {
  tugNo: "",
  specNo: "",
  size: "",
  owner: "",
  loadIndex: "",
  pattern: "",
  projectNo: "",
  location: "",
  description: "",
  isActive: true,
};

// --- Request Item Types (Simplified replacement for RequestDetail) ---
export interface RequestItem {
  id: string;
  requestId: string;
  tireId: string;
  quantity: number;
  disposition: "AVAILABLE" | "SCRAP" | "TESTED" | "REPAIR" | "RESERVED" | "COMPLETED";
  notes?: string;
  unitPrice?: number;
  section?: string;
  tire?: EnhancedTire; // Include tire details when populated
}

export interface RequestItemFormData {
  id?: string;
  requestId: string;
  tireId: string;
  quantity: number;
  disposition: "AVAILABLE" | "SCRAP" | "TESTED" | "REPAIR" | "RESERVED" | "COMPLETED";
  notes?: string;
  unitPrice?: number;
  section?: string;
}

export const initialRequestItemFormData: RequestItemFormData = {
  requestId: "",
  tireId: "",
  quantity: 1,
  disposition: "AVAILABLE",
  notes: "",
  unitPrice: 0,
  section: "",
};

// --- Cut Operation Types (Simplified replacement for RequestDetailCut + CutProcessing) ---
export interface CutOperation {
  id: number;
  requestItemId: string;
  processingId: string;
  quantity: number;
  cutPrice?: number;
  status?: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
  notes?: string;
  createdDate?: string;
  processing?: DialogProcessing; // Include processing details when populated
  requestItem?: RequestItem; // Include request item details when populated
}

export interface CutOperationFormData {
  requestItemId: string;
  processingId: string;
  quantity: number;
  cutPrice?: number;
  status?: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
  notes?: string;
}

export const initialCutOperationFormData: CutOperationFormData = {
  requestItemId: "",
  processingId: "",
  quantity: 1,
  cutPrice: 0,
  status: "PENDING",
  notes: "",
};

// ============================================================================
// LEGACY TYPES - FOR BACKWARD COMPATIBILITY
// ============================================================================

export interface DialogTire {
  id: string;
  tugNo: string;
  specNo: string;
  size: string;
  owner: string;
  loadIndex: string;
  pattern: string;
  projectNo: string;
  location: string;
}

export interface DialogProcessing {
  id: string;
  tireType: string;
  description1: string;
  description2: string;
  testCode1: string;
  testCode2: string;
  cost: string;
}

export interface ProcessingSearchFiltersOld {
  tireType: string;
  picture: boolean;
  description1: string;
  description2: string;
  testCode1: string;
  testCode2: string;
}

export const initialProcessingSearchFiltersOld: ProcessingSearchFiltersOld = {
  tireType: "",
  picture: false,
  description1: "",
  description2: "",
  testCode1: "",
  testCode2: "",
};

export interface TireProcessingItem {
    id: string;
    description1: string;
    description2: string;
    price: string;
    tyreType: string;
    code1: string;
    code2: string;
    n: number;
    picture?: boolean;
}

export interface TireProcessingFormData {
    id?: string;
    description1?: string;
    description2?: string;
    tyreType?: string;
    n?: number | undefined;
    picture?: boolean;
}

export const initialTireProcessingFormData: TireProcessingFormData = {
    id: undefined,
    description1: "",
    description2: "",
    tyreType: "",
    n: undefined,
    picture: false,
};

// New type for attachments
export interface AttachmentFile {
  id: string;
  name: string;
  size: number; // in bytes
  type: string;
  uploadDate?: Date | string; // camelCase version
  upload_date?: string | Date; // snake_case version from backend
  status: "Uploaded" | "Pending";
  fileObject?: File; // Optional: store the actual file object for files pending upload or for download
  request_id?: string; // Add request_id to establish the relationship
}

// New types for CutProcessing
export interface CutProcessing {
  id: number;
  cutId: number;
  processingId: string;
  quantity: number;
  notes?: string;
  createdDate?: string;
  processing?: DialogProcessing; // Dati completi del processing
}

export interface CutProcessingFormData {
  cutId: number;
  processingId: string;
  quantity?: number;
  notes?: string;
}

// Updated RequestDetailCut with processing and enhanced fields
export interface RequestDetailCut {
  id: number;
  idRequestDetails?: string;
  set?: string; // Set specification (e.g., "1/4", "2/4")
  direction?: number; // Direction value
  notes?: string;
  cutPrice?: number;
  status?: string;
  cutProcessing?: CutProcessing[]; // Lista processing associati
  processingCount?: number; // Count of processing operations for UI display
}

export interface RequestDetailCutFormData {
  id?: number;
  idRequestDetails?: string;
  set?: string;
  direction?: number;
  notes?: string;
  cutPrice?: number;
  status?: string;
}

export const initialRequestDetailCutFormData: RequestDetailCutFormData = {
  id: undefined,
  idRequestDetails: undefined,
  set: "",
  direction: 0,
  notes: "",
  cutPrice: undefined,
  status: "PENDING",
};

// Quick select options for cut sets
export const CUT_SET_OPTIONS = [
  "1/4",
  "2/4",
  "3/4",
  "4/4",
  "1/2",
  "2/2",
  "1/3",
  "2/3",
  "3/3",
  "1/6",
  "2/6",
  "3/6",
  "4/6",
  "5/6",
  "6/6"
];

// Cut status options
export const CUT_STATUS_OPTIONS = [
  { value: "PENDING", label: "Pending" },
  { value: "IN_PROGRESS", label: "In Progress" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" }
];

// Processing search filters
export interface ProcessingSearchFilters {
  tireType?: string;
  description1?: string;
  description2?: string;
  testCode1?: string;
  testCode2?: string;
  minCost?: number;
  maxCost?: number;
  picture?: boolean;
}

export const initialProcessingSearchFilters: ProcessingSearchFilters = {
  tireType: "",
  description1: "",
  description2: "",
  testCode1: "",
  testCode2: "",
  minCost: undefined,
  maxCost: undefined,
  picture: undefined,
};
