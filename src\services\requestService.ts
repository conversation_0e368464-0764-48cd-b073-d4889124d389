import axiosInstance from "@/lib/axiosInstance";
import { RequestFormData, AppRequest, SimplifiedAppRequest, Tire, RequestItem, CutOperation } from "@/types";

// URL base uniforme per tutte le richieste
// const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000"; // Base URL is now in axiosInstance
const REQUEST_API_PATH = "/requests"; // Relative path

// Ottieni la lista delle richieste con parametri di query (paginazione, filtri, ordinamento)
export const getRequests = async (params?: Record<string, any>) => {
  try {
    const response = await axiosInstance.get<AppRequest[]>(REQUEST_API_PATH, { params });
    return response.data;
  } catch (error) {
    console.error("[requestService] Error fetching requests:", error);
    throw error;
  }
};

// Ottieni i dettagli di una richiesta specifica
export const getRequest = async (id: string) => {
  try {
    const response = await axiosInstance.get<RequestFormData>(`${REQUEST_API_PATH}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error fetching request with ID ${id}:`, error);
    throw error;
  }
};

// Ottieni i pneumatici associati a una richiesta
export const getRequestTires = async (id: string) => {
  try {
    const response = await axiosInstance.get<Tire[]>(`${REQUEST_API_PATH}/${id}/tires`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error fetching tires for request ID ${id}:`, error);
    throw error;
  }
};

// Crea una nuova richiesta
export const createRequest = async (data: RequestFormData) => {
  // Transform camelCase to snake_case for backend compatibility
  const transformedData = {
    id: data.id,
    request_by: data.requestBy,
    project_no: data.projectNo,
    pid_project: data.pidProject,
    tire_size: data.tireSize,
    request_date: data.requestDate ? new Date(data.requestDate).toISOString() : null,
    target_date: data.targetDate ? new Date(data.targetDate).toISOString() : null,
    status: data.status,
    type: data.type,
    total_n: data.totalN,
    destination: data.destination,
    internal: data.internal,
    wish_date: data.wishDate ? new Date(data.wishDate).toISOString() : null,
    num_pneus_set: data.numPneusSet,
    num_pneus_set_unit: data.numPneusSetUnit,
    in_charge_of: data.inChargeOf,
    note: data.note,
    attachments: data.attachments ? data.attachments.map(att => ({
      id: att.id,
      name: att.name,
      size: att.size,
      type: att.type,
      upload_date: att.uploadDate ? new Date(att.uploadDate).toISOString() :
                   att.upload_date ? new Date(att.upload_date).toISOString() : null,
      status: att.status,
      // Don't set request_id here as it will be assigned by the backend
    })) : []
  };

  try {
    const response = await axiosInstance.post<RequestFormData>(REQUEST_API_PATH, transformedData);
    return response.data;
  } catch (error) {
    console.error("[requestService] Error creating request:", error);
    throw error;
  }
};

// Aggiorna una richiesta esistente
export const updateRequest = async (id: string, data: RequestFormData) => {
  // Transform camelCase to snake_case for backend compatibility
  const transformedData = {
    id: data.id,
    request_by: data.requestBy,
    project_no: data.projectNo,
    pid_project: data.pidProject,
    tire_size: data.tireSize,
    request_date: data.requestDate ? new Date(data.requestDate).toISOString() : null,
    target_date: data.targetDate ? new Date(data.targetDate).toISOString() : null,
    status: data.status,
    type: data.type,
    total_n: data.totalN,
    destination: data.destination,
    internal: data.internal,
    wish_date: data.wishDate ? new Date(data.wishDate).toISOString() : null,
    num_pneus_set: data.numPneusSet,
    num_pneus_set_unit: data.numPneusSetUnit,
    in_charge_of: data.inChargeOf,
    note: data.note,
    attachments: data.attachments ? data.attachments.map(att => ({
      id: att.id,
      name: att.name,
      size: att.size,
      type: att.type,
      upload_date: att.uploadDate ? new Date(att.uploadDate).toISOString() :
                   att.upload_date ? new Date(att.upload_date).toISOString() : null,
      status: att.status,
      request_id: id // Ensure request_id is set to the current request
    })) : []
  };

  try {
    const response = await axiosInstance.put<RequestFormData>(`${REQUEST_API_PATH}/${id}`, transformedData);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error updating request with ID ${id}:`, error);
    throw error;
  }
};

// Elimina una richiesta
export const deleteRequest = async (id: string) => {
  try {
    await axiosInstance.delete(`${REQUEST_API_PATH}/${id}`);
  } catch (error) {
    console.error(`[requestService] Error deleting request with ID ${id}:`, error);
    throw error;
  }
};

// Invia una richiesta (POST /api/v1/requests/{id}/send)
export const sendRequest = async (id: string) => {
  try {
    const response = await axiosInstance.post(`${REQUEST_API_PATH}/${id}/send`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error sending request with ID ${id}:`, error);
    throw error;
  }
};


// Salva una richiesta (POST /requests/{id}/save)
export const saveRequest = async (id: string, data?: any) => {
  try {
    const response = await axiosInstance.post(`${REQUEST_API_PATH}/${id}/save`, data);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error saving request with ID ${id}:`, error);
    throw error;
  }
};

// Copy+Send una richiesta (POST /requests/{id}/copy-send)
export const copySendRequest = async (id: string) => {
  try {
    const response = await axiosInstance.post(`${REQUEST_API_PATH}/${id}/copy-send`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error copying and sending request with ID ${id}:`, error);
    throw error;
  }
};

// Elimina un dettaglio di richiesta (pneumatico)
export const deleteRequestDetail = async (detailId: string) => {
  try {
    await axiosInstance.delete(`${REQUEST_API_PATH}/details/${detailId}`);
  } catch (error) {
    console.error(`[requestService] Error deleting request detail with ID ${detailId}:`, error);
    throw error;
  }
};

// ============================================================================
// NEW SIMPLIFIED REQUEST SERVICE FUNCTIONS - MIGRATION TARGET
// ============================================================================

// Get a request using the new simplified structure
export const getRequestSimplified = async (id: string): Promise<SimplifiedAppRequest> => {
  try {
    const response = await axiosInstance.get<SimplifiedAppRequest>(`${REQUEST_API_PATH}/${id}/simplified`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error fetching simplified request with ID ${id}:`, error);
    throw error;
  }
};

// Get all request items for a specific request
export const getRequestItems = async (id: string): Promise<RequestItem[]> => {
  try {
    const response = await axiosInstance.get<RequestItem[]>(`${REQUEST_API_PATH}/${id}/items`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error fetching request items for request ${id}:`, error);
    throw error;
  }
};

// Get all cut operations for a specific request
export const getRequestCutOperations = async (id: string): Promise<CutOperation[]> => {
  try {
    const response = await axiosInstance.get<CutOperation[]>(`${REQUEST_API_PATH}/${id}/cut-operations`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error fetching cut operations for request ${id}:`, error);
    throw error;
  }
};

// Get a comprehensive summary of a request
export const getRequestSummary = async (id: string): Promise<{
  request: SimplifiedAppRequest;
  statistics: {
    total_items: number;
    total_cuts: number;
    total_cost: number;
    status_breakdown: Record<string, number>;
    processing_breakdown: Record<string, number>;
  };
  cut_operations: CutOperation[];
}> => {
  try {
    const response = await axiosInstance.get(`${REQUEST_API_PATH}/${id}/summary`);
    return response.data;
  } catch (error) {
    console.error(`[requestService] Error fetching request summary for ${id}:`, error);
    throw error;
  }
};

// Transform legacy request to simplified format
export const transformLegacyToSimplified = (legacyRequest: AppRequest): SimplifiedAppRequest => {
  return {
    id: legacyRequest.id,
    requestBy: legacyRequest.requestBy,
    projectNo: legacyRequest.projectNo,
    pidProject: legacyRequest.pidProject,
    tireSize: legacyRequest.tireSize,
    requestDate: legacyRequest.requestDate,
    targetDate: legacyRequest.targetDate,
    status: legacyRequest.status,
    type: legacyRequest.type,
    totalN: legacyRequest.totalN,
    destination: legacyRequest.destination,
    internal: legacyRequest.internal,
    wishDate: legacyRequest.wishDate,
    numPneusSet: legacyRequest.numPneusSet,
    numPneusSetUnit: legacyRequest.numPneusSetUnit,
    inChargeOf: legacyRequest.inChargeOf,
    note: legacyRequest.note,
    attachments: legacyRequest.attachments || [],
    requestItems: legacyRequest.requestItems || [],
  };
};

// Transform simplified request to legacy format for backward compatibility
export const transformSimplifiedToLegacy = (simplifiedRequest: SimplifiedAppRequest): AppRequest => {
  return {
    id: simplifiedRequest.id,
    requestBy: simplifiedRequest.requestBy,
    projectNo: simplifiedRequest.projectNo,
    pidProject: simplifiedRequest.pidProject,
    tireSize: simplifiedRequest.tireSize,
    requestDate: simplifiedRequest.requestDate,
    targetDate: simplifiedRequest.targetDate,
    status: simplifiedRequest.status,
    type: simplifiedRequest.type,
    totalN: simplifiedRequest.totalN,
    destination: simplifiedRequest.destination,
    internal: simplifiedRequest.internal,
    wishDate: simplifiedRequest.wishDate,
    numPneusSet: simplifiedRequest.numPneusSet,
    numPneusSetUnit: simplifiedRequest.numPneusSetUnit,
    inChargeOf: simplifiedRequest.inChargeOf,
    note: simplifiedRequest.note,
    attachments: simplifiedRequest.attachments || [],
    request_details: [], // Legacy structure - empty for simplified requests
    requestItems: simplifiedRequest.requestItems || [],
  };
};
