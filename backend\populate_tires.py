from backend.database import SessionLocal, Base, engine
from backend.models import Tire
import random
import string

# Create the table in the database if it doesn't exist
Base.metadata.create_all(bind=engine, tables=[Tire.__table__])

# Get a database session
db = SessionLocal()

# Sample data for variation
tire_sizes = [
    "205/55R16", "225/45R17", "205/45R18", "225/55R19", "205/45R16",
    "225/45R17", "205/55R18", "225/45R19", "245/40R18", "235/50R18",
    "215/60R16", "255/35R19", "195/65R15", "265/35R20", "185/65R15"
]

owners = ["Bridgestone", "Pirelli", "Michelin", "Continental", "Goodyear",
          "Competitor X", "Competitor Y", "Dunlop", "Hankook", "Yokohama"]

patterns = [
    "P-Zero", "Pilot Sport 4S", "Eagle F1", "Potenza S007", "Turanza T005",
    "Cinturato P7", "Scorpion Verde", "PremiumContact 6", "UltraContact UC7",
    "Assurance WeatherReady"
]

# Generate 50 tire records
for i in range(1, 51):
    # Format ID with leading zeros
    id_str = f"T{str(i).zfill(3)}"

    # Generate TUG number
    letter = random.choice(['A', 'B', 'C', 'D', 'E'])
    tug_no = f"TUG-{letter}{random.randint(1, 9)}"

    # Generate spec number with leading zeros
    spec_no = f"SP-{str(i).zfill(3)}"

    # Random tire size
    size = random.choice(tire_sizes)

    # Random owner
    owner = random.choice(owners)

    # Generate load index
    load_value = random.randint(88, 99)
    speed_rating = random.choice(['V', 'W', 'X', 'Y', 'Z'])
    load_index = f"{load_value}{speed_rating}"

    # Random pattern
    pattern = random.choice(patterns)

    # Generate project number
    project_no = f"PROJ-{str(i).zfill(3)}"

    # Generate location
    shelf_letter = random.choice(['A', 'B', 'C', 'D', 'E', 'F'])
    shelf_number = random.randint(1, 9)
    location = f"Shelf {shelf_letter}-{shelf_number}"

    # Create tire record using the ORM model
    tire = Tire(
        id=id_str,
        tug_no=tug_no,
        spec_no=spec_no,
        size=size,
        owner=owner,
        load_index=load_index,
        pattern=pattern,
        project_no=project_no,
        location=location
    )

    # Add to session
    db.add(tire)

# Commit the changes
db.commit()
db.close()

print("Successfully created and populated the tire table with 50 sample records.")
