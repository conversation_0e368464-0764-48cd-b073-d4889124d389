#!/usr/bin/env python3
"""
Test API endpoints directly without starting the server
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from backend.main import app

def test_api_endpoints():
    """Test all new simplified API endpoints"""
    print("🚀 Testing API Endpoints")
    print("=" * 50)
    
    client = TestClient(app)
    
    # Test health endpoint
    print("\n🏥 Testing Health Endpoint")
    response = client.get("/health")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   ✅ Health check: {response.json()}")
    else:
        print(f"   ❌ Health check failed: {response.text}")
    
    # Test enhanced tires endpoint
    print("\n🛞 Testing Enhanced Tires API")
    response = client.get("/api/v1/tires")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        tires = response.json()
        print(f"   ✅ Found {len(tires)} tires")
        if tires:
            print(f"   📋 Sample tire: {tires[0].get('tugNo', 'N/A')} - {tires[0].get('size', 'N/A')}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test request items endpoint
    print("\n📦 Testing Request Items API")
    response = client.get("/api/v1/request-items/request/REQ001")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        items = response.json()
        print(f"   ✅ Found {len(items)} request items")
        if items:
            print(f"   📋 Sample item: {items[0].get('id', 'N/A')} - Qty: {items[0].get('quantity', 'N/A')}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test cut operations endpoint
    print("\n✂️ Testing Cut Operations API")
    response = client.get("/api/v1/cut-operations/request/REQ001")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        operations = response.json()
        print(f"   ✅ Found {len(operations)} cut operations")
        if operations:
            print(f"   📋 Sample operation: ID {operations[0].get('id', 'N/A')} - Status: {operations[0].get('status', 'N/A')}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test simplified request endpoint
    print("\n📋 Testing Simplified Request API")
    response = client.get("/api/v1/requests/REQ001/simplified")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        request = response.json()
        print(f"   ✅ Request: {request.get('id', 'N/A')} - {request.get('projectNo', 'N/A')}")
        print(f"   📋 Items: {len(request.get('requestItems', []))}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test request summary endpoint
    print("\n📊 Testing Request Summary API")
    response = client.get("/api/v1/requests/REQ001/summary")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        summary = response.json()
        stats = summary.get('statistics', {})
        print(f"   ✅ Summary loaded")
        print(f"   📋 Total Items: {stats.get('total_items', 0)}")
        print(f"   ✂️ Total Cuts: {stats.get('total_cuts', 0)}")
        print(f"   💰 Total Cost: €{stats.get('total_cost', 0):.2f}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test tire statistics endpoint
    print("\n📊 Testing Tire Statistics API")
    response = client.get("/api/v1/tires/stats/summary")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"   ✅ Statistics loaded")
        print(f"   📋 Total Tires: {stats.get('total_tires', 0)}")
        print(f"   ✅ Active Tires: {stats.get('active_tires', 0)}")
        print(f"   ❌ Inactive Tires: {stats.get('inactive_tires', 0)}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    # Test cut operations statistics endpoint
    print("\n📊 Testing Cut Operations Statistics API")
    response = client.get("/api/v1/cut-operations/stats/summary")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"   ✅ Statistics loaded")
        print(f"   📋 Total Operations: {stats.get('total_operations', 0)}")
        financial = stats.get('financial', {})
        print(f"   💰 Total Cost: €{financial.get('total_cost', 0):.2f}")
        print(f"   💰 Average Cost: €{financial.get('average_cost', 0):.2f}")
    else:
        print(f"   ❌ Error: {response.text}")
    
    print("\n" + "=" * 50)
    print("✅ API endpoint testing completed!")

if __name__ == "__main__":
    test_api_endpoints()
