import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

/**
 * Validation error type
 */
export type ValidationErrors = Record<string, string | undefined>;

/**
 * Form field validation rule
 */
export interface ValidationRule<T> {
  /** Validation function that returns error message or undefined */
  validate: (value: any, formData: T) => string | undefined;
  /** Optional trigger - when to run this validation */
  trigger?: 'onChange' | 'onBlur' | 'onSubmit';
}

/**
 * Field configuration for form validation
 */
export interface FieldConfig<T> {
  /** Field is required */
  required?: boolean;
  /** Custom validation rules */
  rules?: ValidationRule<T>[];
  /** Custom error message for required validation */
  requiredMessage?: string;
}

/**
 * Configuration for the Universal Form Hook
 */
export interface UseUniversalFormConfig<T> {
  /** Initial form data */
  initialData: T;
  /** Field validation configuration */
  validationConfig?: Partial<Record<keyof T, FieldConfig<T>>>;
  /** Custom validation function for the entire form */
  customValidation?: (data: T) => ValidationErrors;
  /** Function called on successful form submission */
  onSubmit: (data: T) => Promise<void>;
  /** Optional data transformation before submission */
  transformData?: (data: T) => any;
  /** Reset form after successful submission */
  resetOnSuccess?: boolean;
  /** Show success toast after submission */
  showSuccessToast?: boolean;
  /** Custom success message */
  successMessage?: string;
  /** Show error toast on submission failure */
  showErrorToast?: boolean;
  /** Enable real-time validation */
  validateOnChange?: boolean;
}

/**
 * Return type for the Universal Form Hook
 */
export interface UseUniversalFormReturn<T> {
  /** Current form data */
  formData: T;
  /** Current validation errors */
  errors: ValidationErrors;
  /** Whether form is currently submitting */
  isSubmitting: boolean;
  /** Whether form has been touched */
  touched: Record<keyof T, boolean>;
  /** Whether form is valid */
  isValid: boolean;
  /** Handle field value change */
  handleChange: (field: keyof T, value: any) => void;
  /** Handle numeric input change with validation */
  handleNumericChange: (field: keyof T, value: string, min?: number, max?: number) => void;
  /** Handle form submission */
  handleSubmit: (e?: React.FormEvent) => Promise<void>;
  /** Set form data programmatically */
  setFormData: React.Dispatch<React.SetStateAction<T>>;
  /** Reset form to initial state */
  reset: () => void;
  /** Validate specific field */
  validateField: (field: keyof T) => void;
  /** Validate entire form */
  validateForm: () => boolean;
  /** Clear specific field error */
  clearFieldError: (field: keyof T) => void;
  /** Clear all errors */
  clearErrors: () => void;
  /** Set field as touched */
  setFieldTouched: (field: keyof T, touched?: boolean) => void;
}

/**
 * Universal Form Hook
 *
 * Consolidates form logic that was duplicated across 4 components:
 * - src/components/request-detail/tire-form.tsx:45
 * - src/components/tire-processing-detail/processing-edit-form.tsx:35
 * - src/components/page/request-details.tsx:52
 * - src/components/tire-management/tire-form.tsx:79
 *
 * Features:
 * - Type-safe form state management
 * - Flexible validation system
 * - Real-time validation
 * - Error handling with toast notifications
 * - Numeric input handling
 * - Form reset functionality
 * - Touch state tracking
 *
 * @template T - The form data type
 *
 * @example
 * ```typescript
 * interface UserFormData {
 *   name: string;
 *   email: string;
 *   age?: number;
 * }
 *
 * const {
 *   formData,
 *   errors,
 *   isSubmitting,
 *   handleChange,
 *   handleNumericChange,
 *   handleSubmit
 * } = useUniversalForm<UserFormData>({
 *   initialData: { name: '', email: '', age: undefined },
 *   validationConfig: {
 *     name: { required: true },
 *     email: {
 *       required: true,
 *       rules: [{
 *         validate: (value) =>
 *           /\S+@\S+\.\S+/.test(value) ? undefined : 'Invalid email format'
 *       }]
 *     },
 *     age: {
 *       rules: [{
 *         validate: (value) =>
 *           value && value < 0 ? 'Age must be positive' : undefined
 *       }]
 *     }
 *   },
 *   onSubmit: async (data) => {
 *     await userService.create(data);
 *   },
 *   showSuccessToast: true,
 *   successMessage: 'User created successfully!'
 * });
 * ```
 */
export function useUniversalForm<T extends Record<string, any>>({
  initialData,
  validationConfig = {} as Partial<Record<keyof T, FieldConfig<T>>>,
  customValidation,
  onSubmit,
  transformData,
  resetOnSuccess = false,
  showSuccessToast = true,
  successMessage = 'Operation completed successfully',
  showErrorToast = true,
  validateOnChange = true
}: UseUniversalFormConfig<T>): UseUniversalFormReturn<T> {

  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState<T>(initialData);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);

  // Computed values
  const isValid = Object.keys(errors).length === 0;

  /**
   * Validate a single field
   */
  const validateField = useCallback((field: keyof T): string | undefined => {
    const value = formData[field];
    const config = validationConfig[field];

    if (!config) return undefined;

    // Required validation
    if (config.required) {
      if (value === undefined || value === null || value === '') {
        return config.requiredMessage || `${String(field)} is required`;
      }
    }

    // Custom rules validation
    if (config.rules) {
      for (const rule of config.rules) {
        const error = rule.validate(value, formData);
        if (error) return error;
      }
    }

    return undefined;
  }, [formData, validationConfig]);

  /**
   * Validate entire form
   */
  const validateForm = useCallback((): boolean => {
    const newErrors: ValidationErrors = {};

    // Field-level validation
    Object.keys(validationConfig).forEach((field) => {
      const error = validateField(field as keyof T);
      if (error) {
        newErrors[field] = error;
      }
    });

    // Custom form-level validation
    if (customValidation) {
      const customErrors = customValidation(formData);
      Object.assign(newErrors, customErrors);
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, validationConfig, customValidation, validateField]);

  /**
   * Handle field value change
   */
  const handleChange = useCallback((field: keyof T, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Mark field as touched
    setTouched(prev => ({ ...prev, [field]: true }));

    // Real-time validation
    if (validateOnChange) {
      const error = validateField(field);
      setErrors(prev => ({ ...prev, [field as string]: error }));
    } else {
      // Clear error if field was previously invalid
      if (errors[field as string]) {
        setErrors(prev => ({ ...prev, [field as string]: undefined }));
      }
    }
  }, [validateField, validateOnChange, errors]);

  /**
   * Handle numeric input change with validation
   */
  const handleNumericChange = useCallback((
    field: keyof T,
    value: string,
    min: number = 0,
    max?: number
  ) => {
    if (value === '') {
      handleChange(field, undefined);
      return;
    }

    const numValue = parseInt(value, 10);

    // Validate numeric input
    if (!isNaN(numValue) && numValue >= min && (max === undefined || numValue <= max)) {
      handleChange(field, numValue);
    }
    // If invalid, don't update the value but could show error
  }, [handleChange]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    // Validate form before submission
    if (!validateForm()) {
      if (showErrorToast) {
        toast({
          title: "Validation Error",
          description: "Please fix the errors before submitting",
          variant: "destructive"
        });
      }
      return;
    }

    setIsSubmitting(true);

    try {
      // Transform data if needed
      const dataToSubmit = transformData ? transformData(formData) : formData;

      // Submit form
      await onSubmit(dataToSubmit);

      // Success handling
      if (showSuccessToast) {
        toast({
          title: "Success",
          description: successMessage
        });
      }

      // Reset form if requested
      if (resetOnSuccess) {
        reset();
      }

    } catch (error: any) {
      console.error('Form submission error:', error);

      if (showErrorToast) {
        toast({
          title: "Error",
          description: error.message || "An error occurred while submitting the form",
          variant: "destructive"
        });
      }

      throw error; // Re-throw for component-level handling if needed
    } finally {
      setIsSubmitting(false);
    }
  }, [
    formData,
    validateForm,
    transformData,
    onSubmit,
    showSuccessToast,
    successMessage,
    showErrorToast,
    resetOnSuccess,
    toast
  ]);

  /**
   * Reset form to initial state
   */
  const reset = useCallback(() => {
    setFormData(initialData);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
  }, [initialData]);

  /**
   * Clear specific field error
   */
  const clearFieldError = useCallback((field: keyof T) => {
    setErrors(prev => ({ ...prev, [field as string]: undefined }));
  }, []);

  /**
   * Clear all errors
   */
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  /**
   * Set field as touched
   */
  const setFieldTouched = useCallback((field: keyof T, isTouched: boolean = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }));
  }, []);

  // Effect to validate form when data changes (if validateOnChange is enabled)
  useEffect(() => {
    if (validateOnChange && Object.keys(touched).length > 0) {
      // Only validate touched fields to avoid showing errors immediately
      const newErrors: ValidationErrors = {};

      Object.keys(touched).forEach((field) => {
        if (touched[field as keyof T]) {
          const error = validateField(field as keyof T);
          if (error) {
            newErrors[field] = error;
          }
        }
      });

      setErrors(prev => ({ ...prev, ...newErrors }));
    }
  }, [formData, validateOnChange, touched, validateField]);

  return {
    formData,
    errors,
    isSubmitting,
    touched,
    isValid,
    handleChange,
    handleNumericChange,
    handleSubmit,
    setFormData,
    reset,
    validateField: (field: keyof T) => {
      const error = validateField(field);
      setErrors(prev => ({ ...prev, [field as string]: error }));
    },
    validateForm,
    clearFieldError,
    clearErrors,
    setFieldTouched
  };
}

/**
 * Common validation rules that can be reused across forms
 */
export const ValidationRules = {
  /**
   * Email validation rule
   */
  email: <T>(): ValidationRule<T> => ({
    validate: (value: string) => {
      if (!value) return undefined;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value) ? undefined : 'Please enter a valid email address';
    }
  }),

  /**
   * Minimum length validation rule
   */
  minLength: <T>(min: number): ValidationRule<T> => ({
    validate: (value: string) => {
      if (!value) return undefined;
      return value.length >= min ? undefined : `Must be at least ${min} characters`;
    }
  }),

  /**
   * Maximum length validation rule
   */
  maxLength: <T>(max: number): ValidationRule<T> => ({
    validate: (value: string) => {
      if (!value) return undefined;
      return value.length <= max ? undefined : `Must be no more than ${max} characters`;
    }
  }),

  /**
   * Positive number validation rule
   */
  positiveNumber: <T>(): ValidationRule<T> => ({
    validate: (value: number) => {
      if (value === undefined || value === null) return undefined;
      return value > 0 ? undefined : 'Must be a positive number';
    }
  }),

  /**
   * Number range validation rule
   */
  numberRange: <T>(min: number, max: number): ValidationRule<T> => ({
    validate: (value: number) => {
      if (value === undefined || value === null) return undefined;
      if (value < min || value > max) {
        return `Must be between ${min} and ${max}`;
      }
      return undefined;
    }
  }),

  /**
   * Date range validation rule
   */
  dateRange: <T>(minDate?: Date, maxDate?: Date): ValidationRule<T> => ({
    validate: (value: string | Date) => {
      if (!value) return undefined;
      const date = new Date(value);
      if (isNaN(date.getTime())) return 'Invalid date';

      if (minDate && date < minDate) {
        return `Date must be after ${minDate.toLocaleDateString()}`;
      }
      if (maxDate && date > maxDate) {
        return `Date must be before ${maxDate.toLocaleDateString()}`;
      }
      return undefined;
    }
  })
};