"use client";

import * as React from "react";
import { TireManagementDialog } from "@/components/tire-management/tire-management-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { DialogTire } from "@/types";

export default function TestTireDialogPage() {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleAddTires = (tires: DialogTire[]) => {
    console.log("Tires added:", tires);
    alert(`Added ${tires.length} tires`);
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Tire Management Dialog</h1>
      <Button onClick={() => setIsOpen(true)}>
        Open Tire Management Dialog
      </Button>

      <TireManagementDialog
        open={isOpen}
        onOpenChange={setIsOpen}
        onAddSelectedTires={handleAddTires}
      />
    </div>
  );
}
