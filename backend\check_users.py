import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import User

# Create database connection
SQLALCHEMY_DATABASE_URL = "sqlite:///./app.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Get database session
db = SessionLocal()

try:
    # Query all users
    users = db.query(User).all()

    print('Users in database:')
    for user in users:
        print(f'- Email: {user.email}')
        print(f'  Role: {user.role}')
        print(f'  Active: {user.is_active}')
        print(f'  Full Name: {user.full_name}')
        print()

    if not users:
        print('No users found in database')

    # Also check requests
    from models import Request
    requests = db.query(Request).all()
    print(f'\nRequests in database: {len(requests)}')
    for req in requests:
        print(f'- ID: {req.id}, Request By: {req.request_by}, Status: {req.status}')

finally:
    db.close()
