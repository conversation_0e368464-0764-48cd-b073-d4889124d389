"use client"

import * as React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Trash2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface DeleteConfirmationDialogProps {
  /**
   * Whether the dialog is open
   */
  isOpen: boolean
  /**
   * Function to call when the dialog should be closed
   */
  onClose: () => void
  /**
   * Function to call when the delete action is confirmed
   */
  onConfirm: () => void
  /**
   * The title of the dialog
   * @default "Confirm Deletion"
   */
  title?: string
  /**
   * The description of the dialog
   * @default "Are you sure you want to delete this item? This action cannot be undone."
   */
  description?: string
  /**
   * The text for the cancel button
   * @default "Cancel"
   */
  cancelText?: string
  /**
   * The text for the confirm button
   * @default "Delete"
   */
  confirmText?: string
  /**
   * The type of item being deleted, used in the default description
   * @default "item"
   */
  itemType?: string
  /**
   * The name or identifier of the item being deleted, used in the default description
   */
  itemName?: string
}

export function DeleteConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Deletion",
  description,
  cancelText = "Cancel",
  confirmText = "Delete",
  itemType = "item",
  itemName,
}: DeleteConfirmationDialogProps) {
  // Generate default description if not provided
  const defaultDescription = React.useMemo(() => {
    if (itemName) {
      return `Are you sure you want to delete ${itemType} "${itemName}"? This action cannot be undone.`
    }
    return `Are you sure you want to delete this ${itemType}? This action cannot be undone.`
  }, [itemType, itemName])

  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description || defaultDescription}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{cancelText}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

/**
 * A button that triggers a delete confirmation dialog
 */
export function DeleteButton({
  onClick,
  disabled,
  className,
  children,
  size = "icon",
  ...dialogProps
}: Omit<DeleteConfirmationDialogProps, "isOpen" | "onClose" | "onConfirm"> & {
  onClick: () => void
  disabled?: boolean
  className?: string
  children?: React.ReactNode
  size?: "icon" | "default" | "sm" | "lg"
}) {
  const [isOpen, setIsOpen] = React.useState(false)

  return (
    <>
      <Button
        variant="outline"
        size={size}
        onClick={() => setIsOpen(true)}
        disabled={disabled}
        className={cn(
          "text-destructive border-destructive hover:bg-destructive/10 hover:text-destructive",
          className
        )}
        aria-label="Delete"
      >
        {children || (size === "icon" ? <Trash2 className="h-5 w-5" /> : "Delete")}
      </Button>
      <DeleteConfirmationDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={onClick}
        {...dialogProps}
      />
    </>
  )
}
