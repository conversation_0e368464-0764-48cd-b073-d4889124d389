"""
Generic Router Factory for CutRequestStudio Backend

This module provides a factory pattern to generate FastAPI routers with standardized
CRUD operations, eliminating code duplication across the 5 backend routers:
- backend/routers/user.py
- backend/routers/tire.py
- backend/routers/request.py
- backend/routers/request_detail_cut.py
- backend/routers/cut_processing.py

Integrates seamlessly with the Generic CRUD Base from PRIORITY 1 implementation.

Author: Kilo Code
Date: 28 Maggio 2025
Version: 2.0 (PRIORITY 2 Implementation)
"""

from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from pydantic import BaseModel
import logging

from .database import get_db
from .crud_base import CRUDBase, CRUDFactory, safe_get_or_404, safe_update_or_404, safe_delete_or_404
from . import auth

# Set up logging
logger = logging.getLogger(__name__)

# Type variables
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ReadSchemaType = TypeVar("ReadSchemaType", bound=BaseModel)

class RouterConfig:
    """
    Configuration class for router generation

    Consolidates all configuration options for creating standardized routers
    with flexible customization capabilities.
    """

    def __init__(
        self,
        prefix: str,
        tags: List[str],
        model: Type[ModelType],
        create_schema: Type[CreateSchemaType],
        update_schema: Type[UpdateSchemaType],
        read_schema: Type[ReadSchemaType],
        entity_name: str,
        entity_name_plural: Optional[str] = None,
        # Authentication dependencies
        viewer_dependency: Optional[Callable] = None,
        editor_dependency: Optional[Callable] = None,
        admin_dependency: Optional[Callable] = None,
        # Custom endpoints
        custom_endpoints: Optional[Dict[str, Dict[str, Any]]] = None,
        # Pagination defaults
        default_limit: int = 100,
        max_limit: int = 1000,
        # Additional dependencies
        additional_dependencies: Optional[List[Callable]] = None,
        # Response model overrides
        list_response_model: Optional[Type] = None,
        detail_response_model: Optional[Type] = None,
        # Custom filters
        custom_filters: Optional[Dict[str, Callable]] = None,
        # Soft delete support
        soft_delete: bool = False,
        # Custom validation
        create_validator: Optional[Callable] = None,
        update_validator: Optional[Callable] = None
    ):
        """
        Initialize router configuration

        Args:
            prefix: Router prefix (e.g., "/users", "/tires")
            tags: OpenAPI tags for documentation
            model: SQLAlchemy model class
            create_schema: Pydantic schema for creation
            update_schema: Pydantic schema for updates
            read_schema: Pydantic schema for responses
            entity_name: Singular entity name for error messages
            entity_name_plural: Plural entity name (auto-generated if not provided)
            viewer_dependency: Dependency for read operations
            editor_dependency: Dependency for write operations
            admin_dependency: Dependency for admin operations
            custom_endpoints: Additional custom endpoints configuration
            default_limit: Default pagination limit
            max_limit: Maximum pagination limit
            additional_dependencies: Additional dependencies for all endpoints
            list_response_model: Custom response model for list endpoints
            detail_response_model: Custom response model for detail endpoints
            custom_filters: Custom filter functions
            soft_delete: Enable soft delete functionality
            create_validator: Custom validation for create operations
            update_validator: Custom validation for update operations
        """
        self.prefix = prefix
        self.tags = tags
        self.model = model
        self.create_schema = create_schema
        self.update_schema = update_schema
        self.read_schema = read_schema
        self.entity_name = entity_name
        self.entity_name_plural = entity_name_plural or f"{entity_name}s"

        # Set default auth dependencies if not provided
        self.viewer_dependency = viewer_dependency or Depends(auth.require_viewer_role)
        self.editor_dependency = editor_dependency or Depends(auth.require_editor_role)
        self.admin_dependency = admin_dependency or Depends(auth.require_admin_role)

        self.custom_endpoints = custom_endpoints or {}
        self.default_limit = default_limit
        self.max_limit = max_limit
        self.additional_dependencies = additional_dependencies or []
        self.list_response_model = list_response_model or List[read_schema]
        self.detail_response_model = detail_response_model or read_schema
        self.custom_filters = custom_filters or {}
        self.soft_delete = soft_delete
        self.create_validator = create_validator
        self.update_validator = update_validator


class GenericRouterFactory:
    """
    Factory class for generating standardized FastAPI routers

    This factory eliminates the 85% code duplication identified across
    the 5 backend routers by providing a standardized way to create
    CRUD endpoints with consistent patterns.
    """

    @classmethod
    def create_crud_router(cls, config: RouterConfig) -> APIRouter:
        """
        Create a complete CRUD router with standardized endpoints

        Args:
            config: Router configuration object

        Returns:
            Configured FastAPI router with CRUD endpoints

        Example:
            ```python
            from . import models, schemas

            # Create router configuration
            user_config = RouterConfig(
                prefix="/users",
                tags=["users"],
                model=models.User,
                create_schema=schemas.UserCreate,
                update_schema=schemas.UserUpdate,
                read_schema=schemas.UserRead,
                entity_name="User"
            )

            # Generate router
            user_router = GenericRouterFactory.create_crud_router(user_config)
            ```
        """
        router = APIRouter(prefix=config.prefix, tags=config.tags)

        # Get CRUD instance
        crud = CRUDFactory.get_crud(
            config.model,
            config.create_schema,
            config.update_schema
        )

        # Create standard CRUD endpoints
        cls._add_list_endpoint(router, crud, config)
        cls._add_detail_endpoint(router, crud, config)
        cls._add_create_endpoint(router, crud, config)
        cls._add_update_endpoint(router, crud, config)
        cls._add_delete_endpoint(router, crud, config)

        # Add custom endpoints if configured
        if config.custom_endpoints:
            cls._add_custom_endpoints(router, crud, config)

        return router

    @classmethod
    def _add_list_endpoint(cls, router: APIRouter, crud: CRUDBase, config: RouterConfig):
        """Add GET / endpoint for listing entities"""

        @router.get(
            "/",
            response_model=config.list_response_model,
            dependencies=[config.viewer_dependency] + config.additional_dependencies,
            summary=f"Get {config.entity_name_plural}",
            description=f"Retrieve a list of {config.entity_name_plural.lower()} with optional pagination and filtering"
        )
        def read_entities(
            skip: int = Query(0, ge=0, description="Number of records to skip"),
            limit: int = Query(config.default_limit, ge=1, le=config.max_limit, description="Maximum number of records to return"),
            db: Session = Depends(get_db)
        ):
            """Get list of entities with pagination"""
            try:
                # Apply custom filters if configured
                filters = {}
                if config.soft_delete:
                    filters["is_deleted"] = False

                entities = crud.get_multi(
                    db,
                    skip=skip,
                    limit=limit,
                    filters=filters
                )
                return entities
            except Exception as e:
                logger.error(f"Error retrieving {config.entity_name_plural}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve {config.entity_name_plural.lower()}"
                )

    @classmethod
    def _add_detail_endpoint(cls, router: APIRouter, crud: CRUDBase, config: RouterConfig):
        """Add GET /{id} endpoint for entity details"""

        @router.get(
            "/{entity_id}",
            response_model=config.detail_response_model,
            dependencies=[config.viewer_dependency] + config.additional_dependencies,
            summary=f"Get {config.entity_name}",
            description=f"Retrieve a specific {config.entity_name.lower()} by ID"
        )
        def read_entity(
            entity_id: str,
            db: Session = Depends(get_db)
        ):
            """Get entity by ID"""
            return safe_get_or_404(crud, db, entity_id, config.entity_name)

    @classmethod
    def _add_create_endpoint(cls, router: APIRouter, crud: CRUDBase, config: RouterConfig):
        """Add POST / endpoint for entity creation"""

        @router.post(
            "/",
            response_model=config.detail_response_model,
            status_code=status.HTTP_201_CREATED,
            dependencies=[config.editor_dependency] + config.additional_dependencies,
            summary=f"Create {config.entity_name}",
            description=f"Create a new {config.entity_name.lower()}"
        )
        def create_entity(
            entity_data: config.create_schema,
            db: Session = Depends(get_db)
        ):
            """Create new entity"""
            try:
                # Apply custom validation if configured
                if config.create_validator:
                    config.create_validator(entity_data)

                entity = crud.create(db, obj_in=entity_data)
                logger.info(f"Created {config.entity_name} with ID: {entity.id}")
                return entity
            except Exception as e:
                logger.error(f"Error creating {config.entity_name}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to create {config.entity_name.lower()}: {str(e)}"
                )

    @classmethod
    def _add_update_endpoint(cls, router: APIRouter, crud: CRUDBase, config: RouterConfig):
        """Add PUT /{id} endpoint for entity updates"""

        @router.put(
            "/{entity_id}",
            response_model=config.detail_response_model,
            dependencies=[config.editor_dependency] + config.additional_dependencies,
            summary=f"Update {config.entity_name}",
            description=f"Update an existing {config.entity_name.lower()}"
        )
        def update_entity(
            entity_id: str,
            entity_data: config.update_schema,
            db: Session = Depends(get_db)
        ):
            """Update entity by ID"""
            try:
                # Apply custom validation if configured
                if config.update_validator:
                    config.update_validator(entity_data)

                entity = safe_update_or_404(crud, db, entity_id, entity_data, config.entity_name)
                logger.info(f"Updated {config.entity_name} with ID: {entity_id}")
                return entity
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error updating {config.entity_name}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to update {config.entity_name.lower()}: {str(e)}"
                )

    @classmethod
    def _add_delete_endpoint(cls, router: APIRouter, crud: CRUDBase, config: RouterConfig):
        """Add DELETE /{id} endpoint for entity deletion"""

        @router.delete(
            "/{entity_id}",
            status_code=status.HTTP_204_NO_CONTENT,
            dependencies=[config.admin_dependency] + config.additional_dependencies,
            summary=f"Delete {config.entity_name}",
            description=f"Delete a {config.entity_name.lower()}"
        )
        def delete_entity(
            entity_id: str,
            db: Session = Depends(get_db)
        ):
            """Delete entity by ID"""
            try:
                if config.soft_delete:
                    # Soft delete: mark as deleted
                    entity = safe_get_or_404(crud, db, entity_id, config.entity_name)
                    crud.update(db, db_obj=entity, obj_in={"is_deleted": True})
                    logger.info(f"Soft deleted {config.entity_name} with ID: {entity_id}")
                else:
                    # Hard delete: remove from database
                    safe_delete_or_404(crud, db, entity_id, config.entity_name)
                    logger.info(f"Deleted {config.entity_name} with ID: {entity_id}")

                return None
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error deleting {config.entity_name}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to delete {config.entity_name.lower()}: {str(e)}"
                )

    @classmethod
    def _add_custom_endpoints(cls, router: APIRouter, crud: CRUDBase, config: RouterConfig):
        """Add custom endpoints based on configuration"""

        for endpoint_name, endpoint_config in config.custom_endpoints.items():
            method = endpoint_config.get("method", "GET").upper()
            path = endpoint_config.get("path", f"/{endpoint_name}")
            handler = endpoint_config.get("handler")
            dependencies = endpoint_config.get("dependencies", [])
            response_model = endpoint_config.get("response_model")

            if not handler:
                logger.warning(f"No handler provided for custom endpoint: {endpoint_name}")
                continue

            # Add the custom endpoint
            if method == "GET":
                router.get(
                    path,
                    response_model=response_model,
                    dependencies=dependencies + config.additional_dependencies
                )(handler)
            elif method == "POST":
                router.post(
                    path,
                    response_model=response_model,
                    dependencies=dependencies + config.additional_dependencies
                )(handler)
            elif method == "PUT":
                router.put(
                    path,
                    response_model=response_model,
                    dependencies=dependencies + config.additional_dependencies
                )(handler)
            elif method == "DELETE":
                router.delete(
                    path,
                    dependencies=dependencies + config.additional_dependencies
                )(handler)


class RouterPresets:
    """
    Predefined router configurations for common patterns
    """

    @staticmethod
    def create_standard_config(
        prefix: str,
        entity_name: str,
        model: Type[ModelType],
        create_schema: Type[CreateSchemaType],
        update_schema: Type[UpdateSchemaType],
        read_schema: Type[ReadSchemaType],
        **kwargs
    ) -> RouterConfig:
        """
        Create a standard router configuration with common defaults

        Args:
            prefix: Router prefix
            entity_name: Entity name
            model: SQLAlchemy model
            create_schema: Create schema
            update_schema: Update schema
            read_schema: Read schema
            **kwargs: Additional configuration options

        Returns:
            RouterConfig instance
        """
        return RouterConfig(
            prefix=prefix,
            tags=[entity_name.lower() + "s"],
            model=model,
            create_schema=create_schema,
            update_schema=update_schema,
            read_schema=read_schema,
            entity_name=entity_name,
            **kwargs
        )

    @staticmethod
    def create_readonly_config(
        prefix: str,
        entity_name: str,
        model: Type[ModelType],
        read_schema: Type[ReadSchemaType],
        **kwargs
    ) -> RouterConfig:
        """
        Create a read-only router configuration (no create/update/delete)

        This is useful for entities that should only be readable via API
        """
        # Use read schema for all operations (though create/update won't be added)
        return RouterConfig(
            prefix=prefix,
            tags=[entity_name.lower() + "s"],
            model=model,
            create_schema=read_schema,  # Won't be used
            update_schema=read_schema,  # Won't be used
            read_schema=read_schema,
            entity_name=entity_name,
            editor_dependency=None,  # Disable write operations
            admin_dependency=None,   # Disable delete operations
            **kwargs
        )


class RouterUtils:
    """
    Utility functions for router operations
    """

    @staticmethod
    def create_batch_endpoints(
        router: APIRouter,
        crud: CRUDBase,
        config: RouterConfig
    ):
        """
        Add batch operation endpoints (create multiple, update multiple, delete multiple)
        """

        @router.post(
            "/batch",
            response_model=List[config.detail_response_model],
            dependencies=[config.editor_dependency] + config.additional_dependencies,
            summary=f"Create Multiple {config.entity_name_plural}",
            description=f"Create multiple {config.entity_name_plural.lower()} in a single request"
        )
        def create_batch(
            entities_data: List[config.create_schema],
            db: Session = Depends(get_db)
        ):
            """Create multiple entities"""
            try:
                created_entities = []
                for entity_data in entities_data:
                    if config.create_validator:
                        config.create_validator(entity_data)
                    entity = crud.create(db, obj_in=entity_data)
                    created_entities.append(entity)

                logger.info(f"Created {len(created_entities)} {config.entity_name_plural}")
                return created_entities
            except Exception as e:
                logger.error(f"Error creating batch {config.entity_name_plural}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to create {config.entity_name_plural.lower()}: {str(e)}"
                )

    @staticmethod
    def create_search_endpoint(
        router: APIRouter,
        crud: CRUDBase,
        config: RouterConfig,
        search_fields: List[str]
    ):
        """
        Add search endpoint with full-text search capabilities
        """

        @router.get(
            "/search",
            response_model=config.list_response_model,
            dependencies=[config.viewer_dependency] + config.additional_dependencies,
            summary=f"Search {config.entity_name_plural}",
            description=f"Search {config.entity_name_plural.lower()} by specified criteria"
        )
        def search_entities(
            q: str = Query(..., description="Search query"),
            skip: int = Query(0, ge=0),
            limit: int = Query(config.default_limit, ge=1, le=config.max_limit),
            db: Session = Depends(get_db)
        ):
            """Search entities by query string"""
            try:
                # This is a simplified search - in production you might want to use
                # full-text search capabilities of your database
                query = db.query(config.model)

                # Add search conditions for each field
                search_conditions = []
                for field in search_fields:
                    if hasattr(config.model, field):
                        field_attr = getattr(config.model, field)
                        if hasattr(field_attr.type, 'python_type') and field_attr.type.python_type == str:
                            search_conditions.append(field_attr.ilike(f"%{q}%"))

                if search_conditions:
                    from sqlalchemy import or_
                    query = query.filter(or_(*search_conditions))

                entities = query.offset(skip).limit(limit).all()
                return entities
            except Exception as e:
                logger.error(f"Error searching {config.entity_name_plural}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to search {config.entity_name_plural.lower()}"
                )


# Export factory and configuration classes
__all__ = [
    "GenericRouterFactory",
    "RouterConfig",
    "RouterPresets",
    "RouterUtils"
]