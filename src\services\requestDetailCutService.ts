import axiosInstance from "@/lib/axiosInstance";
import { RequestDetailCut, RequestDetailCutFormData } from "@/types";

// Ottieni la lista di tutti i tagli con parametri di query opzionali
export const getRequestDetailCuts = (params?: Record<string, any>) => {
  return axiosInstance.get<RequestDetailCut[]>("/request-detail-cuts", { params });
};

// Ottieni i dettagli di un taglio specifico
export const getRequestDetailCut = (id: number) => {
  return axiosInstance.get<RequestDetailCut>(`/request-detail-cuts/${id}`);
};

// Ottieni tutti i tagli per un request detail specifico
export const getRequestDetailCutsByRequestDetail = (requestDetailId: string) => {
  return axiosInstance.get<RequestDetailCut[]>(`/request-detail-cuts/by-request-detail/${requestDetailId}`);
};

// Crea un nuovo taglio
export const createRequestDetailCut = (data: RequestDetailCutFormData) => {
  // Transform camelCase to snake_case for backend compatibility
  const transformedData = {
    id: data.id,
    id_request_details: data.idRequestDetails,
    set: data.set,
    direction: data.direction,
    notes: data.notes,
    cut_price: data.cutPrice,
    status: data.status,
  };

  return axiosInstance.post<RequestDetailCut>("/request-detail-cuts", transformedData);
};

// Aggiorna un taglio esistente
export const updateRequestDetailCut = (id: number, data: RequestDetailCutFormData) => {
  // Transform camelCase to snake_case for backend compatibility
  const transformedData = {
    set: data.set,
    direction: data.direction,
    notes: data.notes,
    cut_price: data.cutPrice,
    status: data.status,
  };

  return axiosInstance.put<RequestDetailCut>(`/request-detail-cuts/${id}`, transformedData);
};

// Elimina un taglio
export const deleteRequestDetailCut = (id: number) => {
  return axiosInstance.delete<RequestDetailCut>(`/request-detail-cuts/${id}`);
};
