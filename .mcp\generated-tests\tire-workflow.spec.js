
// Generated Playwright test for tire management workflow
const { test, expect } = require('@playwright/test');

test('tire management workflow', async ({ page }) => {
  // Navigate to CutRequestStudio
  await page.goto('http://localhost:9002');

  // Wait for dashboard to load
  await page.waitForSelector('[data-testid="dashboard-loaded"]');

  // Open tire management
  await page.click('[data-testid="tire-management-button"]');

  // Wait for dialog
  await page.waitForSelector('[data-testid="tire-management-dialog"]');

  // Search for tire
  await page.fill('[data-testid="tire-search-input"]', 'Michelin');
  await page.click('[data-testid="search-button"]');

  // Verify results
  await expect(page.locator('[data-testid="tire-results"]')).toBeVisible();

  // Take screenshot
  await page.screenshot({ path: 'tire-workflow-test.png' });
});
