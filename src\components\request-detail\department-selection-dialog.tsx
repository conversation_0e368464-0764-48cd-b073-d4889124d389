
"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface DepartmentSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDepartment: (department: string) => void;
}

const mockDepartments = [
  "R&D - Tire Design",
  "Testing - Proving Grounds",
  "Logistics - TCE Warehouse",
  "Manufacturing - Plant A",
  "Quality Assurance",
  "Sales - EMEA",
  "Marketing - Global",
  "Human Resources",
  "IT Department",
  "Finance & Accounting",
];

export function DepartmentSelectionDialog({
  isOpen,
  onClose,
  onSelectDepartment,
}: DepartmentSelectionDialogProps) {

  const handleDepartmentClick = (department: string) => {
    onSelectDepartment(department);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-center">Select Department</DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-72 my-4 rounded-md p-2">
          <div className="space-y-1">
            {mockDepartments.map((dept) => (
              <Button
                key={dept}
                variant="ghost"
                className="w-full justify-start px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                onClick={() => handleDepartmentClick(dept)}
              >
                {dept}
              </Button>
            ))}
          </div>
        </ScrollArea>

        <DialogFooter className="justify-end">
          <DialogClose asChild>
            <Button variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
