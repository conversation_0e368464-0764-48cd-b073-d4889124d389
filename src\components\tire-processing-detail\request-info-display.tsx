
"use client";

import type * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
// import { Button } from "@/components/ui/button"; // Not used for icons in this simplified display
import { Textarea } from "@/components/ui/textarea";
// import { FileText, Users } from "lucide-react"; // Not used for icons here
import { format } from "date-fns";
import type { AppRequest } from "@/types";
import { cn } from "@/lib/utils";

interface FormFieldDisplayProps {
  label: string;
  value: string | number | React.ReactNode;
  className?: string;
}

const FormFieldDisplay: React.FC<FormFieldDisplayProps> = ({ label, value, className }) => {
  // Safely handle null/undefined values for inputs
  const safeValue = (val: any): string => {
    if (val === null || val === undefined) return "";
    return String(val);
  };

  return (
    <div className={cn("space-y-1.5", className)}>
      <Label className="text-sm font-medium text-muted-foreground">{label}</Label>
      {typeof value === 'string' || typeof value === 'number' ? (
        <Input value={safeValue(value)} readOnly className="bg-muted/50 border-none cursor-default" />
      ) : (
        <div className="h-10 flex items-center px-3 py-2 text-sm rounded-md bg-muted/50 border-none cursor-default">{value}</div>
      )}
    </div>
  );
};

interface RequestInfoDisplayProps {
  request: AppRequest | null;
}

export function RequestInfoDisplay({ request }: RequestInfoDisplayProps) {
  if (!request) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Request Details</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No request data available.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">REQUEST:</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-4">
          <FormFieldDisplay label="REQUEST:" value={request.id} />
          <FormFieldDisplay label="Status:" value={request.status} />
          <FormFieldDisplay label="Total N°:" value={request.totalN ?? 'N/A'} />
          <FormFieldDisplay label="Request Date:" value={request.requestDate ? format(new Date(request.requestDate), "PPP") : "N/A"} />

          <div className="space-y-1.5">
            <Label className="text-sm font-medium text-muted-foreground">Internal:</Label>
            <div className="flex items-center h-10 px-3 py-2 text-sm rounded-md bg-muted/50 border-none cursor-default">
                <Checkbox checked={request.internal} disabled className="mr-2" aria-readonly/>
                <span>{request.internal ? "Yes" : "No"}</span>
            </div>
          </div>

          <FormFieldDisplay label="Request By:" value={request.requestBy} />
          <FormFieldDisplay label="Type:" value={request.type} />
          <FormFieldDisplay label="Wish Date:" value={request.wishDate ? format(new Date(request.wishDate), "PPP") : "N/A"} />

          {/* Simplified: Report icon button removed for pure display */}
          {/* <FormFieldDisplay label="Report:" value="N/A (Icon Action)" /> */}

          <FormFieldDisplay label="Pid/Project#:" value={request.pidProject || request.projectNo} className="xl:col-span-1" />
          <FormFieldDisplay label="Tire size:" value={request.tireSize} />

          {/* Simplified: In Charge Of icon button removed for pure display */}
          <FormFieldDisplay label="In Charge Of:" value={request.inChargeOf || "N/A"} className="xl:col-span-2" />


          <div className="sm:col-span-2 lg:col-span-3 xl:col-span-4 space-y-1.5">
            <Label className="text-sm font-medium text-muted-foreground">Note:</Label>
            <Textarea value={request.note || "N/A"} readOnly rows={2} className="bg-muted/50 border-none cursor-default" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
