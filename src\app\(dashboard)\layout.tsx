
"use client";

import type { ReactNode } from "react";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { SidebarProvider, Sidebar, SidebarHeader, SidebarContent, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarInset } from "@/components/ui/sidebar";
import { DashboardAppBar } from "@/components/dashboard/dashboard-app-bar";
import { Home, ClipboardList, FileSearch2, Settings2 } from "lucide-react";
import * as React from "react";
import { cn } from "@/lib/utils";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

export default function DashboardLayout({ children }: { children: ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [appBarTitle, setAppBarTitle] = React.useState("Dashboard");

  const menuItems = React.useMemo(() => [
    {
      label: "Richiesta",
      icon: ClipboardList,
      tooltip: "Gestione Richieste",
      href: "/dashboard/richiesta",
    },
    {
      label: "Dettaglio",
      icon: FileSearch2,
      tooltip: "Dettaglio Richiesta",
      href: "/dashboard/dettaglio",
    },
    {
      label: "Lavorazioni",
      icon: Settings2,
      tooltip: "Gestione Lavorazioni",
      href: "/dashboard/lavorazioni",
    },
  ], []);

  React.useEffect(() => {
    let currentTitle = "Dashboard";
    if (pathname === "/dashboard" || pathname === "/dashboard/") {
      currentTitle = "Dashboard";
    } else {
      const matchedItem = menuItems.find(item => pathname.startsWith(item.href));
      if (matchedItem) {
        currentTitle = matchedItem.label;
      } else if (pathname.startsWith("/dashboard/tire-processing-detail")) {
        currentTitle = "Dettaglio Lavorazione Pneumatico";
      }
    }
    setAppBarTitle(currentTitle);
  }, [pathname, menuItems]);

  const handleMenuItemClick = (item: typeof menuItems[0]) => {
    setAppBarTitle(item.label);
    router.push(item.href);
  };

  const handleHomeLinkClick = () => {
    setAppBarTitle("Dashboard");
    router.push('/dashboard');
  };

  return (
    <ProtectedRoute requiredRole="viewer">
      <SidebarProvider defaultOpen={true}>
      <Sidebar side="left" variant="sidebar" collapsible="none" className="shadow-md z-40 bg-white dark:bg-gray-900">
        <SidebarHeader className="p-0">
          <Link href="/dashboard" onClick={handleHomeLinkClick} className="flex items-center gap-2 px-4 py-5 h-16 bg-primary text-primary-foreground">
            <Home className="h-6 w-6" />
            <span className="font-medium text-xl tracking-wide group-data-[collapsible=icon]:hidden">BRIDGESTONE</span>
          </Link>
        </SidebarHeader>
        <SidebarContent className="p-0">
          <SidebarMenu>
            {menuItems.map((item) => (
              <SidebarMenuItem key={item.label} className="px-2 py-1">
                 <SidebarMenuButton
                      asChild={item.href.startsWith("/")}
                      tooltip={item.tooltip}
                      onClick={() => {
                        if (!item.href.startsWith("/")) {
                           handleMenuItemClick(item);
                        } else {
                           setAppBarTitle(item.label);
                        }
                      }}
                      className={cn(
                        "justify-start w-full rounded-md px-3 py-2.5 transition-colors",
                        pathname.startsWith(item.href)
                          ? "bg-primary/10 text-primary font-medium"
                          : "hover:bg-muted"
                      )}
                    >
                      {item.href.startsWith("/") ? (
                        <Link href={item.href} className="flex items-center gap-3 w-full">
                          <item.icon className="h-5 w-5" />
                          <span className="group-data-[collapsible=icon]:hidden text-sm font-medium">{item.label}</span>
                        </Link>
                      ) : (
                        <>
                          <item.icon className="h-5 w-5" />
                          <span className="group-data-[collapsible=icon]:hidden text-sm font-medium">{item.label}</span>
                        </>
                      )}
                    </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarContent>
      </Sidebar>
      <SidebarInset>
        <DashboardAppBar title={appBarTitle} />
        <main className="p-4 sm:p-6 lg:p-8 bg-muted min-h-[calc(100vh-4rem)]">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
    </ProtectedRoute>
  );
}
