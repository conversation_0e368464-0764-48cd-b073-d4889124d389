/**
 * Knowledge Extractor for CutRequestStudio Memory Bank
 * Extracts patterns, documentation, and knowledge from codebase
 */

const fs = require('fs');
const path = require('path');

class KnowledgeExtractor {
  constructor(projectRoot = '.') {
    this.projectRoot = projectRoot;
    this.memoryBankPath = path.join(projectRoot, '.kilocode', 'memory-bank');
    this.extractedKnowledge = {
      technical: {},
      development: {},
      business: {},
      operational: {}
    };
  }

  /**
   * Extract all knowledge from the project
   */
  async extractAll() {
    console.log('🔍 Starting comprehensive knowledge extraction...');

    await this.extractTechnicalKnowledge();
    await this.extractDevelopmentHistory();
    await this.extractBusinessDomain();
    await this.extractOperationalKnowledge();

    await this.saveExtractedKnowledge();
    await this.updateIndexes();

    console.log('✅ Knowledge extraction completed successfully');
    return this.extractedKnowledge;
  }

  /**
   * Extract technical architecture and patterns
   */
  async extractTechnicalKnowledge() {
    console.log('📐 Extracting technical knowledge...');

    // Extract architecture patterns
    this.extractedKnowledge.technical.architecture = {
      patterns: await this.extractArchitecturePatterns(),
      components: await this.extractComponentPatterns(),
      services: await this.extractServicePatterns(),
      integrations: await this.extractIntegrationPatterns()
    };

    // Extract API documentation
    this.extractedKnowledge.technical.api = {
      schemas: await this.extractApiSchemas(),
      endpoints: await this.extractApiEndpoints(),
      types: await this.extractTypeDefinitions(),
      validation: await this.extractValidationRules()
    };

    // Extract frontend patterns
    this.extractedKnowledge.technical.frontend = {
      components: await this.extractReactComponents(),
      hooks: await this.extractCustomHooks(),
      contexts: await this.extractReactContexts(),
      utilities: await this.extractFrontendUtilities()
    };

    // Extract backend patterns
    this.extractedKnowledge.technical.backend = {
      models: await this.extractDatabaseModels(),
      crud: await this.extractCrudPatterns(),
      routers: await this.extractRouterPatterns(),
      middleware: await this.extractMiddlewarePatterns()
    };

    // Extract performance optimizations
    this.extractedKnowledge.technical.performance = {
      optimizations: await this.extractPerformanceOptimizations(),
      monitoring: await this.extractMonitoringPatterns(),
      caching: await this.extractCachingStrategies(),
      benchmarks: await this.extractBenchmarkData()
    };
  }

  /**
   * Extract development history and decisions
   */
  async extractDevelopmentHistory() {
    console.log('📚 Extracting development history...');

    this.extractedKnowledge.development = {
      history: {
        'priority-1': await this.extractPriorityImplementation(1),
        'priority-2': await this.extractPriorityImplementation(2),
        'priority-3': await this.extractPriorityImplementation(3),
        timeline: await this.extractDevelopmentTimeline()
      },
      decisions: {
        architecture: await this.extractArchitecturalDecisions(),
        technology: await this.extractTechnologyDecisions(),
        patterns: await this.extractPatternDecisions(),
        'trade-offs': await this.extractTradeOffDecisions()
      },
      migrations: {
        'code-refactoring': await this.extractRefactoringMigrations(),
        database: await this.extractDatabaseMigrations(),
        'api-changes': await this.extractApiMigrations(),
        deployment: await this.extractDeploymentMigrations()
      },
      'lessons-learned': {
        successes: await this.extractSuccessStories(),
        challenges: await this.extractChallenges(),
        improvements: await this.extractImprovements(),
        'best-practices': await this.extractBestPractices()
      }
    };
  }

  /**
   * Extract business domain knowledge
   */
  async extractBusinessDomain() {
    console.log('🏢 Extracting business domain knowledge...');

    this.extractedKnowledge.business = {
      domain: {
        'tire-processing': await this.extractTireProcessingKnowledge(),
        'request-management': await this.extractRequestManagementKnowledge(),
        'quality-control': await this.extractQualityControlKnowledge(),
        reporting: await this.extractReportingKnowledge()
      },
      workflows: {
        'user-journeys': await this.extractUserJourneys(),
        'approval-processes': await this.extractApprovalProcesses(),
        'data-flows': await this.extractDataFlows(),
        integrations: await this.extractBusinessIntegrations()
      },
      rules: {
        validation: await this.extractBusinessValidationRules(),
        'business-logic': await this.extractBusinessLogic(),
        permissions: await this.extractPermissionRules(),
        constraints: await this.extractBusinessConstraints()
      },
      standards: {
        industry: await this.extractIndustryStandards(),
        compliance: await this.extractComplianceRequirements(),
        quality: await this.extractQualityStandards(),
        security: await this.extractSecurityStandards()
      }
    };
  }

  /**
   * Extract operational knowledge
   */
  async extractOperationalKnowledge() {
    console.log('⚙️ Extracting operational knowledge...');

    this.extractedKnowledge.operational = {
      development: {
        setup: await this.extractDevelopmentSetup(),
        guidelines: await this.extractDevelopmentGuidelines(),
        tools: await this.extractDevelopmentTools(),
        workflows: await this.extractDevelopmentWorkflows()
      },
      deployment: {
        environments: await this.extractEnvironmentConfigurations(),
        procedures: await this.extractDeploymentProcedures(),
        rollback: await this.extractRollbackProcedures(),
        monitoring: await this.extractDeploymentMonitoring()
      },
      maintenance: {
        updates: await this.extractUpdateProcedures(),
        backups: await this.extractBackupProcedures(),
        performance: await this.extractPerformanceMaintenance(),
        security: await this.extractSecurityMaintenance()
      },
      troubleshooting: {
        'common-issues': await this.extractCommonIssues(),
        debugging: await this.extractDebuggingGuides(),
        performance: await this.extractPerformanceTroubleshooting(),
        recovery: await this.extractRecoveryProcedures()
      }
    };
  }

  /**
   * Extract architecture patterns from codebase
   */
  async extractArchitecturePatterns() {
    const patterns = [];

    // Extract Generic CRUD patterns
    const crudService = await this.readFile('src/lib/generic-crud-service.ts');
    if (crudService) {
      patterns.push({
        name: 'Generic CRUD Service',
        type: 'Service Pattern',
        description: 'Reusable CRUD operations with type safety',
        file: 'src/lib/generic-crud-service.ts',
        usage: 'Frontend service layer',
        benefits: ['Type safety', 'Code reuse', 'Consistent API'],
        implementation: this.extractCodePattern(crudService, 'class GenericCrudService'),
        examples: this.extractUsageExamples(crudService)
      });
    }

    // Extract Universal Form Hook patterns
    const formHook = await this.readFile('src/hooks/useUniversalForm.ts');
    if (formHook) {
      patterns.push({
        name: 'Universal Form Hook',
        type: 'React Hook Pattern',
        description: 'Reusable form management with validation',
        file: 'src/hooks/useUniversalForm.ts',
        usage: 'Form components',
        benefits: ['Form state management', 'Validation integration', 'Reusability'],
        implementation: this.extractCodePattern(formHook, 'function useUniversalForm'),
        examples: this.extractUsageExamples(formHook)
      });
    }

    return patterns;
  }

  /**
   * Extract Priority implementation details
   */
  async extractPriorityImplementation(priority) {
    const docPath = `docs/PRIORITY_${priority}_IMPLEMENTATION.md`;
    const content = await this.readFile(docPath);

    if (!content) return null;

    return {
      priority: priority,
      document: docPath,
      summary: this.extractSection(content, 'EXECUTIVE SUMMARY'),
      components: this.extractSection(content, 'COMPONENTI IMPLEMENTATI'),
      results: this.extractSection(content, 'RISULTATI QUANTITATIVI'),
      integration: this.extractSection(content, 'INTEGRAZIONE'),
      performance: this.extractSection(content, 'PERFORMANCE'),
      testing: this.extractSection(content, 'TESTING'),
      migration: this.extractSection(content, 'MIGRATION GUIDE')
    };
  }

  /**
   * Utility methods
   */
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.projectRoot, filePath);
      return fs.readFileSync(fullPath, 'utf8');
    } catch (error) {
      return null;
    }
  }

  extractCodePattern(content, pattern) {
    // Extract code patterns using regex
    const regex = new RegExp(`${pattern}[\\s\\S]*?(?=\\n\\n|\\n\\s*\\/\\*|$)`, 'g');
    const matches = content.match(regex);
    return matches ? matches[0] : null;
  }

  extractUsageExamples(content) {
    // Extract usage examples from comments or documentation
    const exampleRegex = /\/\*\*[\s\S]*?@example[\s\S]*?\*\//g;
    const matches = content.match(exampleRegex);
    return matches || [];
  }

  extractSection(content, sectionName) {
    const regex = new RegExp(`## \\*\\*.*${sectionName}.*\\*\\*([\\s\\S]*?)(?=## \\*\\*|$)`, 'i');
    const match = content.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * Save extracted knowledge to memory bank
   */
  async saveExtractedKnowledge() {
    console.log('💾 Saving extracted knowledge...');

    for (const [category, knowledge] of Object.entries(this.extractedKnowledge)) {
      const categoryPath = path.join(this.memoryBankPath, category);
      await this.ensureDirectory(categoryPath);

      for (const [subcategory, data] of Object.entries(knowledge)) {
        const subcategoryPath = path.join(categoryPath, subcategory);
        await this.ensureDirectory(subcategoryPath);

        const filePath = path.join(subcategoryPath, 'extracted-knowledge.json');
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      }
    }
  }

  /**
   * Update search and cross-reference indexes
   */
  async updateIndexes() {
    console.log('🔄 Updating indexes...');

    // Update metadata
    const metadataPath = path.join(this.memoryBankPath, 'index', 'metadata.json');
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
    metadata.memoryBank.lastUpdated = new Date().toISOString();
    metadata.memoryBank.statistics.totalDocuments = this.countExtractedDocuments();
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

    // Update search index
    await this.updateSearchIndex();

    // Update knowledge graph
    await this.updateKnowledgeGraph();
  }

  async ensureDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  countExtractedDocuments() {
    let count = 0;
    for (const category of Object.values(this.extractedKnowledge)) {
      for (const subcategory of Object.values(category)) {
        if (Array.isArray(subcategory)) {
          count += subcategory.length;
        } else if (typeof subcategory === 'object') {
          count += Object.keys(subcategory).length;
        }
      }
    }
    return count;
  }

  async updateSearchIndex() {
    // Implementation for search index updates
    console.log('📇 Search index updated');
  }

  async updateKnowledgeGraph() {
    // Implementation for knowledge graph updates
    console.log('🕸️ Knowledge graph updated');
  }
}

module.exports = KnowledgeExtractor;

// CLI usage
if (require.main === module) {
  const extractor = new KnowledgeExtractor();
  extractor.extractAll().catch(console.error);
}