{"crossReferences": {"version": "1.0.0", "lastUpdated": "2025-05-28T13:52:00Z", "relationships": {"technical-to-business": [], "implementation-to-requirements": [], "component-to-workflow": [], "pattern-to-usage": [], "decision-to-outcome": []}, "categories": {"IMPLEMENTS": {"description": "Technical component implements business requirement", "bidirectional": true}, "DEPENDS_ON": {"description": "Component depends on another component", "bidirectional": false}, "RELATES_TO": {"description": "General relationship between knowledge items", "bidirectional": true}, "EVOLVED_FROM": {"description": "Current implementation evolved from previous version", "bidirectional": false}, "SUPPORTS": {"description": "Technical pattern supports business workflow", "bidirectional": false}}, "index": {"bySource": {}, "byTarget": {}, "byType": {}}}}