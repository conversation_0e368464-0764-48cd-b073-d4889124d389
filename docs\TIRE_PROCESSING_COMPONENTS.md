# Tire Processing Components Documentation

This document provides detailed documentation for the tire processing components in the CutRequestStudio application.

## Overview

The tire processing system allows users to manage processing operations associated with specific tires. It includes functionality for viewing, editing, and saving processing data with proper field-level access control.

## Component Architecture

### Main Page Component

**File**: `src/app/(dashboard)/dashboard/tire-processing-detail/page.tsx`

The main tire processing detail page that orchestrates all tire processing functionality.

#### Key Features
- **Data Loading**: Fetches tire and processing data from multiple API endpoints
- **State Management**: Manages tire data, processing data, and form states
- **Save Operations**: Handles saving both tire information and processing quantities
- **Data Refresh**: Automatically refreshes data after successful save operations
- **Error Handling**: Comprehensive error handling with user feedback

#### State Variables
```typescript
const [tireData, setTireData] = useState<TireFormData>(initialTireFormData);
const [processings, setProcessings] = useState<TireProcessingItem[]>([]);
const [selectedProcessingId, setSelectedProcessingId] = useState<string | null>(null);
const [currentProcessingFormData, setCurrentProcessingFormData] = useState<TireProcessingFormData>(initialTireProcessingFormData);
const [modifiedProcessingIds, setModifiedProcessingIds] = useState<Set<string>>(new Set());
const [isTireDataModified, setIsTireDataModified] = useState(false);
const [isSaving, setIsSaving] = useState(false);
```

#### Key Functions

**handleSavePageClick()**: Main save function that:
1. Saves tire data if modified
2. Saves processing quantity changes
3. Refreshes data from server after successful saves
4. Provides comprehensive error handling and user feedback

**handleProcessingTableInputChange()**: Handles inline editing in the processing table:
- Only allows modification of the 'n' (quantity) field
- Tracks modified processing items
- Synchronizes table and form data

### Processing List Section

**File**: `src/components/tire-processing-detail/processing-list-section.tsx`

Displays the list of processing operations associated with a tire in a table format.

#### Features
- **Read-Only Fields**: Description1, Description2, Price, Code1, Code2 are displayed as read-only
- **Editable Quantity**: Only the 'n' (quantity) field can be edited inline
- **Row Selection**: Click to select processing items for detailed editing
- **Action Buttons**: Back, Add, and Delete operations
- **Material Design**: Consistent styling with muted backgrounds for read-only fields

#### Props Interface
```typescript
interface ProcessingListSectionProps {
  processings: TireProcessingItem[];
  selectedProcessingId: string | null;
  onRowClick: (id: string) => void;
  onAction: (action: "back" | "add" | "delete") => void;
  onTableInputChange: (processingId: string, field: keyof TireProcessingItem, value: any) => void;
}
```

### Processing Edit Form

**File**: `src/components/tire-processing-detail/processing-edit-form.tsx`

Provides a detailed form for editing selected processing items.

#### Features
- **Read-Only Display**: Shows processing details (descriptions, type) as read-only
- **Quantity Editing**: Allows editing of the quantity field
- **Visual Distinction**: Clear visual separation between editable and read-only fields
- **Save/Cancel Actions**: Form-level save and cancel operations

#### Field Access Control
- **Read-Only**: `description1`, `description2`, `tyreType`
- **Editable**: `n` (quantity)
- **Display Only**: `picture` checkbox (read-only)

### Service Layer

**File**: `src/services/tireProcessingService.ts`

Handles API communication for tire processing operations.

#### Key Functions

**getTireProcessing(requestDetailId)**: 
- Fetches all processing data for a specific tire
- Transforms backend response to frontend format
- Returns array of TireProcessingItem objects

**updateTireProcessing(cutProcessingId, quantity, notes)**:
- Updates processing quantity and notes
- Only modifies editable fields
- Returns updated processing data

#### Data Transformation
The service transforms backend `CutProcessingWithProcessing` objects to frontend `TireProcessingItem` format:

```typescript
const transformToTireProcessingItem = (item: CutProcessingWithProcessing): TireProcessingItem => {
  return {
    id: `CP-${item.id}`, // Prefix for unique identification
    description1: item.processing.description1,
    description2: item.processing.description2,
    price: item.processing.cost.replace(' €', '').trim(),
    tyreType: item.processing.tire_type,
    code1: item.processing.test_code1,
    code2: item.processing.test_code2,
    n: item.quantity,
    picture: item.processing.picture,
  };
};
```

## Data Flow

### Loading Process
1. **Page Load**: Component fetches request and tire data
2. **Processing Data**: Loads processing data for the specific tire
3. **State Initialization**: Sets up all state variables
4. **UI Rendering**: Displays data in table and form components

### Save Process
1. **Change Tracking**: Modified items are tracked in state
2. **Validation**: Ensures data integrity before saving
3. **API Calls**: Sends updates to backend endpoints
4. **Data Refresh**: Reloads data from server after successful saves
5. **User Feedback**: Shows success/error messages via toast notifications

### Error Handling
- **Network Errors**: Graceful handling of API failures
- **Validation Errors**: Client-side validation with user feedback
- **State Recovery**: Maintains UI state during error conditions
- **User Guidance**: Clear error messages and recovery suggestions

## Field Access Control

### Read-Only Fields
These fields come from the master Processing catalog and cannot be modified:
- `description1`: Primary processing description
- `description2`: Secondary processing description  
- `price`: Processing cost
- `tyreType`: Tire type specification
- `code1`: Primary test code
- `code2`: Secondary test code

### Editable Fields
These fields can be modified at the CutProcessing association level:
- `n` (quantity): Number of processing operations
- `notes`: Additional notes (via detailed form)

## Material Design Implementation

### Visual Indicators
- **Read-Only Fields**: Displayed with `bg-muted/50` background and rounded borders
- **Editable Fields**: Standard input styling with focus states
- **Selected Rows**: Highlighted with `bg-primary/20` background
- **Action Buttons**: Consistent icon-based design with hover states

### User Experience
- **Progressive Disclosure**: Detailed edit form appears only when item is selected
- **Immediate Feedback**: Toast notifications for all user actions
- **Loading States**: Visual indicators during save operations
- **Confirmation Dialogs**: For destructive actions like delete

## Integration Points

### Backend APIs
- `GET /cut-processing/by-tire/{request_detail_id}`: Load processing data
- `PUT /cut-processing/{id}`: Update processing quantities
- `PUT /requests/details/{detail_id}`: Update tire information

### State Management
- **React Context**: Authentication and global state
- **Local State**: Component-specific data and UI state
- **Form State**: Controlled components with validation

### Navigation
- **URL Parameters**: Request ID and tire ID from search params
- **Breadcrumb Navigation**: Back to request details
- **Deep Linking**: Direct access to specific tire processing pages

## Testing Considerations

### Unit Tests
- Component rendering with various data states
- User interaction handling (clicks, form inputs)
- State management and data transformations
- Error boundary behavior

### Integration Tests
- API service integration
- End-to-end save workflows
- Navigation between components
- Error handling scenarios

### Accessibility
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- ARIA labels and descriptions

## Performance Optimizations

### Data Loading
- Efficient API calls with proper error handling
- Minimal re-renders through proper state management
- Lazy loading of non-critical data

### State Updates
- Batched state updates for better performance
- Memoization of expensive calculations
- Optimistic UI updates where appropriate

### Memory Management
- Proper cleanup of event listeners
- State reset on component unmount
- Efficient data structures for large datasets
