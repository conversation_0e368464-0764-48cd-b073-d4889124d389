# CutRequestStudio Deployment Guide

This guide covers deployment strategies for CutRequestStudio in various environments.

## Prerequisites

### System Requirements
- **Node.js** 18+ with npm
- **Python** 3.8+ with pip
- **Database**: PostgreSQL 12+ (production) or SQLite (development)
- **Memory**: Minimum 2GB RAM
- **Storage**: Minimum 10GB available space

### Production Dependencies
```bash
# Backend additional dependencies
pip install psycopg2-binary gunicorn

# Frontend production build
npm install --production
```

## Environment Configuration

### Backend Environment Variables (.env)
```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/cutrequestdb

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Optional
DEBUG=false
LOG_LEVEL=INFO
```

### Frontend Environment Variables (.env.production)
```env
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/api/v1
NEXT_PUBLIC_APP_ENV=production
```

## Database Setup

### PostgreSQL Setup
```sql
-- Create database and user
CREATE DATABASE cutrequestdb;
CREATE USER cutrequestuser WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE cutrequestdb TO cutrequestuser;

-- Connect to the database
\c cutrequestdb;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO cutrequestuser;
```

### Database Migration
```bash
# Initialize database with tables
cd backend
python create_db.py

# Optional: Populate with sample data
python populate_db.py
```

## Docker Deployment

### Docker Compose (Recommended)
Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: cutrequestdb
      POSTGRES_USER: cutrequestuser
      POSTGRES_PASSWORD: your-secure-password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    environment:
      DATABASE_URL: **************************************************************/cutrequestdb
      SECRET_KEY: your-super-secret-key
    ports:
      - "8000:8000"
    depends_on:
      - postgres

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    environment:
      NEXT_PUBLIC_API_BASE_URL: http://backend:8000/api/v1
    ports:
      - "3000:3000"
    depends_on:
      - backend

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend

volumes:
  postgres_data:
```

### Backend Dockerfile
Create `Dockerfile.backend`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend code
COPY backend/ .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

# Expose port
EXPOSE 8000

# Start application
CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000"]
```

### Frontend Dockerfile
Create `Dockerfile.frontend`:

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production image
FROM node:18-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### Deploy with Docker Compose
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Traditional Server Deployment

### Backend Deployment with Gunicorn
```bash
# Install production dependencies
pip install gunicorn

# Start with Gunicorn
gunicorn backend.main:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile - \
  --error-logfile -
```

### Frontend Deployment
```bash
# Build for production
npm run build

# Start production server
npm start

# Or use PM2 for process management
npm install -g pm2
pm2 start npm --name "cutrequestudio-frontend" -- start
```

### Nginx Configuration
Create `/etc/nginx/sites-available/cutrequestudio`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files caching
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/cutrequestudio /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Cloud Deployment

### AWS Deployment
1. **EC2 Instance**: Use t3.medium or larger
2. **RDS**: PostgreSQL instance for database
3. **S3**: For file storage (if implementing file uploads)
4. **CloudFront**: CDN for static assets
5. **Application Load Balancer**: For high availability

### Heroku Deployment
```bash
# Install Heroku CLI and login
heroku login

# Create applications
heroku create cutrequestudio-api
heroku create cutrequestudio-frontend

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev -a cutrequestudio-api

# Set environment variables
heroku config:set SECRET_KEY=your-secret-key -a cutrequestudio-api
heroku config:set NEXT_PUBLIC_API_BASE_URL=https://cutrequestudio-api.herokuapp.com/api/v1 -a cutrequestudio-frontend

# Deploy backend
git subtree push --prefix=backend heroku-api main

# Deploy frontend
git subtree push --prefix=frontend heroku-frontend main
```

## Monitoring and Logging

### Application Monitoring
```python
# Add to backend/main.py
import logging
from fastapi import Request
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    logger.info(f"{request.method} {request.url} - {response.status_code} - {process_time:.4f}s")
    return response
```

### Health Checks
```python
# Enhanced health check
@app.get("/health")
def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.1.0"
    }
```

## Security Considerations

### SSL/TLS Configuration
- Use Let's Encrypt for free SSL certificates
- Configure strong SSL ciphers
- Enable HSTS headers

### Environment Security
- Never commit secrets to version control
- Use environment variables for all sensitive data
- Implement proper CORS policies
- Regular security updates

### Database Security
- Use connection pooling
- Implement proper backup strategies
- Regular security patches
- Network isolation

## Backup Strategy

### Database Backup
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U cutrequestuser cutrequestdb > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### Application Backup
- Code: Version control (Git)
- Configuration: Environment variable documentation
- Database: Regular automated backups
- Files: S3 or equivalent cloud storage

## Performance Optimization

### Backend Optimization
- Use connection pooling
- Implement caching (Redis)
- Database query optimization
- API response compression

### Frontend Optimization
- Enable Next.js optimizations
- Implement proper caching headers
- Use CDN for static assets
- Image optimization

## Troubleshooting

### Common Deployment Issues
1. **Database connection errors**: Check connection string and firewall
2. **CORS issues**: Verify allowed origins configuration
3. **SSL certificate problems**: Check certificate validity and paths
4. **Memory issues**: Monitor resource usage and scale accordingly

### Log Analysis
```bash
# View application logs
docker-compose logs -f backend
docker-compose logs -f frontend

# System logs
sudo journalctl -u nginx -f
sudo journalctl -u postgresql -f
```

## Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Database read replicas
- Stateless application design
- Session management

### Vertical Scaling
- Monitor CPU and memory usage
- Database performance tuning
- Application profiling
- Resource allocation optimization
