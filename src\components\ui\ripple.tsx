"use client"

import * as React from "react"

interface RippleProps {
  color?: string
  duration?: number
  className?: string
}

export const Ripple = ({
  color = "rgba(255, 255, 255, 0.35)",
  duration = 500,
  className = "",
}: RippleProps) => {
  const [rippleArray, setRippleArray] = React.useState<
    { x: number; y: number; size: number }[]
  >([])

  const addRipple = (event: React.MouseEvent<HTMLDivElement>) => {
    const rippleContainer = event.currentTarget.getBoundingClientRect()
    const size =
      rippleContainer.width > rippleContainer.height
        ? rippleContainer.width
        : rippleContainer.height
    const x = event.clientX - rippleContainer.left - size / 2
    const y = event.clientY - rippleContainer.top - size / 2
    const newRipple = {
      x,
      y,
      size,
    }

    setRippleArray([...rippleArray, newRipple])
  }

  React.useEffect(() => {
    let bounce: number | undefined

    if (rippleArray.length > 0) {
      bounce = window.setTimeout(() => {
        setRippleArray([])
        clearTimeout(bounce)
      }, duration * 4)
    }

    return () => clearTimeout(bounce)
  }, [rippleArray.length, duration])

  return (
    <div
      className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}
      onMouseDown={addRipple}
    >
      {rippleArray.length > 0 &&
        rippleArray.map((ripple, index) => {
          return (
            <span
              key={index}
              style={{
                position: "absolute",
                left: ripple.x,
                top: ripple.y,
                width: ripple.size,
                height: ripple.size,
                borderRadius: "50%",
                backgroundColor: color,
                transform: "scale(0)",
                animation: `ripple ${duration}ms linear`,
                opacity: 0,
              }}
            />
          )
        })}
      <style jsx>{`
        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 0.5;
          }
          100% {
            transform: scale(2);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  )
}
