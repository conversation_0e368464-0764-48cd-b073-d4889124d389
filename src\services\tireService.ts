import axiosInstance from "@/lib/axiosInstance";
import { DialogTire } from "@/types";

// Relative API paths
const TIRE_API_PATH = "/tires";
const REQUEST_DETAILS_API_PATH = "/requests/details";

// Interface for tire filter parameters
export interface TireFilterParams {
  skip?: number;
  limit?: number;
  owner?: string;
  pattern?: string;
  size?: string;
  project_no?: string;
}

// Get all tires with optional filtering
export const getTires = async (params?: TireFilterParams) => {
  try {
    const response = await axiosInstance.get<DialogTire[]>(TIRE_API_PATH, { params });
    return response.data;
  } catch (error) {
    console.error("[tireService] Error fetching tires:", error);
    throw error;
  }
};

// Get a specific tire by ID
export const getTire = async (id: string) => {
  try {
    // The ID itself might indicate the path if it's a detail
    const path = id.startsWith("MGT-") || id.startsWith("DET-") ? `${REQUEST_DETAILS_API_PATH}/${id}` : `${TIRE_API_PATH}/${id}`;
    const response = await axiosInstance.get<DialogTire>(path);
    return response.data;
  } catch (error) {
    console.error(`[tireService] Error fetching tire with ID ${id}:`, error);
    throw error;
  }
};

// Create a new tire (assumes this always creates a "standard" tire, not a request detail directly)
export const createTire = async (data: Omit<DialogTire, "id">) => {
  try {
    const response = await axiosInstance.post<DialogTire>(TIRE_API_PATH, data);
    return response.data;
  } catch (error) {
    console.error("[tireService] Error creating tire:", error);
    throw error;
  }
};

// Update an existing tire or request detail
export const updateTire = async (id: string, data: Partial<DialogTire>) => {
  try {
    let path: string;
    if (id.startsWith("MGT-") || id.startsWith("DET-")) {
      path = `${REQUEST_DETAILS_API_PATH}/${id}`;
    } else {
      path = `${TIRE_API_PATH}/${id}`;
    }
    const response = await axiosInstance.put<DialogTire>(path, data);
    return response.data;
  } catch (error) {
    console.error(`[tireService] Error updating tire/detail with ID ${id}:`, error);
    throw error;
  }
};

// Delete a tire or a request detail
export const deleteTire = async (id: string) => {
  try {
    let path: string;
    if (id.startsWith("MGT-") || id.startsWith("DET-")) {
      path = `${REQUEST_DETAILS_API_PATH}/${id}`;
    } else {
      path = `${TIRE_API_PATH}/${id}`;
    }
    await axiosInstance.delete(path);
  } catch (error) {
    console.error(`[tireService] Error deleting tire/detail with ID ${id}:`, error);
    throw error;
  }
};

// updateRequestDetail is now consolidated into updateTire.
// If specific logic for updateRequestDetail was different beyond the endpoint,
// that would need to be merged into updateTire or handled differently.
// Based on the original code, it seems the only difference was the endpoint.
