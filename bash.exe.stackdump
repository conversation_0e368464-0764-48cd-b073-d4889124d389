Stack trace:
Frame         Function      Args
0007FFFFA8E0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF97E0) msys-2.0.dll+0x1FE8E
0007FFFFA8E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABB8) msys-2.0.dll+0x67F9
0007FFFFA8E0  000210046832 (000210286019, 0007FFFFA798, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8E0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA8E0  000210068E24 (0007FFFFA8F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABC0  00021006A225 (0007FFFFA8F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBEDBF0000 ntdll.dll
7FFBECA10000 KERNEL32.DLL
7FFBEAEF0000 KERNELBASE.dll
7FFBEC6C0000 USER32.dll
7FFBEB3F0000 win32u.dll
000210040000 msys-2.0.dll
7FFBEBB80000 GDI32.dll
7FFBEB420000 gdi32full.dll
7FFBEB540000 msvcp_win.dll
7FFBEB2D0000 ucrtbase.dll
7FFBED640000 advapi32.dll
7FFBEB990000 msvcrt.dll
7FFBEBAD0000 sechost.dll
7FFBEAD00000 bcrypt.dll
7FFBEC8F0000 RPCRT4.dll
7FFBEA520000 CRYPTBASE.DLL
7FFBEAD30000 bcryptPrimitives.dll
7FFBEBA90000 IMM32.DLL
