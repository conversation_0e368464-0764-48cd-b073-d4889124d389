#!/usr/bin/env python3
"""
Database Migration - Phase 2: Migrate Data to New Tables

This script migrates data from the old tables to the new simplified structure:
1. Migrate 'tire' data to enhanced 'tires' table
2. Transform 'request_details' to 'request_items' with tire references
3. Merge 'REQUEST_DETAIL_CUT' + 'cut_processing' into 'cut_operations'

The script handles data transformation and creates proper relationships.
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add the parent directory to the Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database import SessionLocal
import backend.models as models

def migrate_tire_data():
    """Migrate data from 'tire' table to enhanced 'tires' table"""
    print("🔄 Migrating tire data...")

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Check if old tire table exists and has data
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='tire'")
        if cursor.fetchone()[0] == 0:
            print("   ⚠️  Old 'tire' table not found, creating sample data...")
            create_sample_tire_data(cursor)
        else:
            # Migrate existing tire data
            cursor.execute("SELECT * FROM tire")
            old_tires = cursor.fetchall()

            if old_tires:
                print(f"   📦 Found {len(old_tires)} tires to migrate")

                # Get column names from old tire table
                cursor.execute("PRAGMA table_info(tire)")
                old_columns = [col[1] for col in cursor.fetchall()]

                for tire in old_tires:
                    tire_dict = dict(zip(old_columns, tire))

                    # Insert into new tires table with additional fields
                    cursor.execute('''
                        INSERT OR REPLACE INTO tires
                        (id, tug_no, spec_no, size, owner, load_index, pattern, project_no, location, description, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tire_dict['id'],
                        tire_dict['tug_no'],
                        tire_dict['spec_no'],
                        tire_dict['size'],
                        tire_dict['owner'],
                        tire_dict['load_index'],
                        tire_dict['pattern'],
                        tire_dict['project_no'],
                        tire_dict['location'],
                        f"Migrated from old tire catalog - {tire_dict.get('pattern', 'N/A')}",  # description
                        1  # is_active
                    ))
            else:
                print("   ⚠️  Old tire table is empty, creating sample data...")
                create_sample_tire_data(cursor)

        conn.commit()
        print("   ✅ Tire data migration completed")

    except Exception as e:
        conn.rollback()
        print(f"   ❌ Error migrating tire data: {e}")
        raise
    finally:
        conn.close()

def create_sample_tire_data(cursor):
    """Create sample tire data for testing"""
    sample_tires = [
        ("T001", "TUG-A1", "SPEC001", "225/45R17", "Michelin", "91V", "PILOT_SPORT", "PRJ001", "Shelf A-1", "High performance tire", 1),
        ("T002", "TUG-B2", "SPEC002", "235/50R18", "Bridgestone", "97W", "TURANZA", "PRJ002", "Shelf B-2", "Comfort tire", 1),
        ("T003", "TUG-C3", "SPEC003", "245/40R19", "Continental", "98Y", "SPORT_CONTACT", "PRJ003", "Shelf C-3", "Sport tire", 1),
        ("T004", "TUG-D4", "SPEC004", "255/35R20", "Pirelli", "97Y", "P_ZERO", "PRJ004", "Shelf D-4", "Ultra high performance", 1),
        ("T005", "TUG-E5", "SPEC005", "265/30R21", "Goodyear", "96Y", "EAGLE_F1", "PRJ005", "Shelf E-5", "Racing tire", 1),
    ]

    for tire in sample_tires:
        cursor.execute('''
            INSERT OR REPLACE INTO tires
            (id, tug_no, spec_no, size, owner, load_index, pattern, project_no, location, description, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', tire)

    print(f"   📦 Created {len(sample_tires)} sample tires")

def migrate_request_details_to_items():
    """Transform request_details to request_items with tire references"""
    print("🔄 Migrating request details to request items...")

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Check if old request_details table exists
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='request_details'")
        if cursor.fetchone()[0] == 0:
            print("   ⚠️  Old 'request_details' table not found, creating sample data...")
            create_sample_request_items(cursor)
            return

        # Get existing request_details
        cursor.execute("SELECT * FROM request_details")
        old_details = cursor.fetchall()

        if not old_details:
            print("   ⚠️  No request details found, creating sample data...")
            create_sample_request_items(cursor)
            return

        print(f"   📦 Found {len(old_details)} request details to migrate")

        # Get column names
        cursor.execute("PRAGMA table_info(request_details)")
        old_columns = [col[1] for col in cursor.fetchall()]

        for detail in old_details:
            detail_dict = dict(zip(old_columns, detail))

            # Try to find matching tire by tug_number or create a generic one
            cursor.execute("SELECT id FROM tires WHERE tug_no = ? LIMIT 1", (detail_dict.get('tug_number', ''),))
            tire_result = cursor.fetchone()

            if tire_result:
                tire_id = tire_result[0]
            else:
                # Create a generic tire entry for this request detail
                tire_id = f"T_GEN_{detail_dict['id']}"
                cursor.execute('''
                    INSERT OR REPLACE INTO tires
                    (id, tug_no, spec_no, size, owner, load_index, pattern, project_no, location, description, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    tire_id,
                    detail_dict.get('tug_number', f"TUG_{detail_dict['id']}"),
                    detail_dict.get('spec_number', 'SPEC_UNKNOWN'),
                    detail_dict.get('tire_size', 'SIZE_UNKNOWN'),
                    'UNKNOWN',
                    'UNKNOWN',
                    detail_dict.get('pattern', 'PATTERN_UNKNOWN'),
                    detail_dict.get('project_number', 'PRJ_UNKNOWN'),
                    'UNKNOWN',
                    f"Auto-generated from request detail {detail_dict['id']}",
                    1
                ))

            # Insert into request_items
            cursor.execute('''
                INSERT OR REPLACE INTO request_items
                (id, request_id, tire_id, quantity, disposition, notes, unit_price, section)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                detail_dict['id'],
                detail_dict['request_id'],
                tire_id,
                detail_dict.get('process_number', 1),  # Use process_number as quantity
                detail_dict.get('disposition', 'AVAILABLE'),
                detail_dict.get('note', ''),
                None,  # unit_price - not available in old schema
                detail_dict.get('section', '')
            ))

        conn.commit()
        print("   ✅ Request details migration completed")

    except Exception as e:
        conn.rollback()
        print(f"   ❌ Error migrating request details: {e}")
        raise
    finally:
        conn.close()

def create_sample_request_items(cursor):
    """Create sample request items for testing"""
    # First check if we have any requests
    cursor.execute("SELECT id FROM requests LIMIT 3")
    existing_requests = cursor.fetchall()

    if not existing_requests:
        print("   📝 Creating sample requests...")
        sample_requests = [
            ("REQ001", "<EMAIL>", "PRJ001", "PID001", "225/45R17", "2024-01-01", "2024-01-15", "OPEN", "NEWDEV", 5, "PLANT_A", 1, "2024-01-10", 2, "SET", "Manager A", "Test request 1"),
            ("REQ002", "<EMAIL>", "PRJ002", "PID002", "235/50R18", "2024-01-02", "2024-01-16", "IN_PROGRESS", "REPAIR", 3, "PLANT_B", 0, "2024-01-11", 1, "SET", "Manager B", "Test request 2"),
        ]

        for req in sample_requests:
            cursor.execute('''
                INSERT OR REPLACE INTO requests
                (id, request_by, project_no, pid_project, tire_size, request_date, target_date, status, type, total_n, destination, internal, wish_date, num_pneus_set, num_pneus_set_unit, in_charge_of, note)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', req)

        request_ids = ["REQ001", "REQ002"]
    else:
        request_ids = [req[0] for req in existing_requests]
        print(f"   📋 Using existing requests: {request_ids}")

    # Create sample request items using available request IDs and tire IDs
    sample_items = [
        ("ITEM001", request_ids[0], "T001", 2, "AVAILABLE", "Sample item 1", 150.0, "SECTION_A"),
        ("ITEM002", request_ids[0], "T002", 1, "AVAILABLE", "Sample item 2", 200.0, "SECTION_B"),
    ]

    if len(request_ids) > 1:
        sample_items.append(("ITEM003", request_ids[1], "T003", 3, "RESERVED", "Sample item 3", 250.0, "SECTION_C"))

    for item in sample_items:
        cursor.execute('''
            INSERT OR REPLACE INTO request_items
            (id, request_id, tire_id, quantity, disposition, notes, unit_price, section)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', item)

    print(f"   📦 Created {len(sample_items)} sample request items")

def main():
    """Main migration function for Phase 2"""
    print("🚀 Starting Database Migration - Phase 2")
    print("=" * 50)

    # Step 1: Migrate tire data
    migrate_tire_data()

    # Step 2: Migrate request details to request items
    migrate_request_details_to_items()

    # Step 3: Verify migration
    verify_data_migration()

    print("\n" + "=" * 50)
    print("✅ Phase 2 Migration completed successfully!")
    print("\nNext steps:")
    print("1. Test the new API endpoints")
    print("2. Update frontend to use new data structure")
    print("3. Run Phase 3 migration to remove old tables")

def verify_data_migration():
    """Verify that data was migrated correctly"""
    print("\n🔍 Verifying data migration...")

    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    try:
        # Check tires
        cursor.execute("SELECT COUNT(*) FROM tires")
        tire_count = cursor.fetchone()[0]
        print(f"   📊 Tires table: {tire_count} records")

        # Check request_items
        cursor.execute("SELECT COUNT(*) FROM request_items")
        item_count = cursor.fetchone()[0]
        print(f"   📊 Request items table: {item_count} records")

        # Check relationships
        cursor.execute('''
            SELECT ri.id, ri.request_id, t.tug_no, ri.quantity
            FROM request_items ri
            JOIN tires t ON ri.tire_id = t.id
            LIMIT 5
        ''')
        relationships = cursor.fetchall()

        if relationships:
            print("   🔗 Sample relationships:")
            for rel in relationships:
                print(f"     - Item {rel[0]}: Request {rel[1]} -> Tire {rel[2]} (qty: {rel[3]})")

    except Exception as e:
        print(f"   ❌ Error during verification: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
