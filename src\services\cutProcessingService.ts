import axiosInstance from "@/lib/axiosInstance";
import { CutProcessing, CutProcessingFormData, DialogProcessing, ProcessingSearchFilters } from "@/types";

// Ottieni la lista di tutte le associazioni cut-processing
export const getCutProcessingList = (params?: Record<string, any>) => {
  return axiosInstance.get<CutProcessing[]>("/cut-processing", { params });
};

// Ottieni i dettagli di una associazione specifica
export const getCutProcessing = (id: number) => {
  return axiosInstance.get<CutProcessing>(`/cut-processing/${id}`);
};

// Ottieni tutti i processing per un cut specifico
export const getCutProcessingByCut = (cutId: number) => {
  return axiosInstance.get<CutProcessing[]>(`/cut-processing/by-cut/${cutId}`);
};

// Crea una nuova associazione cut-processing
export const createCutProcessing = (data: CutProcessingFormData) => {
  // Transform camelCase to snake_case for backend compatibility
  const transformedData = {
    cut_id: data.cutId,
    processing_id: data.processingId,
    quantity: data.quantity || 1,
    notes: data.notes,
  };

  return axiosInstance.post<CutProcessing>("/cut-processing", transformedData);
};

// Aggiorna una associazione esistente
export const updateCutProcessing = (id: number, data: Partial<CutProcessingFormData>) => {
  // Transform camelCase to snake_case for backend compatibility
  const transformedData = {
    quantity: data.quantity,
    notes: data.notes,
  };

  return axiosInstance.put<CutProcessing>(`/cut-processing/${id}`, transformedData);
};

// Elimina una associazione
export const deleteCutProcessing = (id: number) => {
  // Validate input
  if (!id || id <= 0) {
    throw new Error(`Invalid processing ID: ${id}`);
  }

  return axiosInstance.delete<CutProcessing>(`/cut-processing/${id}`)
    .catch(error => {
      // Enhanced error handling with more specific messages
      if (error.response?.status === 404) {
        const enhancedError = new Error(`Processing with ID ${id} not found. It may have been already deleted.`);
        enhancedError.name = 'NotFoundError';
        throw enhancedError;
      } else if (error.response?.status === 403) {
        const enhancedError = new Error(`Insufficient permissions to delete processing with ID ${id}.`);
        enhancedError.name = 'PermissionError';
        throw enhancedError;
      }
      // Re-throw original error for other cases
      throw error;
    });
};

// Elimina una associazione specifica per cut e processing
export const deleteCutProcessingByCutAndProcessing = (cutId: number, processingId: string) => {
  return axiosInstance.delete<CutProcessing>(`/cut-processing/by-cut-and-processing/${cutId}/${processingId}`);
};

// Cerca processing con filtri avanzati
export const searchProcessing = (filters: ProcessingSearchFilters, params?: Record<string, any>) => {
  const searchParams: Record<string, any> = {
    ...params,
    tire_type: filters.tireType,
    description1: filters.description1,
    description2: filters.description2,
    test_code1: filters.testCode1,
    test_code2: filters.testCode2,
    min_cost: filters.minCost,
    max_cost: filters.maxCost,
    picture: filters.picture,
  };

  // Rimuovi parametri undefined
  Object.keys(searchParams).forEach(key => {
    if (searchParams[key] === undefined || searchParams[key] === "") {
      delete searchParams[key];
    }
  });

  return axiosInstance.get<DialogProcessing[]>("/cut-processing/processing/search", { params: searchParams });
};

// Utility function to check if a cut processing record exists
export const checkCutProcessingExists = async (id: number): Promise<boolean> => {
  try {
    await getCutProcessing(id);
    return true;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return false;
    }
    // Re-throw other errors (network issues, auth issues, etc.)
    throw error;
  }
};

// Safe delete function that checks existence first
export const safeDeletecutProcessing = async (id: number) => {
  // First check if the record exists
  const exists = await checkCutProcessingExists(id);
  if (!exists) {
    throw new Error(`Cannot delete processing with ID ${id}: record not found`);
  }

  // Proceed with deletion if record exists
  return deleteCutProcessing(id);
};
