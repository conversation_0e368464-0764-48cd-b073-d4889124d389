/**
 * Test script for the Memory MCP Server
 * Demonstrates the server's knowledge graph capabilities
 */

const { spawn } = require('child_process');
const path = require('path');

class MemoryServerTester {
    constructor() {
        this.serverProcess = null;
    }

    async startServer() {
        console.log('🚀 Starting Memory MCP Server...');

        // Start the memory server process
        this.serverProcess = spawn('npx', ['-y', '@modelcontextprotocol/server-memory'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                MEMORY_FILE_PATH: path.join(__dirname, 'memory.json')
            }
        });

        // Wait for server to be ready
        await new Promise((resolve) => {
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('Knowledge Graph MCP Server running')) {
                    console.log('✅ Memory server is ready!');
                    resolve();
                }
            });
        });
    }

    async sendRequest(method, params = {}) {
        const request = {
            jsonrpc: '2.0',
            id: Date.now(),
            method: method,
            params: params
        };

        console.log(`📤 Sending request: ${method}`);
        console.log(`   Params:`, JSON.stringify(params, null, 2));

        return new Promise((resolve, reject) => {
            let responseData = '';

            const timeout = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, 5000);

            const dataHandler = (data) => {
                responseData += data.toString();
                try {
                    const response = JSON.parse(responseData);
                    clearTimeout(timeout);
                    this.serverProcess.stdout.removeListener('data', dataHandler);
                    resolve(response);
                } catch (e) {
                    // Continue collecting data
                }
            };

            this.serverProcess.stdout.on('data', dataHandler);
            this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
        });
    }

    async demonstrateCapabilities() {
        console.log('\n🧠 Demonstrating Memory Server Capabilities\n');

        try {
            // 1. Create entities
            console.log('1️⃣ Creating entities...');
            const createEntitiesResponse = await this.sendRequest('tools/call', {
                name: 'create_entities',
                arguments: {
                    entities: [
                        {
                            name: 'John_Smith',
                            entityType: 'person',
                            observations: ['Software engineer', 'Works at Anthropic', 'Speaks fluent Spanish']
                        },
                        {
                            name: 'Anthropic',
                            entityType: 'organization',
                            observations: ['AI safety company', 'Founded in 2021', 'Based in San Francisco']
                        },
                        {
                            name: 'CutRequestStudio',
                            entityType: 'project',
                            observations: ['Tire request management application', 'Built with React and Next.js', 'Uses TypeScript']
                        }
                    ]
                }
            });
            console.log('✅ Entities created:', createEntitiesResponse.result);

            // 2. Create relations
            console.log('\n2️⃣ Creating relations...');
            const createRelationsResponse = await this.sendRequest('tools/call', {
                name: 'create_relations',
                arguments: {
                    relations: [
                        {
                            from: 'John_Smith',
                            to: 'Anthropic',
                            relationType: 'works_at'
                        },
                        {
                            from: 'John_Smith',
                            to: 'CutRequestStudio',
                            relationType: 'contributes_to'
                        }
                    ]
                }
            });
            console.log('✅ Relations created:', createRelationsResponse.result);

            // 3. Add observations
            console.log('\n3️⃣ Adding observations...');
            const addObservationsResponse = await this.sendRequest('tools/call', {
                name: 'add_observations',
                arguments: {
                    observations: [
                        {
                            entityName: 'John_Smith',
                            contents: ['Prefers morning meetings', 'Expert in React development']
                        }
                    ]
                }
            });
            console.log('✅ Observations added:', addObservationsResponse.result);

            // 4. Search nodes
            console.log('\n4️⃣ Searching for nodes...');
            const searchResponse = await this.sendRequest('tools/call', {
                name: 'search_nodes',
                arguments: {
                    query: 'React'
                }
            });
            console.log('✅ Search results:', searchResponse.result);

            // 5. Read entire graph
            console.log('\n5️⃣ Reading entire knowledge graph...');
            const readGraphResponse = await this.sendRequest('tools/call', {
                name: 'read_graph',
                arguments: {}
            });
            console.log('✅ Knowledge graph:', JSON.stringify(readGraphResponse.result, null, 2));

            // 6. Open specific nodes
            console.log('\n6️⃣ Opening specific nodes...');
            const openNodesResponse = await this.sendRequest('tools/call', {
                name: 'open_nodes',
                arguments: {
                    names: ['John_Smith', 'Anthropic']
                }
            });
            console.log('✅ Opened nodes:', JSON.stringify(openNodesResponse.result, null, 2));

        } catch (error) {
            console.error('❌ Error during demonstration:', error.message);
        }
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up...');
        if (this.serverProcess) {
            this.serverProcess.kill();
            console.log('✅ Server process terminated');
        }
    }
}

// Run the demonstration
async function main() {
    const tester = new MemoryServerTester();

    try {
        await tester.startServer();
        await tester.demonstrateCapabilities();
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await tester.cleanup();
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, cleaning up...');
    process.exit(0);
});

if (require.main === module) {
    main();
}

module.exports = MemoryServerTester;