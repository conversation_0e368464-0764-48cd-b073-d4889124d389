"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { DialogTire } from "@/types";
import { createTire } from "@/services/tireService";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Sample data for dropdowns
const tireSizes = [
  "205/55R16", "225/45R17", "205/45R18", "225/55R19", "205/45R16",
  "225/45R17", "205/55R18", "225/45R19", "245/40R18", "235/50R18",
  "215/60R16", "255/35R19", "195/65R15", "265/35R20", "185/65R15"
];

const owners = [
  "Bridgestone", "Pirelli", "Michelin", "Continental", "Goodyear", 
  "Competitor X", "Competitor Y", "Dunlop", "Hankook", "Yokohama"
];

const patterns = [
  "P-Zero", "Pilot Sport 4S", "Eagle F1", "Potenza S007", "Turanza T005",
  "Cinturato P7", "Scorpion Verde", "PremiumContact 6", "UltraContact UC7",
  "Assurance WeatherReady"
];

// Validation schema
const tireFormSchema = z.object({
  id: z.string()
    .min(1, "ID is required")
    .regex(/^T\d{3}$/, "ID must be in format T000"),
  tug_no: z.string()
    .min(1, "TUG Number is required")
    .regex(/^TUG-[A-E]\d$/, "TUG Number must be in format TUG-X0"),
  spec_no: z.string()
    .min(1, "Spec Number is required")
    .regex(/^SP-\d{3}$/, "Spec Number must be in format SP-000"),
  size: z.string().min(1, "Size is required"),
  owner: z.string().min(1, "Owner is required"),
  load_index: z.string()
    .min(1, "Load Index is required")
    .regex(/^\d{2}[V-Z]$/, "Load Index must be in format 00X"),
  pattern: z.string().min(1, "Pattern is required"),
  project_no: z.string()
    .min(1, "Project Number is required")
    .regex(/^PROJ-\d{3}$/, "Project Number must be in format PROJ-000"),
  location: z.string()
    .min(1, "Location is required")
    .regex(/^Shelf [A-F]-\d$/, "Location must be in format Shelf X-0"),
});

type TireFormValues = z.infer<typeof tireFormSchema>;

interface TireFormProps {
  onTireCreated: (tire: DialogTire) => void;
}

export function TireForm({ onTireCreated }: TireFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Initialize form with react-hook-form
  const form = useForm<TireFormValues>({
    resolver: zodResolver(tireFormSchema),
    defaultValues: {
      id: "",
      tug_no: "",
      spec_no: "",
      size: "",
      owner: "",
      load_index: "",
      pattern: "",
      project_no: "",
      location: "",
    },
  });

  // Generate a new ID when the component mounts
  React.useEffect(() => {
    const randomNum = Math.floor(Math.random() * 900) + 100; // Generate a random 3-digit number
    form.setValue("id", `T${randomNum}`);
  }, [form]);

  // Handle form submission
  const onSubmit = async (data: TireFormValues) => {
    setIsSubmitting(true);
    try {
      // Transform the data to match the DialogTire interface
      const tireData = {
        id: data.id,
        tugNo: data.tug_no,
        specNo: data.spec_no,
        size: data.size,
        owner: data.owner,
        loadIndex: data.load_index,
        pattern: data.pattern,
        projectNo: data.project_no,
        location: data.location,
      };
      
      // Call the API to create a new tire
      const response = await createTire({
        id: data.id,
        tug_no: data.tug_no,
        spec_no: data.spec_no,
        size: data.size,
        owner: data.owner,
        load_index: data.load_index,
        pattern: data.pattern,
        project_no: data.project_no,
        location: data.location,
      });
      
      // Notify parent component of successful creation
      onTireCreated(tireData);
      
      // Reset the form
      form.reset({
        id: `T${Math.floor(Math.random() * 900) + 100}`,
        tug_no: "",
        spec_no: "",
        size: "",
        owner: "",
        load_index: "",
        pattern: "",
        project_no: "",
        location: "",
      });
    } catch (error) {
      console.error("Failed to create tire:", error);
      toast({
        title: "Error",
        description: "Failed to create tire. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate a new TUG number
  const generateTugNo = () => {
    const letter = String.fromCharCode(65 + Math.floor(Math.random() * 5)); // A-E
    const number = Math.floor(Math.random() * 9) + 1; // 1-9
    form.setValue("tug_no", `TUG-${letter}${number}`);
  };

  // Generate a new Spec number
  const generateSpecNo = () => {
    const number = String(Math.floor(Math.random() * 900) + 100).padStart(3, '0');
    form.setValue("spec_no", `SP-${number}`);
  };

  // Generate a new Project number
  const generateProjectNo = () => {
    const number = String(Math.floor(Math.random() * 900) + 100).padStart(3, '0');
    form.setValue("project_no", `PROJ-${number}`);
  };

  // Generate a new Location
  const generateLocation = () => {
    const letter = String.fromCharCode(65 + Math.floor(Math.random() * 6)); // A-F
    const number = Math.floor(Math.random() * 9) + 1; // 1-9
    form.setValue("location", `Shelf ${letter}-${number}`);
  };

  // Generate a new Load Index
  const generateLoadIndex = () => {
    const loadValue = Math.floor(Math.random() * 12) + 88; // 88-99
    const speedRating = String.fromCharCode(86 + Math.floor(Math.random() * 5)); // V-Z
    form.setValue("load_index", `${loadValue}${speedRating}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* ID Field */}
          <FormField
            control={form.control}
            name="id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>ID</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="T000" disabled />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* TUG Number Field */}
          <FormField
            control={form.control}
            name="tug_no"
            render={({ field }) => (
              <FormItem>
                <FormLabel>TUG Number</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input {...field} placeholder="TUG-X0" />
                  </FormControl>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={generateTugNo}
                  >
                    Generate
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Spec Number Field */}
          <FormField
            control={form.control}
            name="spec_no"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Spec Number</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input {...field} placeholder="SP-000" />
                  </FormControl>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={generateSpecNo}
                  >
                    Generate
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Size Field */}
          <FormField
            control={form.control}
            name="size"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Size</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a size" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {tireSizes.map(size => (
                      <SelectItem key={size} value={size}>{size}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Owner Field */}
          <FormField
            control={form.control}
            name="owner"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Owner</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an owner" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {owners.map(owner => (
                      <SelectItem key={owner} value={owner}>{owner}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Load Index Field */}
          <FormField
            control={form.control}
            name="load_index"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Load Index</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input {...field} placeholder="00X" />
                  </FormControl>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={generateLoadIndex}
                  >
                    Generate
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Pattern Field */}
          <FormField
            control={form.control}
            name="pattern"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Pattern</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a pattern" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {patterns.map(pattern => (
                      <SelectItem key={pattern} value={pattern}>{pattern}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Project Number Field */}
          <FormField
            control={form.control}
            name="project_no"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Number</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input {...field} placeholder="PROJ-000" />
                  </FormControl>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={generateProjectNo}
                  >
                    Generate
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Location Field */}
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input {...field} placeholder="Shelf X-0" />
                  </FormControl>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={generateLocation}
                  >
                    Generate
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Tire"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
