import * as React from "react"

import { cn } from "@/lib/utils"

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  fullWidth?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, fullWidth = true, ...props }, ref) => {
    return (
      <div className={cn("relative", fullWidth ? "w-full" : "")}>
        <input
          type={type}
          className={cn(
            "flex h-10 w-full rounded-none border-0 border-b-2 border-input bg-transparent px-3 py-2 text-base transition-colors ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/70 focus-visible:outline-none focus-visible:border-b-2 focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            className
          )}
          ref={ref}
          {...props}
        />
        <div className="absolute bottom-0 left-0 w-full h-0.5 bg-transparent transition-transform origin-bottom-left scale-x-0 group-focus-within:scale-x-100 group-focus-within:bg-primary" />
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
