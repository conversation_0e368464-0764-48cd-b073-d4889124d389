"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Search, RefreshCw } from "lucide-react";
import { DialogTire, EnhancedTire } from "@/types";
import { cn } from "@/lib/utils";
import { getEnhancedTires, EnhancedTireFilterParams } from "@/services/enhancedTireService";

interface TireManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddSelectedTires?: (selectedTires: DialogTire[]) => void;
}

export function TireManagementDialog({
  open,
  onOpenChange,
  onAddSelectedTires,
}: TireManagementDialogProps) {
  const { toast } = useToast();
  const [enhancedTires, setEnhancedTires] = React.useState<EnhancedTire[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [selectedTireIds, setSelectedTireIds] = React.useState<Set<string>>(new Set());

  // Pagination state
  const [currentPage, setCurrentPage] = React.useState(1);
  const [totalPages, setTotalPages] = React.useState(1);
  const itemsPerPage = 10;

  // Filter state
  const [filters, setFilters] = React.useState<EnhancedTireFilterParams>({
    owner: "",
    pattern: "",
    size: "",
    project_no: "",
    is_active: true,
  });

  // Unique values for dropdowns
  const [uniqueOwners, setUniqueOwners] = React.useState<string[]>([]);
  const [uniquePatterns, setUniquePatterns] = React.useState<string[]>([]);
  const [uniqueSizes, setUniqueSizes] = React.useState<string[]>([]);

  // Load tires when the dialog opens or filters change
  React.useEffect(() => {
    if (open) {
      loadTires();
    }
  }, [open, currentPage, filters]);

  // Extract unique values for dropdowns from the loaded tires
  React.useEffect(() => {
    if (enhancedTires.length > 0) {
      setUniqueOwners([...new Set(enhancedTires.map(tire => tire.owner))]);
      setUniquePatterns([...new Set(enhancedTires.map(tire => tire.pattern))]);
      setUniqueSizes([...new Set(enhancedTires.map(tire => tire.size))]);
    }
  }, [enhancedTires]);

  const loadTires = async () => {
    setLoading(true);
    try {
      const params: EnhancedTireFilterParams = {
        ...filters,
        skip: (currentPage - 1) * itemsPerPage,
        limit: itemsPerPage,
      };

      const response = await getEnhancedTires(params);

      // Ensure response is an array
      if (!Array.isArray(response)) {
        console.error("Invalid response format: expected array, got:", typeof response);
        setEnhancedTires([]);
        setTotalPages(1);
        return;
      }

      setEnhancedTires(response);

      // Calculate total pages based on response headers or data length
      // This is a simplified approach - ideally the API would return total count
      const estimatedTotalItems = response.length < itemsPerPage
        ? (currentPage - 1) * itemsPerPage + response.length
        : currentPage * itemsPerPage + 1;

      setTotalPages(Math.max(1, Math.ceil(estimatedTotalItems / itemsPerPage)));
    } catch (error) {
      console.error("Failed to load enhanced tires:", error);
      setEnhancedTires([]);
      setTotalPages(1);
      toast({
        title: "Error",
        description: "Failed to load tires. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof EnhancedTireFilterParams, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setFilters({
      owner: "",
      pattern: "",
      size: "",
      project_no: "",
      is_active: true,
    });
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle tire selection
  const handleTireSelection = (tireId: string) => {
    setSelectedTireIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(tireId)) {
        newSet.delete(tireId);
      } else {
        newSet.add(tireId);
      }
      return newSet;
    });
  };

  // Transform EnhancedTire to DialogTire format for compatibility
  const mapEnhancedTireToDialogTire = (enhancedTire: EnhancedTire): DialogTire => {
    console.log('mapEnhancedTireToDialogTire: Mapping enhanced tire:', enhancedTire);

    const dialogTire = {
      id: enhancedTire.id,
      tugNo: enhancedTire.tugNo || "N/A",
      specNo: enhancedTire.specNo || "N/A",
      size: enhancedTire.size || "N/A",
      owner: enhancedTire.owner || "N/A",
      loadIndex: enhancedTire.loadIndex || "N/A",
      pattern: enhancedTire.pattern || "N/A",
      projectNo: enhancedTire.projectNo || "N/A",
      location: enhancedTire.location || "N/A",
    };

    console.log('mapEnhancedTireToDialogTire: Mapped to dialog tire:', dialogTire);
    return dialogTire;
  };

  // Toggle select all displayed tires
  const handleToggleSelectAllDisplayed = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(enhancedTires.map(tire => tire.id));
      setSelectedTireIds(allIds);
    } else {
      setSelectedTireIds(new Set());
    }
  };

  // Add selected tires to the request
  const handleAddSelected = () => {
    if (!onAddSelectedTires) return;

    const selectedEnhancedTires = enhancedTires.filter(t => selectedTireIds.has(t.id));
    if (selectedEnhancedTires.length === 0) {
      toast({
        title: "No Tires Selected",
        description: "Please select tires to add.",
        variant: "destructive"
      });
      return;
    }

    // Convert to DialogTire format for compatibility
    const tiresToAdd = selectedEnhancedTires.map(mapEnhancedTireToDialogTire);
    onAddSelectedTires(tiresToAdd);
    toast({
      title: "Tires Added",
      description: `${tiresToAdd.length} selected tires added to the request.`
    });
    onOpenChange(false); // Close dialog after adding
  };

  // Add all displayed tires to the request
  const handleAddAll = () => {
    if (!onAddSelectedTires) return;

    if (enhancedTires.length === 0) {
      toast({
        title: "No Tires to Add",
        description: "The current search result is empty.",
        variant: "destructive"
      });
      return;
    }

    // Convert to DialogTire format for compatibility
    const tiresToAdd = enhancedTires.map(mapEnhancedTireToDialogTire);
    onAddSelectedTires(tiresToAdd);
    toast({
      title: `Added All ${enhancedTires.length} Displayed Tires`,
      description: "All currently displayed tires have been added to the request."
    });
    onOpenChange(false); // Close dialog after adding
  };

  const areAllDisplayedSelected = enhancedTires.length > 0 &&
    selectedTireIds.size === enhancedTires.length &&
    enhancedTires.every(t => selectedTireIds.has(t.id));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Tire Management</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col flex-grow min-h-0 gap-4">
          {/* Filter Section */}
          <div className="bg-accent/10 p-4 rounded-lg shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold">Filters</h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                  className="h-8"
                >
                  Clear
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadTires}
                  className="h-8"
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Refresh
                </Button>

              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <Label htmlFor="owner-filter">Owner</Label>
                <Select
                  value={filters.owner || "all"}
                  onValueChange={(value) => handleFilterChange("owner", value === "all" ? "" : value)}
                >
                  <SelectTrigger id="owner-filter">
                    <SelectValue placeholder="All Owners" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Owners</SelectItem>
                    {uniqueOwners.map(owner => (
                      <SelectItem key={owner} value={owner}>{owner}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="pattern-filter">Pattern</Label>
                <Select
                  value={filters.pattern || "all"}
                  onValueChange={(value) => handleFilterChange("pattern", value === "all" ? "" : value)}
                >
                  <SelectTrigger id="pattern-filter">
                    <SelectValue placeholder="All Patterns" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Patterns</SelectItem>
                    {uniquePatterns.map(pattern => (
                      <SelectItem key={pattern} value={pattern}>{pattern}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="size-filter">Size</Label>
                <Select
                  value={filters.size || "all"}
                  onValueChange={(value) => handleFilterChange("size", value === "all" ? "" : value)}
                >
                  <SelectTrigger id="size-filter">
                    <SelectValue placeholder="All Sizes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sizes</SelectItem>
                    {uniqueSizes.map(size => (
                      <SelectItem key={size} value={size}>{size}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="project-filter">Project Number</Label>
                <div className="flex">
                  <Input
                    id="project-filter"
                    value={filters.project_no || ""}
                    onChange={(e) => handleFilterChange("project_no", e.target.value)}
                    placeholder="Search by project number"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="ml-1"
                    onClick={loadTires}
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>



          {/* Tire Table Section */}
          <div className="flex-grow flex flex-col min-h-0">
            <div className="mb-2 text-sm text-muted-foreground">
              {loading ? (
                <p>Loading tires...</p>
              ) : (
                <p>{enhancedTires.length} Tires found, displaying page {currentPage} of {totalPages}</p>
              )}
            </div>

            <ScrollArea className="rounded-md flex-grow bg-card">
              <Table>
                <TableHeader className="sticky top-0 bg-card z-10">
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox
                        checked={areAllDisplayedSelected}
                        onCheckedChange={(checked) => handleToggleSelectAllDisplayed(!!checked)}
                        aria-label="Select all tires in current view"
                      />
                    </TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>TUG Number</TableHead>
                    <TableHead>Spec Number</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Owner</TableHead>
                    <TableHead>Load Index</TableHead>
                    <TableHead>Pattern</TableHead>
                    <TableHead>Project Number</TableHead>
                    <TableHead>Location</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center h-24">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                      </TableCell>
                    </TableRow>
                  ) : enhancedTires.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center h-24">
                        No tires found matching your criteria.
                      </TableCell>
                    </TableRow>
                  ) : (
                    enhancedTires.map((tire) => (
                      <TableRow key={tire.id} className="hover:bg-accent/30">
                        <TableCell>
                          <Checkbox
                            checked={selectedTireIds.has(tire.id)}
                            onCheckedChange={() => handleTireSelection(tire.id)}
                            aria-label={`Select tire ${tire.id}`}
                          />
                        </TableCell>
                        <TableCell>{tire.id}</TableCell>
                        <TableCell>{tire.tugNo}</TableCell>
                        <TableCell>{tire.specNo}</TableCell>
                        <TableCell>{tire.size}</TableCell>
                        <TableCell>{tire.owner}</TableCell>
                        <TableCell>{tire.loadIndex}</TableCell>
                        <TableCell>{tire.pattern}</TableCell>
                        <TableCell>{tire.projectNo}</TableCell>
                        <TableCell>{tire.location}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-4 flex justify-center">
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0 rounded-full"
                  >
                    &lt;
                  </Button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page =>
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    )
                    .map((page, index, array) => (
                      <React.Fragment key={page}>
                        {index > 0 && array[index - 1] !== page - 1 && (
                          <span className="flex items-center justify-center">...</span>
                        )}
                        <Button
                          variant={currentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className="h-8 w-8 p-0 rounded-full"
                        >
                          {page}
                        </Button>
                      </React.Fragment>
                    ))}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-8 w-8 p-0 rounded-full"
                  >
                    &gt;
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="p-4 border-t flex-shrink-0">
          <div className="flex-grow">
            {/* Placeholder for left-aligned items if needed */}
          </div>
          <div className="flex space-x-2">
            {onAddSelectedTires && (
              <>
                <Button variant="outline" onClick={handleAddSelected}>
                  Add Selected: {selectedTireIds.size}
                </Button>
                <Button variant="outline" onClick={handleAddAll}>
                  Add All: {enhancedTires.length}
                </Button>
              </>
            )}
            <DialogClose asChild>
              <Button variant="default">Close</Button>
            </DialogClose>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
