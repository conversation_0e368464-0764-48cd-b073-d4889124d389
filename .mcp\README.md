# MCP Server Configuration for CutRequestStudio

This directory contains the Model Context Protocol (MCP) server configurations for the CutRequestStudio project.

## Installed MCP Servers

### Playwright MCP Server
- **Name**: `github.com/executeautomation/mcp-playwright`
- **Package**: `@executeautomation/playwright-mcp-server`
- **Purpose**: Browser automation, web scraping, screenshot generation, and automated testing
- **Command**: `npx -y @executeautomation/playwright-mcp-server`

## Configuration Files

- [`mcp-settings.json`](./mcp-settings.json): Main MCP server configuration
- [`servers/playwright/`](./servers/playwright/): Playwright-specific configurations and scripts

## Usage in VS Code

The MCP servers are configured to work with VS Code's MCP integration. After installation:

1. The servers should be automatically available in VS Code
2. Use the MCP tools through VS Code's command palette or integrated features
3. Access browser automation capabilities for testing and documentation

## Available Capabilities

### Playwright MCP Server Tools
- **Browser Control**: Navigate, click, type, scroll
- **Screenshot Capture**: Full page and element screenshots
- **JavaScript Execution**: Run custom scripts in browser context
- **Web Scraping**: Extract data from web pages
- **Test Generation**: Create Playwright test code
- **Page Analysis**: Inspect DOM elements and structure

## Integration with CutRequestStudio

The Playwright MCP server can be used for:
- Automated testing of tire management workflows
- Screenshot generation for documentation
- Web scraping for tire data validation
- Quality assurance testing
- End-to-end integration testing

## Installation Notes

- Installed globally via npm: `npm install -g @executeautomation/playwright-mcp-server`
- Configuration follows MCP server installation rules
- Server name uses the GitHub repository format as specified
- Compatible with Windows 11 and VS Code environment