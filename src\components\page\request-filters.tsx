"use client";

import * as React from "react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export const filterOptions = [
  "DEFAULT",
  "DRAFT",
  "APPROVED",
  "WORKING",
  "<PERSON><PERSON><PERSON><PERSON>",
  "REJECTED",
  "CANC<PERSON>LED",
  "COMPLETED",
];

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface RequestFiltersProps {
  activeFilter: string;
  onFilterChange: (filterValue: string) => void;
  filterValues: { [key: string]: any };
  onInputChange: (field: string, value: any) => void;
  onSearch: () => void;
  onReset: () => void;
}

export function RequestFiltersSection({
  activeFilter,
  onFilterChange,
  filterValues,
  onInputChange,
  onSearch,
  onReset,
}: RequestFiltersProps) {
  return (
    <section aria-labelledby="request-filters-heading" className="mb-4">
      <h2 id="request-filters-heading" className="sr-only">Request Filters</h2>
      <div className="flex flex-wrap gap-4 items-end">
        <div>
          <Label>Status</Label>
          <RadioGroup
            value={activeFilter}
            className="flex flex-wrap gap-x-6 gap-y-2"
            onValueChange={onFilterChange}
            aria-label="Request status filters"
          >
            {filterOptions.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`filter-${option}`} />
                <Label htmlFor={`filter-${option}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        <div>
          <Label htmlFor="filter-projectNo">Project#</Label>
          <Input
            id="filter-projectNo"
            value={filterValues.projectNo || ""}
            onChange={e => onInputChange("projectNo", e.target.value)}
            placeholder="Project number"
            className="w-32"
          />
        </div>
        <div>
          <Label htmlFor="filter-type">Type</Label>
          <Input
            id="filter-type"
            value={filterValues.type || ""}
            onChange={e => onInputChange("type", e.target.value)}
            placeholder="Type"
            className="w-32"
          />
        </div>
        <div className="flex gap-2">
          <Button variant="default" onClick={onSearch}>Search</Button>
          <Button variant="outline" onClick={onReset}>Reset</Button>
        </div>
      </div>
    </section>
  );
}
