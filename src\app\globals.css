/* Material Design Font - Roboto */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Tailwind CSS directives */
/* stylelint-disable at-rule-no-unknown */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* stylelint-enable at-rule-no-unknown */

/* Keep existing fonts as fallbacks */
@font-face {
  font-family: 'Geist Sans';
  src: url('/fonts/geist-sans/Geist[wght].woff2') format('woff2');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Geist Mono';
  src: url('/fonts/geist-mono/GeistMono-VariableFont_wght.woff2') format('woff2'),
       url('/fonts/geist-mono/GeistMono-VariableFont_wght.woff') format('woff');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@layer base {
  :root {
    /* Material Design Light Theme */
    --background: 0 0% 98%; /* Material Light Background */
    --foreground: 0 0% 20%; /* Material Dark Text */

    --card: 0 0% 100%; /* Material Card Background */
    --card-foreground: 0 0% 20%; /* Material Card Text */

    --popover: 0 0% 100%; /* Material Popover Background */
    --popover-foreground: 0 0% 20%; /* Material Popover Text */

    /* Material Blue as Primary */
    --primary: 210 79% 46%; /* Material Blue 500 */
    --primary-foreground: 0 0% 100%; /* White text on primary */

    /* Material Deep Orange as Secondary */
    --secondary: 14 100% 57%; /* Material Deep Orange 500 */
    --secondary-foreground: 0 0% 100%; /* White text on secondary */

    /* Material Gray for muted areas */
    --muted: 0 0% 96%; /* Material Gray 100 */
    --muted-foreground: 0 0% 45%; /* Material Gray 600 */

    /* Material Teal as Accent */
    --accent: 174 100% 29%; /* Material Teal 500 */
    --accent-foreground: 0 0% 100%; /* White text on accent */

    /* Material Red for destructive actions */
    --destructive: 0 100% 50%; /* Material Red 500 */
    --destructive-foreground: 0 0% 100%; /* White text on destructive */

    /* Material Design borders and inputs */
    --border: 0 0% 90%; /* Material Gray 300 */
    --input: 0 0% 90%; /* Material Gray 300 */
    --ring: 210 79% 46%; /* Material Blue 500 (same as primary) */

    /* Chart colors from Material palette */
    --chart-1: 210 79% 46%; /* Material Blue 500 */
    --chart-2: 174 100% 29%; /* Material Teal 500 */
    --chart-3: 14 100% 57%; /* Material Deep Orange 500 */
    --chart-4: 291 64% 42%; /* Material Purple 500 */
    --chart-5: 45 100% 51%; /* Material Amber 500 */

    /* Material Design uses 4px and 8px rounding */
    --radius: 0.25rem;

    /* Sidebar with Material Indigo */
    --sidebar-background: 0 0% 100%; /* White background */
    --sidebar-foreground: 0 0% 20%; /* Dark text */
    --sidebar-primary: 210 79% 46%; /* Material Blue 500 */
    --sidebar-primary-foreground: 0 0% 100%; /* White text */
    --sidebar-accent: 231 48% 48%; /* Material Indigo 500 */
    --sidebar-accent-foreground: 0 0% 100%; /* White text */
    --sidebar-border: 0 0% 90%; /* Material Gray 300 */
    --sidebar-ring: 210 79% 46%; /* Material Blue 500 */

    /* Soft Pastel Colors for Tables and Forms */
    --table-row: 210 100% 97%; /* Very light blue for table rows */
    --table-border: 270 50% 95%; /* Very light lavender for table borders */
    --form-bg: 150 60% 96%; /* Very light mint for form backgrounds */
    --form-border: 30 100% 95%; /* Very light peach for form borders */
  }

  .dark {
    /* Material Design Dark Theme */
    --background: 0 0% 10%; /* Material Dark Background */
    --foreground: 0 0% 98%; /* Material Light Text */

    --card: 0 0% 15%; /* Material Dark Card Background */
    --card-foreground: 0 0% 98%; /* Material Light Card Text */

    --popover: 0 0% 15%; /* Material Dark Popover Background */
    --popover-foreground: 0 0% 98%; /* Material Light Popover Text */

    /* Material Blue as Primary (lighter for dark mode) */
    --primary: 210 79% 65%; /* Material Blue 300 */
    --primary-foreground: 0 0% 0%; /* Black text on light primary */

    /* Material Deep Orange as Secondary (lighter for dark mode) */
    --secondary: 14 100% 67%; /* Material Deep Orange 300 */
    --secondary-foreground: 0 0% 0%; /* Black text on light secondary */

    /* Material Gray for muted areas */
    --muted: 0 0% 20%; /* Material Gray 800 */
    --muted-foreground: 0 0% 70%; /* Material Gray 400 */

    /* Material Teal as Accent (lighter for dark mode) */
    --accent: 174 100% 39%; /* Material Teal 300 */
    --accent-foreground: 0 0% 0%; /* Black text on light accent */

    /* Material Red for destructive actions (lighter for dark mode) */
    --destructive: 0 100% 65%; /* Material Red 300 */
    --destructive-foreground: 0 0% 0%; /* Black text on light destructive */

    /* Material Design borders and inputs for dark mode */
    --border: 0 0% 25%; /* Material Gray 700 */
    --input: 0 0% 25%; /* Material Gray 700 */
    --ring: 210 79% 65%; /* Material Blue 300 (same as primary) */

    /* Chart colors from Material palette (lighter for dark mode) */
    --chart-1: 210 79% 65%; /* Material Blue 300 */
    --chart-2: 174 100% 39%; /* Material Teal 300 */
    --chart-3: 14 100% 67%; /* Material Deep Orange 300 */
    --chart-4: 291 64% 62%; /* Material Purple 300 */
    --chart-5: 45 100% 61%; /* Material Amber 300 */

    /* Sidebar with Material Indigo (darker for dark mode) */
    --sidebar-background: 0 0% 15%; /* Dark background */
    --sidebar-foreground: 0 0% 98%; /* Light text */
    --sidebar-primary: 210 79% 65%; /* Material Blue 300 */
    --sidebar-primary-foreground: 0 0% 0%; /* Black text */
    --sidebar-accent: 231 48% 58%; /* Material Indigo 300 */
    --sidebar-accent-foreground: 0 0% 0%; /* Black text */
    --sidebar-border: 0 0% 25%; /* Material Gray 700 */
    --sidebar-ring: 210 79% 65%; /* Material Blue 300 */

    /* Soft Pastel Colors for Tables and Forms (Dark Mode) */
    --table-row: 210 30% 18%; /* Muted blue for table rows in dark mode */
    --table-border: 270 20% 22%; /* Muted lavender for table borders in dark mode */
    --form-bg: 150 25% 17%; /* Muted mint for form backgrounds in dark mode */
    --form-border: 30 40% 20%; /* Muted peach for form borders in dark mode */
  }
}

@layer base {
  * {
    border-color: var(--border);
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

/* Remove all borders from tables */
@layer components {
  table, thead, tbody, tfoot, tr, th, td {
    border: none !important;
    border-collapse: collapse;
  }

  /* Ensure table elements don't inherit borders */
  .table-no-borders table,
  .table-no-borders thead,
  .table-no-borders tbody,
  .table-no-borders tfoot,
  .table-no-borders tr,
  .table-no-borders th,
  .table-no-borders td {
    border: none !important;
  }
}
