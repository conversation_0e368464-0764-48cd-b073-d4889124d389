#!/usr/bin/env node

/**
 * MCP Playwright Server Tool Discovery
 *
 * This script discovers the actual available tools from the MCP server
 */

const { spawn } = require('child_process');

class MCPToolDiscovery {
  constructor() {
    this.mcpProcess = null;
    this.messageId = 1;
  }

  async startServer() {
    console.log('🚀 Starting MCP Playwright Server for tool discovery...');

    this.mcpProcess = spawn('npx', ['-y', '@executeautomation/playwright-mcp-server'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    let serverOutput = '';

    this.mcpProcess.stdout.on('data', (data) => {
      const output = data.toString();
      serverOutput += output;
      console.log('📡 Server:', output.trim());
    });

    this.mcpProcess.stderr.on('data', (data) => {
      console.error('❌ Error:', data.toString().trim());
    });

    // Wait for server to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    return this.mcpProcess;
  }

  async sendMessage(method, params = {}) {
    const message = {
      jsonrpc: "2.0",
      id: this.messageId++,
      method: method,
      params: params
    };

    console.log(`📤 Sending: ${method}`);

    if (this.mcpProcess && this.mcpProcess.stdin.writable) {
      this.mcpProcess.stdin.write(JSON.stringify(message) + '\n');
    }

    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async discoverTools() {
    console.log('🔍 Discovering available MCP tools...');
    console.log('=' .repeat(50));

    try {
      // Start server
      await this.startServer();

      // Initialize
      await this.sendMessage('initialize', {
        protocolVersion: "2024-11-05",
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: "tool-discovery",
          version: "1.0.0"
        }
      });

      // List tools
      await this.sendMessage('tools/list', {});

      // List resources
      await this.sendMessage('resources/list', {});

      console.log('\n✅ Tool discovery completed!');
      console.log('Check the server output above for available tools and resources.');

    } catch (error) {
      console.error('❌ Discovery failed:', error);
    } finally {
      if (this.mcpProcess) {
        setTimeout(() => {
          this.mcpProcess.kill();
          console.log('🛑 Server stopped');
        }, 2000);
      }
    }
  }
}

// Run discovery
const discovery = new MCPToolDiscovery();
discovery.discoverTools().catch(console.error);