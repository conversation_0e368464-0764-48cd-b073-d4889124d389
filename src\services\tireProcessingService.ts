import axiosInstance from "@/lib/axiosInstance";
import { TireProcessingItem } from "@/types";

// Interface for the backend response
interface CutProcessingWithProcessing {
  id: number;
  cut_id: number;
  processing_id: string;
  quantity: number;
  notes?: string;
  created_date?: string;
  processing?: {
    id: string;
    tire_type: string;
    description1: string;
    description2: string;
    test_code1: string;
    test_code2: string;
    cost: string;
    picture: boolean;
  } | null;
}

// Transform backend response to frontend TireProcessingItem format
const transformToTireProcessingItem = (item: CutProcessingWithProcessing): TireProcessingItem => {
  // Handle cases where processing data is missing (orphaned records)
  if (!item.processing) {
    return {
      id: `CP-${item.id}`, // Prefix to distinguish from other IDs
      description1: `Missing Processing (${item.processing_id})`,
      description2: "Processing data not found",
      price: "0.00",
      tyreType: "Unknown",
      code1: item.processing_id,
      code2: "N/A",
      n: item.quantity,
      picture: false,
    };
  }

  return {
    id: `CP-${item.id}`, // Prefix to distinguish from other IDs
    description1: item.processing.description1,
    description2: item.processing.description2,
    price: item.processing.cost.replace(' €', '').trim(), // Remove € symbol for consistency
    tyreType: item.processing.tire_type,
    code1: item.processing.test_code1,
    code2: item.processing.test_code2,
    n: item.quantity,
    picture: item.processing.picture,
  };
};

/**
 * Get tire processing data for a specific tire (request detail)
 * @param requestDetailId - The ID of the request detail (tire)
 * @param includeOrphaned - Whether to include records with missing processing data (default: true)
 * @returns Promise with array of TireProcessingItem
 */
export const getTireProcessing = async (
  requestDetailId: string,
  includeOrphaned: boolean = true
): Promise<TireProcessingItem[]> => {
  try {
    const response = await axiosInstance.get<CutProcessingWithProcessing[]>(
      `/cut-processing/by-tire/${requestDetailId}`
    );

    // Filter out orphaned records if requested
    const filteredData = includeOrphaned
      ? response.data
      : response.data.filter(item => item.processing !== null);

    // Transform the backend response to match frontend interface
    const transformedData = filteredData.map(transformToTireProcessingItem);

    // Log warning if orphaned records were found
    const orphanedCount = response.data.length - response.data.filter(item => item.processing !== null).length;
    if (orphanedCount > 0) {
      console.warn(`[tireProcessingService] Found ${orphanedCount} orphaned processing records for tire ${requestDetailId}`);
    }

    return transformedData;
  } catch (error) {
    console.error("[tireProcessingService] Error fetching tire processing:", error);
    throw error;
  }
};

/**
 * Create a new tire processing association
 * @param requestDetailId - The ID of the request detail (tire)
 * @param processingId - The ID of the processing to associate
 * @param quantity - The quantity/number for this processing
 * @param notes - Optional notes
 * @returns Promise with the created processing item
 */
export const createTireProcessing = async (
  cutId: number,
  processingId: string,
  quantity: number = 1,
  notes?: string
): Promise<CutProcessingWithProcessing> => {
  try {
    const response = await axiosInstance.post<CutProcessingWithProcessing>("/cut-processing", {
      cut_id: cutId,
      processing_id: processingId,
      quantity,
      notes,
    });

    return response.data;
  } catch (error) {
    console.error("[tireProcessingService] Error creating tire processing:", error);
    throw error;
  }
};

/**
 * Update an existing tire processing association
 * @param cutProcessingId - The ID of the cut processing to update
 * @param quantity - The new quantity
 * @param notes - The new notes
 * @returns Promise with the updated processing item
 */
export const updateTireProcessing = async (
  cutProcessingId: number,
  quantity?: number,
  notes?: string
): Promise<CutProcessingWithProcessing> => {
  try {
    const updateData: any = {};
    if (quantity !== undefined) updateData.quantity = quantity;
    if (notes !== undefined) updateData.notes = notes;

    const response = await axiosInstance.put<CutProcessingWithProcessing>(
      `/cut-processing/${cutProcessingId}`,
      updateData
    );

    return response.data;
  } catch (error) {
    console.error("[tireProcessingService] Error updating tire processing:", error);
    throw error;
  }
};

/**
 * Delete a tire processing association
 * @param cutProcessingId - The ID of the cut processing to delete
 * @returns Promise with the deleted processing item
 */
export const deleteTireProcessing = async (cutProcessingId: number): Promise<CutProcessingWithProcessing> => {
  try {
    const response = await axiosInstance.delete<CutProcessingWithProcessing>(
      `/cut-processing/${cutProcessingId}`
    );

    return response.data;
  } catch (error) {
    console.error("[tireProcessingService] Error deleting tire processing:", error);
    throw error;
  }
};

/**
 * Calculate the total processing quantity for a specific tire
 * @param requestDetailId - The ID of the request detail (tire)
 * @returns Promise with the total processing quantity
 */
export const getTireProcessingTotal = async (requestDetailId: string): Promise<number> => {
  try {
    const processingItems = await getTireProcessing(requestDetailId, false);
    return processingItems.reduce((total, item) => total + (item.n || 0), 0);
  } catch (error) {
    console.error(`[tireProcessingService] Error calculating processing total for tire ${requestDetailId}:`, error);
    return 0; // Return 0 on error to prevent UI issues
  }
};

/**
 * Calculate processing totals for multiple tires
 * @param requestDetailIds - Array of request detail IDs (tire IDs)
 * @returns Promise with a record mapping tire ID to total processing quantity
 */
export const getMultipleTireProcessingTotals = async (
  requestDetailIds: string[]
): Promise<Record<string, number>> => {
  try {
    const totals: Record<string, number> = {};

    // Process all tires in parallel for better performance
    const promises = requestDetailIds.map(async (tireId) => {
      const total = await getTireProcessingTotal(tireId);
      return { tireId, total };
    });

    const results = await Promise.all(promises);

    results.forEach(({ tireId, total }) => {
      totals[tireId] = total;
    });

    return totals;
  } catch (error) {
    console.error('[tireProcessingService] Error calculating multiple tire processing totals:', error);
    // Return empty object with zeros for all tires on error
    return requestDetailIds.reduce((acc, tireId) => {
      acc[tireId] = 0;
      return acc;
    }, {} as Record<string, number>);
  }
};
