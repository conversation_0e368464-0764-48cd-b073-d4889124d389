"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Settings,
  Edit,
  Search,
  Filter,
  X
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  RequestDetailCut,
  RequestDetailCutFormData,
  initialRequestDetailCutFormData,
  CUT_SET_OPTIONS,
  CUT_STATUS_OPTIONS,
  Tire
} from "@/types";
import {
  getRequestDetailCutsByRequestDetail,
  createRequestDetailCut,
  updateRequestDetailCut,
  deleteRequestDetailCut,
} from "@/services/requestDetailCutService";
import { getCutProcessingByCut } from "@/services/cutProcessingService";

interface CutManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tire: Tire | null;
  onOpenProcessingManagement: (cut: RequestDetailCut) => void;
}

export function CutManagementDialog({
  isOpen,
  onClose,
  tire,
  onOpenProcessingManagement,
}: CutManagementDialogProps) {
  const [cuts, setCuts] = useState<RequestDetailCut[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingCut, setIsAddingCut] = useState(false);
  const [editingCut, setEditingCut] = useState<RequestDetailCut | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [newCutForm, setNewCutForm] = useState<RequestDetailCutFormData>(initialRequestDetailCutFormData);
  const { toast } = useToast();

  // Load cuts when dialog opens or tire changes
  useEffect(() => {
    if (isOpen && tire?.id) {
      loadCuts();
    }
  }, [isOpen, tire?.id]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setIsAddingCut(false);
      setEditingCut(null);
      setNewCutForm(initialRequestDetailCutFormData);
      setSearchTerm("");
      setStatusFilter("ALL");
    }
  }, [isOpen]);

  const loadCuts = async () => {
    if (!tire?.id) return;

    try {
      setIsLoading(true);
      const response = await getRequestDetailCutsByRequestDetail(tire.id);

      // Load processing count for each cut
      const cutsWithProcessingCount = await Promise.all(
        response.data.map(async (cut) => {
          try {
            const processingResponse = await getCutProcessingByCut(cut.id);
            return {
              ...cut,
              processingCount: processingResponse.data.length,
              cutProcessing: processingResponse.data,
            };
          } catch (error) {
            console.error(`Error loading processing for cut ${cut.id}:`, error);
            return { ...cut, processingCount: 0 };
          }
        })
      );

      setCuts(cutsWithProcessingCount);
    } catch (error) {
      console.error("Error loading cuts:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i tagli",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCut = () => {
    setNewCutForm({
      ...initialRequestDetailCutFormData,
      idRequestDetails: tire?.id,
    });
    setIsAddingCut(true);
  };

  const handleEditCut = (cut: RequestDetailCut) => {
    setEditingCut(cut);
    setNewCutForm({
      id: cut.id,
      idRequestDetails: cut.idRequestDetails,
      set: cut.set,
      direction: cut.direction,
      notes: cut.notes,
      cutPrice: cut.cutPrice,
      status: cut.status,
    });
    setIsAddingCut(true);
  };

  const handleCancelEdit = () => {
    setIsAddingCut(false);
    setEditingCut(null);
    setNewCutForm(initialRequestDetailCutFormData);
  };

  const handleSubmitCut = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tire?.id) return;

    try {
      setIsSubmitting(true);

      const formDataWithTire = {
        ...newCutForm,
        idRequestDetails: tire.id,
      };

      if (editingCut) {
        await updateRequestDetailCut(editingCut.id, formDataWithTire);
        toast({
          title: "Successo",
          description: "Taglio aggiornato con successo",
        });
      } else {
        await createRequestDetailCut(formDataWithTire);
        toast({
          title: "Successo",
          description: "Taglio creato con successo",
        });
      }

      await loadCuts();
      handleCancelEdit();
    } catch (error) {
      console.error("Error saving cut:", error);
      toast({
        title: "Errore",
        description: "Impossibile salvare il taglio",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCut = async (cutId: number) => {
    try {
      await deleteRequestDetailCut(cutId);
      toast({
        title: "Successo",
        description: "Taglio eliminato con successo",
      });
      await loadCuts();
    } catch (error) {
      console.error("Error deleting cut:", error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare il taglio",
        variant: "destructive",
      });
    }
  };

  const handleFormChange = (field: keyof RequestDetailCutFormData, value: any) => {
    setNewCutForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "COMPLETED": return "default";
      case "IN_PROGRESS": return "secondary";
      case "PENDING": return "outline";
      case "CANCELLED": return "destructive";
      default: return "outline";
    }
  };

  const getStatusText = (status: string) => {
    const statusOption = CUT_STATUS_OPTIONS.find(opt => opt.value === status);
    return statusOption?.label || status;
  };

  // Filter cuts based on search term and status
  const filteredCuts = cuts.filter(cut => {
    const matchesSearch = !searchTerm ||
      (cut.set?.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (cut.notes?.toLowerCase().includes(searchTerm.toLowerCase())) ||
      cut.id.toString().includes(searchTerm);

    const matchesStatus = statusFilter === "ALL" || cut.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (!tire) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Gestione Tagli - {tire.tugNo}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Tire Info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Informazioni Pneumatico</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Tug#:</span> {tire.tugNo}
                </div>
                <div>
                  <span className="font-medium">Sezione:</span> {tire.section}
                </div>
                <div>
                  <span className="font-medium">Progetto#:</span> {tire.projectNo}
                </div>
                <div>
                  <span className="font-medium">Misura:</span> {tire.tireSize}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Search and Filter Controls */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cerca per set, note o ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Tutti gli stati</SelectItem>
                  {CUT_STATUS_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleAddCut} className="shrink-0">
              <Plus className="h-4 w-4 mr-2" />
              Nuovo Taglio
            </Button>
          </div>

          {/* Add/Edit Cut Form */}
          {isAddingCut && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {editingCut ? "Modifica Taglio" : "Nuovo Taglio"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmitCut} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="set">Set</Label>
                      <Select
                        value={newCutForm.set || ""}
                        onValueChange={(value) => handleFormChange("set", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleziona set" />
                        </SelectTrigger>
                        <SelectContent>
                          {CUT_SET_OPTIONS.map(option => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="direction">Direzione</Label>
                      <Input
                        id="direction"
                        type="number"
                        placeholder="0"
                        value={newCutForm.direction || ""}
                        onChange={(e) => handleFormChange("direction", parseInt(e.target.value) || 0)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cutPrice">Prezzo (€)</Label>
                      <Input
                        id="cutPrice"
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        value={newCutForm.cutPrice || ""}
                        onChange={(e) => handleFormChange("cutPrice", parseFloat(e.target.value) || undefined)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="status">Stato</Label>
                      <Select
                        value={newCutForm.status || ""}
                        onValueChange={(value) => handleFormChange("status", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleziona stato" />
                        </SelectTrigger>
                        <SelectContent>
                          {CUT_STATUS_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notes">Note</Label>
                      <Textarea
                        id="notes"
                        placeholder="Note aggiuntive..."
                        value={newCutForm.notes || ""}
                        onChange={(e) => handleFormChange("notes", e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSubmitting}
                    >
                      Annulla
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Salvando..." : editingCut ? "Aggiorna" : "Crea"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Cuts Table */}
          <div className="flex-1 overflow-hidden">
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>Tagli ({filteredCuts.length})</span>
                  {isLoading && <span className="text-sm text-muted-foreground">Caricamento...</span>}
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-auto pt-0">
                {filteredCuts.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {isLoading ? "Caricamento tagli..." : "Nessun taglio trovato"}
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Set</TableHead>
                        <TableHead>Direzione</TableHead>
                        <TableHead>Prezzo</TableHead>
                        <TableHead>Stato</TableHead>
                        <TableHead>Processing</TableHead>
                        <TableHead>Note</TableHead>
                        <TableHead className="w-[120px]">Azioni</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCuts.map((cut) => (
                        <TableRow key={cut.id}>
                          <TableCell className="font-medium">{cut.id}</TableCell>
                          <TableCell>{cut.set || "-"}</TableCell>
                          <TableCell>{cut.direction || 0}</TableCell>
                          <TableCell>
                            {cut.cutPrice ? `€${cut.cutPrice.toFixed(2)}` : "-"}
                          </TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(cut.status || "")}>
                              {getStatusText(cut.status || "")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {cut.processingCount || 0}
                            </Badge>
                          </TableCell>
                          <TableCell className="max-w-[150px] truncate">
                            {cut.notes || "-"}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => onOpenProcessingManagement(cut)}
                                title="Gestisci Processing"
                                className="h-8 w-8"
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleEditCut(cut)}
                                title="Modifica"
                                className="h-8 w-8"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <DeleteButton
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleDeleteCut(cut.id)}
                                title="Elimina Taglio"
                                description={`Sei sicuro di voler eliminare questo taglio? ${cut.processingCount ? `Questo eliminerà anche ${cut.processingCount} operazioni di processing associate.` : ""} Questa azione non può essere annullata.`}
                                confirmText="Elimina"
                                cancelText="Annulla"
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
