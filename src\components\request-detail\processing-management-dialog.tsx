"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Search,
  Edit,
  Save,
  X,
  Calculator,
  FileText
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  RequestDetailCut,
  CutProcessing,
  DialogProcessing,
  ProcessingSearchFilters,
} from "@/types";
import {
  getCutProcessingByCut,
  updateCutProcessing,
  deleteCutProcessing,
  createCutProcessing,
  checkCutProcessingExists,
} from "@/services/cutProcessingService";
import { ProcessingSearchDialog } from "./processing-search-dialog";

interface ProcessingManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  cut: RequestDetailCut | null;
}

export function ProcessingManagementDialog({
  isOpen,
  onClose,
  cut,
}: ProcessingManagementDialogProps) {
  const [processing, setProcessing] = useState<CutProcessing[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingProcessing, setEditingProcessing] = useState<{ [key: number]: boolean }>({});
  const [editValues, setEditValues] = useState<{ [key: number]: { quantity: number; notes: string } }>({});
  const [isProcessingSearchOpen, setIsProcessingSearchOpen] = useState(false);
  const [isSaving, setIsSaving] = useState<{ [key: number]: boolean }>({});
  const { toast } = useToast();

  // Load processing when dialog opens or cut changes
  useEffect(() => {
    if (isOpen && cut?.id) {
      loadProcessing();
      // Validate data consistency after a short delay to allow data to load
      setTimeout(() => {
        validateDataConsistency();
      }, 1000);
    }
  }, [isOpen, cut?.id]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm("");
      setEditingProcessing({});
      setEditValues({});
      setIsSaving({});
    }
  }, [isOpen]);

  const loadProcessing = async () => {
    if (!cut?.id) return;

    try {
      setIsLoading(true);
      const response = await getCutProcessingByCut(cut.id);
      setProcessing(response.data);
    } catch (error) {
      console.error("Error loading processing:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i processing",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Validate data consistency between frontend and backend
  const validateDataConsistency = async () => {
    if (processing.length === 0) return;

    try {
      // Check if any displayed records no longer exist in the backend
      const validationPromises = processing.map(async (proc) => {
        try {
          const exists = await checkCutProcessingExists(proc.id);
          return { id: proc.id, exists };
        } catch (error) {
          console.warn(`Error checking existence of processing ${proc.id}:`, error);
          return { id: proc.id, exists: false };
        }
      });

      const validationResults = await Promise.all(validationPromises);
      const missingRecords = validationResults.filter(result => !result.exists);

      if (missingRecords.length > 0) {
        console.warn('Found missing records:', missingRecords.map(r => r.id));
        toast({
          title: "Dati non sincronizzati",
          description: `${missingRecords.length} processing non più disponibili. Aggiornamento in corso...`,
          variant: "destructive",
        });
        // Refresh data to sync with backend
        await loadProcessing();
      }
    } catch (error) {
      console.error("Error validating data consistency:", error);
    }
  };

  const handleEditProcessing = (processingId: number, currentQuantity: number, currentNotes: string) => {
    setEditingProcessing(prev => ({ ...prev, [processingId]: true }));
    setEditValues(prev => ({
      ...prev,
      [processingId]: {
        quantity: currentQuantity,
        notes: currentNotes || "",
      }
    }));
  };

  const handleCancelEdit = (processingId: number) => {
    setEditingProcessing(prev => ({ ...prev, [processingId]: false }));
    setEditValues(prev => {
      const newValues = { ...prev };
      delete newValues[processingId];
      return newValues;
    });
  };

  const handleSaveEdit = async (processingId: number) => {
    const editValue = editValues[processingId];
    if (!editValue) return;

    try {
      setIsSaving(prev => ({ ...prev, [processingId]: true }));

      await updateCutProcessing(processingId, {
        quantity: editValue.quantity,
        notes: editValue.notes,
      });

      toast({
        title: "Successo",
        description: "Processing aggiornato con successo",
      });

      await loadProcessing();
      setEditingProcessing(prev => ({ ...prev, [processingId]: false }));
      setEditValues(prev => {
        const newValues = { ...prev };
        delete newValues[processingId];
        return newValues;
      });
    } catch (error) {
      console.error("Error updating processing:", error);
      toast({
        title: "Errore",
        description: "Impossibile aggiornare il processing",
        variant: "destructive",
      });
    } finally {
      setIsSaving(prev => ({ ...prev, [processingId]: false }));
    }
  };

  const handleDeleteProcessing = async (processingId: number) => {
    // Validate that the processing record exists in current data
    const processingExists = processing.find(p => p.id === processingId);
    if (!processingExists) {
      console.warn(`Processing with ID ${processingId} not found in current data`);
      toast({
        title: "Errore",
        description: "Il processing selezionato non esiste più. Aggiornamento dati in corso...",
        variant: "destructive",
      });
      // Refresh data to sync with backend
      await loadProcessing();
      return;
    }

    try {
      await deleteCutProcessing(processingId);
      toast({
        title: "Successo",
        description: "Processing rimosso con successo",
      });
      await loadProcessing();
    } catch (error: any) {
      console.error("Error deleting processing:", error);

      // Enhanced error handling for specific HTTP status codes
      if (error?.response?.status === 404) {
        toast({
          title: "Errore",
          description: "Il processing selezionato non esiste più. Aggiornamento dati in corso...",
          variant: "destructive",
        });
        // Refresh data to sync with backend
        await loadProcessing();
      } else if (error?.response?.status === 403) {
        toast({
          title: "Errore",
          description: "Non hai i permessi necessari per eliminare questo processing",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Errore",
          description: "Impossibile rimuovere il processing. Riprova più tardi.",
          variant: "destructive",
        });
      }
    }
  };

  const handleAddProcessing = async (selectedProcessing: DialogProcessing[]) => {
    if (!cut?.id) return;

    try {
      for (const proc of selectedProcessing) {
        await createCutProcessing({
          cutId: cut.id,
          processingId: proc.id,
          quantity: 1,
          notes: `Processing ${proc.description1} aggiunto`,
        });
      }

      toast({
        title: "Successo",
        description: `${selectedProcessing.length} processing aggiunti con successo`,
      });

      await loadProcessing();
      setIsProcessingSearchOpen(false);
    } catch (error) {
      console.error("Error adding processing:", error);
      toast({
        title: "Errore",
        description: "Impossibile aggiungere i processing",
        variant: "destructive",
      });
    }
  };

  const handleEditValueChange = (processingId: number, field: 'quantity' | 'notes', value: any) => {
    setEditValues(prev => ({
      ...prev,
      [processingId]: {
        ...prev[processingId],
        [field]: value,
      }
    }));
  };

  // Filter processing based on search term
  const filteredProcessing = processing.filter(proc => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      proc.processing?.description1?.toLowerCase().includes(searchLower) ||
      proc.processing?.description2?.toLowerCase().includes(searchLower) ||
      proc.processing?.testCode1?.toLowerCase().includes(searchLower) ||
      proc.processing?.testCode2?.toLowerCase().includes(searchLower) ||
      proc.notes?.toLowerCase().includes(searchLower) ||
      proc.id.toString().includes(searchTerm)
    );
  });

  // Calculate total cost
  const totalCost = filteredProcessing.reduce((sum, proc) => {
    const cost = parseFloat(proc.processing?.cost || "0");
    return sum + (cost * proc.quantity);
  }, 0);

  if (!cut) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Gestione Processing - Taglio #{cut.id}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex flex-col space-y-4">
            {/* Cut Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Informazioni Taglio</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">ID:</span> {cut.id}
                  </div>
                  <div>
                    <span className="font-medium">Set:</span> {cut.set || "-"}
                  </div>
                  <div>
                    <span className="font-medium">Direzione:</span> {cut.direction || 0}
                  </div>
                  <div>
                    <span className="font-medium">Prezzo:</span> {cut.cutPrice ? `€${cut.cutPrice.toFixed(2)}` : "-"}
                  </div>
                </div>
                {cut.notes && (
                  <div className="mt-2 text-sm">
                    <span className="font-medium">Note:</span> {cut.notes}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Search and Actions */}
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cerca processing per descrizione, codice test o note..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex items-center gap-2">
                <Calculator className="h-4 w-4 text-muted-foreground" />
                <Badge variant="outline" className="text-sm">
                  Totale: €{totalCost.toFixed(2)}
                </Badge>
              </div>
              <Button onClick={() => setIsProcessingSearchOpen(true)} className="shrink-0">
                <Plus className="h-4 w-4 mr-2" />
                Aggiungi Processing
              </Button>
            </div>

            {/* Processing Table */}
            <div className="flex-1 overflow-hidden">
              <Card className="h-full flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center justify-between">
                    <span>Processing ({filteredProcessing.length})</span>
                    {isLoading && <span className="text-sm text-muted-foreground">Caricamento...</span>}
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-auto pt-0">
                  {filteredProcessing.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      {isLoading ? "Caricamento processing..." : "Nessun processing associato"}
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Descrizione 1</TableHead>
                          <TableHead>Descrizione 2</TableHead>
                          <TableHead>Test Code 1</TableHead>
                          <TableHead>Test Code 2</TableHead>
                          <TableHead>Costo Unit.</TableHead>
                          <TableHead>Quantità</TableHead>
                          <TableHead>Costo Tot.</TableHead>
                          <TableHead>Note</TableHead>
                          <TableHead className="w-[120px]">Azioni</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredProcessing.map((proc) => {
                          const isEditing = editingProcessing[proc.id];
                          const editValue = editValues[proc.id];
                          const unitCost = parseFloat(proc.processing?.cost || "0");
                          const totalCost = unitCost * (editValue?.quantity || proc.quantity);

                          return (
                            <TableRow key={proc.id}>
                              <TableCell className="font-medium">{proc.processingId}</TableCell>
                              <TableCell className="max-w-[150px] truncate">
                                {proc.processing?.description1 || "-"}
                              </TableCell>
                              <TableCell className="max-w-[150px] truncate">
                                {proc.processing?.description2 || "-"}
                              </TableCell>
                              <TableCell>{proc.processing?.testCode1 || "-"}</TableCell>
                              <TableCell>{proc.processing?.testCode2 || "-"}</TableCell>
                              <TableCell>€{unitCost.toFixed(2)}</TableCell>
                              <TableCell>
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    min="1"
                                    value={editValue?.quantity || proc.quantity}
                                    onChange={(e) => handleEditValueChange(proc.id, 'quantity', parseInt(e.target.value) || 1)}
                                    className="w-20"
                                  />
                                ) : (
                                  <Badge variant="secondary">{proc.quantity}</Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">€{totalCost.toFixed(2)}</Badge>
                              </TableCell>
                              <TableCell className="max-w-[150px]">
                                {isEditing ? (
                                  <Textarea
                                    value={editValue?.notes || proc.notes || ""}
                                    onChange={(e) => handleEditValueChange(proc.id, 'notes', e.target.value)}
                                    placeholder="Note..."
                                    rows={2}
                                    className="min-w-[150px]"
                                  />
                                ) : (
                                  <span className="truncate">{proc.notes || "-"}</span>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex space-x-1">
                                  {isEditing ? (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleSaveEdit(proc.id)}
                                        disabled={isSaving[proc.id]}
                                        title="Salva"
                                        className="h-8 w-8"
                                      >
                                        <Save className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleCancelEdit(proc.id)}
                                        disabled={isSaving[proc.id]}
                                        title="Annulla"
                                        className="h-8 w-8"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </>
                                  ) : (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleEditProcessing(proc.id, proc.quantity, proc.notes || "")}
                                        title="Modifica"
                                        className="h-8 w-8"
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                      <DeleteButton
                                        size="icon"
                                        className="h-8 w-8"
                                        onClick={() => handleDeleteProcessing(proc.id)}
                                        title="Rimuovi Processing"
                                        description="Sei sicuro di voler rimuovere questo processing dal taglio? Questa azione non può essere annullata."
                                        confirmText="Rimuovi"
                                        cancelText="Annulla"
                                      />
                                    </>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Processing Search Dialog */}
      <ProcessingSearchDialog
        isOpen={isProcessingSearchOpen}
        onClose={() => setIsProcessingSearchOpen(false)}
        onAddSelectedProcessing={handleAddProcessing}
      />
    </>
  );
}
