# CutRequestStudio User Guide

This guide provides comprehensive instructions for using the CutRequestStudio application for tire request management.

## Getting Started

### Logging In
1. Navigate to the application URL
2. Enter your email address and password
3. Click "Login" to access the dashboard

### User Roles
- **Viewer**: Read-only access to all data
- **Editor**: Can create, edit, and delete requests and related data
- **Admin**: Full access including user management

## Dashboard Overview

The dashboard provides a summary of your tire requests with the following information:
- **Request Status Summary**: Visual overview of completed, in-progress, and pending requests
- **Quick Access**: Direct links to main application sections
- **Recent Activity**: Latest updates and changes

### Navigation
The sidebar contains the main navigation menu:
- **Richiesta**: Request management and creation
- **Dettaglio**: Request details and tire management
- **Lavorazioni**: Processing management (future feature)

## Request Management (Richiesta)

### Viewing Requests
The request management page displays all tire requests in a table format with:
- Request ID and basic information
- Status indicators
- Project details
- Target dates
- Action buttons for editing and deletion

### Creating a New Request
1. Click the "Nuova Richiesta" (New Request) button
2. Fill in the required information:
   - **Request By**: Person making the request
   - **Project No**: Project number
   - **PID Project**: PID project identifier (optional)
   - **Tire Size**: Tire size specification
   - **Request Date**: Date of request creation
   - **Target Date**: Desired completion date
   - **Status**: Current status (pending, in progress, completed)
   - **Type**: Request type (development, production, etc.)
   - **Destination**: Where tires will be delivered
   - **Internal**: Check if internal request
   - **Wish Date**: Preferred completion date
   - **Number of Tires**: Quantity specifications
   - **In Charge Of**: Responsible person
   - **Notes**: Additional information

3. Add file attachments if needed:
   - Click "Aggiungi Allegato" (Add Attachment)
   - Select files from your computer
   - Files will be uploaded and associated with the request

4. Click "Salva" (Save) to create the request

### Editing Requests
1. Click on a request row to select it
2. Modify the information in the details panel
3. Click "Salva" (Save) to update the request

### Filtering and Searching
Use the filter section to find specific requests:
- **Status Filter**: Filter by request status
- **Date Range**: Filter by date ranges
- **Project Filter**: Filter by project number
- **Text Search**: Search in request details

### Deleting Requests
1. Select a request from the table
2. Click the delete button (trash icon)
3. Confirm the deletion in the dialog

## Request Details (Dettaglio)

The detail page provides comprehensive management of individual tire specifications within a request.

### Viewing Request Information
The top section displays:
- Complete request information
- Status and dates
- Project details
- Notes and attachments

### Managing Tires
The tire section shows all tire specifications for the request:

#### Adding Tires
1. Click "Aggiungi Pneumatico" (Add Tire)
2. Choose from existing tire catalog or create new
3. Fill in tire specifications:
   - **TUG Number**: Unique tire identifier
   - **Project Number**: Associated project
   - **Tire Size**: Size specification
   - **Pattern**: Tire pattern
   - **Disposition**: Approval status
   - **Quantity**: Number of tires
   - **Notes**: Additional information

#### Editing Tires
1. Click on a tire row to select it
2. Modify information in the tire form
3. Click "Salva" (Save) to update

#### Copying Tires
1. Select a tire from the list
2. Click "Copia" (Copy) button
3. A duplicate tire will be created with incremented identifiers

#### Tire Processing
1. Select a tire from the list
2. Click "Lavorazione" (Processing) button
3. Navigate to the tire processing detail page

### Managing Cuts
For each tire, you can define cut specifications:

#### Adding Cuts
1. Select a tire
2. In the cuts section, click "Aggiungi Taglio" (Add Cut)
3. Specify cut parameters:
   - **Set**: Set specification (e.g., "1/4")
   - **Direction**: Direction value
   - **Notes**: Additional information

#### Processing Assignment
1. Select a cut
2. Click "Aggiungi Lavorazione" (Add Processing)
3. Search and select from available processing operations
4. Specify quantity and notes

### Page Actions
- **Home**: Return to dashboard
- **Copy & Send**: Create a copy and send for processing
- **Save**: Save all changes made on the page

## Tire Processing Detail

This specialized page manages the processing workflow for individual tires.

### Request Information
Displays the parent request information for context.

### Tire Information
Shows detailed tire specifications with editing capabilities.

### Processing Operations
Lists all processing operations assigned to the tire:

#### Adding Processing
1. Click "Aggiungi Lavorazione" (Add Processing)
2. Search for processing operations using filters:
   - **Tire Type**: Filter by tire type
   - **Description**: Search in descriptions
   - **Test Codes**: Filter by test codes
   - **Cost Range**: Filter by cost
   - **Picture**: Filter by picture availability

3. Select processing operations from search results
4. Specify quantity and notes for each operation

#### Editing Processing
1. Click on a processing row to select it
2. Modify quantity, notes, or other parameters
3. Click "Salva" (Save) to update

#### Removing Processing
1. Select a processing operation
2. Click the delete button
3. Confirm removal

### Processing Search
The search dialog provides advanced filtering:
- **Multiple Filters**: Combine different search criteria
- **Real-time Results**: Results update as you type
- **Bulk Selection**: Select multiple processing operations at once

## File Management

### Uploading Attachments
1. In the request details, click "Aggiungi Allegato" (Add Attachment)
2. Select files from your computer
3. Files are automatically uploaded and associated with the request

### Viewing Attachments
- Attachments are listed in the request details
- Click on attachment names to download
- File size and upload date are displayed

### Managing Attachments
- Delete attachments using the delete button
- Replace attachments by uploading new files with the same purpose

## Reports and Export

### Generating Reports
1. Navigate to the detail page for a request
2. Click "Report" button
3. Select report type and parameters
4. Generate and download the report

### Export Options
- **PDF Reports**: Detailed request and processing reports
- **Excel Export**: Data export for further analysis
- **Print Views**: Printer-friendly versions of pages

## Tips and Best Practices

### Efficient Workflow
1. **Use Templates**: Create template requests for common scenarios
2. **Batch Operations**: Process multiple similar requests together
3. **Regular Updates**: Keep request status updated for better tracking

### Data Quality
1. **Consistent Naming**: Use consistent naming conventions for projects
2. **Complete Information**: Fill in all relevant fields for better tracking
3. **Regular Cleanup**: Remove outdated or cancelled requests

### Search and Navigation
1. **Use Filters**: Leverage filtering to find specific requests quickly
2. **Bookmarks**: Bookmark frequently accessed requests
3. **Keyboard Shortcuts**: Learn keyboard shortcuts for faster navigation

## Troubleshooting

### Common Issues

#### Login Problems
- **Forgot Password**: Contact your administrator for password reset
- **Account Locked**: Check with administrator if account is active
- **Browser Issues**: Clear cache and cookies, try different browser

#### Data Not Saving
- **Check Permissions**: Ensure you have editor or admin role
- **Network Issues**: Check internet connection
- **Validation Errors**: Review error messages and correct invalid data

#### Performance Issues
- **Large Datasets**: Use filters to reduce data load
- **Browser Memory**: Close unnecessary tabs and refresh page
- **Network Speed**: Check internet connection speed

#### File Upload Issues
- **File Size**: Check if file size exceeds limits
- **File Type**: Ensure file type is supported
- **Browser Settings**: Check if browser allows file uploads

### Getting Help
- **Documentation**: Refer to this user guide and technical documentation
- **Support**: Contact your system administrator
- **Training**: Request additional training if needed

## Keyboard Shortcuts

### General Navigation
- `Ctrl + Home`: Return to dashboard
- `Ctrl + S`: Save current form
- `Esc`: Close dialogs and cancel operations

### Request Management
- `Ctrl + N`: Create new request
- `Ctrl + E`: Edit selected request
- `Ctrl + D`: Delete selected request
- `Ctrl + F`: Focus on search/filter

### Form Navigation
- `Tab`: Move to next field
- `Shift + Tab`: Move to previous field
- `Enter`: Submit form (when applicable)

## Data Backup and Recovery

### Personal Data
- **Export Regularly**: Export important data regularly
- **Local Copies**: Keep local copies of critical documents
- **Version Control**: Track changes to important requests

### System Backup
- System administrators handle automatic backups
- Contact administrator for data recovery needs
- Report data loss immediately

## Security and Privacy

### Account Security
- **Strong Passwords**: Use strong, unique passwords
- **Regular Updates**: Change passwords regularly
- **Secure Logout**: Always log out when finished

### Data Privacy
- **Confidential Information**: Handle confidential data appropriately
- **Access Control**: Only access data you're authorized to view
- **Sharing**: Follow company policies for data sharing

### Compliance
- Follow all company policies and procedures
- Report security incidents immediately
- Maintain data integrity and accuracy
