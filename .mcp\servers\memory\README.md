# Memory MCP Server Setup

This directory contains the setup and configuration for the Memory MCP Server, which provides persistent memory using a local knowledge graph.

## Installation Summary

✅ **Server Installed**: `@modelcontextprotocol/server-memory`
✅ **Configuration Added**: Added to `.vscode/mcp-settings.json`
✅ **Directory Created**: `.mcp/servers/memory/`
✅ **Memory File**: `memory.json` (auto-created)

## Server Configuration

The memory server has been configured in `.vscode/mcp-settings.json` with the following settings:

```json
{
  "github.com/modelcontextprotocol/servers/tree/main/src/memory": {
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-memory"],
    "env": {
      "MEMORY_FILE_PATH": ".mcp/servers/memory/memory.json"
    },
    "description": "Knowledge Graph Memory Server for persistent memory using a local knowledge graph",
    "capabilities": [
      "knowledge_graph",
      "persistent_memory",
      "entity_management",
      "relationship_tracking",
      "observation_storage"
    ]
  }
}
```

## Available Tools

The Memory MCP Server provides the following tools:

### Core Entity Management
- **`create_entities`** - Create multiple new entities in the knowledge graph
- **`delete_entities`** - Remove entities and their relations (cascading deletion)
- **`open_nodes`** - Retrieve specific nodes by name

### Relationship Management
- **`create_relations`** - Create multiple new relations between entities
- **`delete_relations`** - Remove specific relations from the graph

### Observation Management
- **`add_observations`** - Add new observations to existing entities
- **`delete_observations`** - Remove specific observations from entities

### Query and Retrieval
- **`read_graph`** - Read the entire knowledge graph
- **`search_nodes`** - Search for nodes based on query (searches names, types, observations)

## Core Concepts

### Entities
Primary nodes in the knowledge graph with:
- Unique name (identifier)
- Entity type (e.g., "person", "organization", "event")
- List of observations

### Relations
Directed connections between entities, stored in active voice:
```json
{
  "from": "John_Smith",
  "to": "Anthropic",
  "relationType": "works_at"
}
```

### Observations
Discrete pieces of information about entities:
- Stored as strings
- Attached to specific entities
- Atomic (one fact per observation)

## Usage Examples

### Creating Entities
```json
{
  "entities": [
    {
      "name": "CutRequestStudio",
      "entityType": "project",
      "observations": [
        "Tire request management application",
        "Built with React and Next.js",
        "Uses TypeScript"
      ]
    }
  ]
}
```

### Creating Relations
```json
{
  "relations": [
    {
      "from": "Giovanni_Rossi",
      "to": "CutRequestStudio",
      "relationType": "develops"
    }
  ]
}
```

### Adding Observations
```json
{
  "observations": [
    {
      "entityName": "Giovanni_Rossi",
      "contents": [
        "Prefers morning meetings",
        "Expert in React development"
      ]
    }
  ]
}
```

## Use Cases for CutRequestStudio

The Memory MCP Server can enhance CutRequestStudio by:

1. **User Context Management**
   - Remember user preferences and settings
   - Track user behavior patterns
   - Store personalized configurations

2. **Relationship Tracking**
   - Map relationships between users, departments, and requests
   - Track tire processing workflows
   - Maintain organizational structure

3. **Knowledge Storage**
   - Store tire specifications and processing knowledge
   - Remember common issues and solutions
   - Track equipment and maintenance information

4. **Session Continuity**
   - Maintain context across different sessions
   - Remember previous conversations and decisions
   - Provide personalized recommendations

## Files in this Directory

- **`README.md`** - This documentation file
- **`memory.json`** - Knowledge graph storage file (auto-created)
- **`demo-memory-capabilities.js`** - Demonstration script
- **`test-memory-server.js`** - Comprehensive test script

## Next Steps

1. **Restart VS Code** to activate the MCP connection
2. **Test the connection** using MCP tools in VS Code
3. **Start building knowledge** by creating entities for your project
4. **Integrate with workflows** to automatically capture and store information

## Troubleshooting

If the server doesn't connect:
1. Ensure VS Code is restarted after configuration changes
2. Check that Node.js and npm are properly installed
3. Verify the memory.json file has proper permissions
4. Check VS Code's MCP extension logs for errors

## Memory File Location

The knowledge graph data is stored in:
```
.mcp/servers/memory/memory.json
```

This file contains all entities, relations, and observations in JSON format.