#!/usr/bin/env python3
"""
Test script per debuggare il problema di salvataggio dei request details.
Simula esattamente quello che il frontend invia al backend.
"""

import requests
import json
from datetime import datetime

# Configurazione
BASE_URL = "http://localhost:8000"
REQUEST_ID = "REQ001"  # ID esistente nel database

# Credenziali di test (basate su backend/check_users.py)
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "admin123"

def get_auth_token():
    """Ottiene un token di autenticazione"""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={
                "username": TEST_EMAIL,
                "password": TEST_PASSWORD
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"❌ Auth failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Auth error: {e}")
        return None

def get_auth_headers():
    """Ottiene gli headers di autenticazione"""
    token = get_auth_token()
    if token:
        return {"Authorization": f"Bearer {token}"}
    return {}

# Payload che il frontend invia (basato su handleSavePageClick)
payload = {
    "id": REQUEST_ID,
    "request_by": "giovanni.rossi",
    "project_no": "PRJ-TEST",
    "pid_project": "PID-TEST",
    "tire_size": "225/45R17",
    "request_date": datetime.now().isoformat(),
    "target_date": datetime.now().isoformat(),
    "status": "DRAFT",
    "type": "NEWDEV",
    "total_n": 2,
    "destination": "PLANT_A",
    "internal": False,
    "wish_date": None,
    "num_pneus_set": 4,
    "num_pneus_set_unit": "SET",
    "in_charge_of": "Test Department",
    "note": "Test note",
    "attachments": [],
    "request_details": [
        {
            "id": "detail-1",
            "request_id": REQUEST_ID,
            "tug_number": "TUG-TEST-NEW",
            "section": "FROM_MANAGEMENT",
            "project_number": "PRJ-TEST",
            "spec_number": "SPEC-TEST",
            "tire_size": "225/45R17",
            "pattern": "TEST_PATTERN",
            "note": "Added via management...",
            "disposition": "AVAILABLE",
            "process_number": 0
        },
        {
            "id": "detail-2",
            "request_id": REQUEST_ID,
            "tug_number": "TUG-TEST-NEW",
            "section": "FROM_MANAGEMENT",
            "project_number": "PRJ-TEST",
            "spec_number": "SPEC-TEST",
            "tire_size": "225/45R17",
            "pattern": "Potenza S007",
            "note": "Added via management...",
            "disposition": "AVAILABLE",
            "process_number": 0
        }
    ]
}

def test_save_request():
    """Test del salvataggio della richiesta"""
    print("🔍 Testing save request endpoint...")
    print(f"URL: {BASE_URL}/api/v1/requests/{REQUEST_ID}/save")
    print(f"Payload keys: {list(payload.keys())}")
    print(f"Request details count: {len(payload['request_details'])}")

    # Ottieni headers di autenticazione
    auth_headers = get_auth_headers()
    if not auth_headers:
        print("❌ Could not get authentication token")
        return

    try:
        # Prima verifica se la richiesta esiste
        print("\n📋 Checking if request exists...")
        get_response = requests.get(f"{BASE_URL}/api/v1/requests/{REQUEST_ID}", headers=auth_headers)
        if get_response.status_code == 404:
            print("❌ Request not found, creating it first...")
            create_payload = {k: v for k, v in payload.items() if k != 'request_details'}
            create_headers = {**auth_headers, "Content-Type": "application/json"}
            create_response = requests.post(f"{BASE_URL}/api/v1/requests", json=create_payload, headers=create_headers)
            print(f"Create response: {create_response.status_code}")
            if create_response.status_code not in [200, 201]:
                print(f"Create error: {create_response.text}")
                return
        else:
            print(f"✅ Request exists: {get_response.status_code}")

        # Ora testa il salvataggio
        print("\n💾 Testing save endpoint...")
        save_headers = {**auth_headers, "Content-Type": "application/json"}
        response = requests.post(
            f"{BASE_URL}/api/v1/requests/{REQUEST_ID}/save",
            json=payload,
            headers=save_headers
        )

        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ Save successful!")
            result = response.json()
            print(f"Response keys: {list(result.keys())}")
        else:
            print("❌ Save failed!")
            print(f"Error response: {response.text}")

            # Prova a parsare l'errore JSON se possibile
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error as JSON")

    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the backend running on port 8000?")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_schema_validation():
    """Test della validazione dello schema RequestDetailBase"""
    print("\n🔍 Testing schema validation...")

    # Test con un singolo request detail
    single_detail = payload["request_details"][0]
    print(f"Single detail keys: {list(single_detail.keys())}")

    # Verifica che tutti i campi richiesti siano presenti
    required_fields = [
        "id", "request_id", "tug_number", "section", "project_number",
        "spec_number", "tire_size", "pattern", "note", "disposition", "process_number"
    ]

    missing_fields = [field for field in required_fields if field not in single_detail]
    if missing_fields:
        print(f"❌ Missing required fields: {missing_fields}")
    else:
        print("✅ All required fields present")

    # Verifica i tipi di dati
    print(f"process_number type: {type(single_detail['process_number'])}")
    print(f"process_number value: {single_detail['process_number']}")

if __name__ == "__main__":
    print("🚀 Starting save request debug test...")
    test_schema_validation()
    test_save_request()
    print("\n✅ Test completed!")
