/**
 * PERFORMANCE OPTIMIZATION LAYER - PRIORITÀ 3
 *
 * Implementa ottimizzazioni per:
 * - Code splitting automatico per componenti generici
 * - Lazy loading dei dialog components
 * - Memoization delle trasformazioni dati
 * - Caching intelligente delle chiamate API
 * - Tree shaking ottimizzato
 *
 * Caratteristiche:
 * - Integrazione con tutti i componenti PRIORITÀ 1 e 2
 * - Monitoring delle performance in real-time
 * - Automatic bundle analysis
 * - Memory leak detection
 * - Progressive loading strategies
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';

// ============================================================================
// CORE PERFORMANCE TYPES
// ============================================================================

export interface PerformanceMetrics {
  /** Component render time in milliseconds */
  renderTime: number;
  /** Memory usage in MB */
  memoryUsage: number;
  /** Bundle size in KB */
  bundleSize?: number;
  /** API response time in milliseconds */
  apiResponseTime?: number;
  /** Cache hit ratio (0-1) */
  cacheHitRatio?: number;
  /** Number of re-renders */
  rerenderCount: number;
  /** Timestamp of measurement */
  timestamp: Date;
}

export interface CacheConfig {
  /** Cache key */
  key: string;
  /** Time to live in milliseconds */
  ttl: number;
  /** Maximum cache size */
  maxSize?: number;
  /** Whether to persist cache across sessions */
  persistent?: boolean;
  /** Custom serialization function */
  serialize?: (data: any) => string;
  /** Custom deserialization function */
  deserialize?: (data: string) => any;
}

export interface LazyLoadConfig {
  /** Intersection observer threshold */
  threshold?: number;
  /** Root margin for intersection observer */
  rootMargin?: string;
  /** Fallback component while loading */
  fallback?: React.ComponentType;
  /** Error component if loading fails */
  errorComponent?: React.ComponentType<{ error: Error; retry: () => void }>;
  /** Preload condition */
  preload?: boolean;
}

export interface MemoizationConfig {
  /** Dependencies for memoization */
  deps: any[];
  /** Custom equality function */
  isEqual?: (a: any, b: any) => boolean;
  /** Maximum cache size for memoized results */
  maxCacheSize?: number;
  /** Time to live for memoized results */
  ttl?: number;
}

// ============================================================================
// MEMORY MANAGEMENT
// ============================================================================

/**
 * Memory management utilities for performance optimization
 */
class MemoryManager {
  private static readonly MEMORY_THRESHOLD_MB = 100;
  private static readonly CHECK_INTERVAL_MS = 30000; // 30 seconds
  private static memoryCheckInterval: NodeJS.Timeout | null = null;
  private static memoryUsageHistory: number[] = [];

  /**
   * Starts memory monitoring
   */
  static startMemoryMonitoring(): void {
    if (typeof window === 'undefined' || this.memoryCheckInterval) return;

    this.memoryCheckInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.CHECK_INTERVAL_MS);
  }

  /**
   * Stops memory monitoring
   */
  static stopMemoryMonitoring(): void {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }
  }

  /**
   * Gets current memory usage
   */
  static getCurrentMemoryUsage(): number {
    if (typeof window === 'undefined' || !(performance as any).memory) {
      return 0;
    }

    const memory = (performance as any).memory;
    return memory.usedJSHeapSize / (1024 * 1024); // Convert to MB
  }

  /**
   * Checks memory usage and triggers cleanup if needed
   */
  private static checkMemoryUsage(): void {
    const currentUsage = this.getCurrentMemoryUsage();
    this.memoryUsageHistory.push(currentUsage);

    // Keep only last 10 measurements
    if (this.memoryUsageHistory.length > 10) {
      this.memoryUsageHistory.shift();
    }

    // Trigger cleanup if memory usage is high
    if (currentUsage > this.MEMORY_THRESHOLD_MB) {
      this.triggerMemoryCleanup();
    }

    // Detect memory leaks (consistently increasing memory usage)
    if (this.detectMemoryLeak()) {
      console.warn('Potential memory leak detected', {
        currentUsage,
        history: this.memoryUsageHistory
      });
    }
  }

  /**
   * Detects potential memory leaks
   */
  private static detectMemoryLeak(): boolean {
    if (this.memoryUsageHistory.length < 5) return false;

    const recent = this.memoryUsageHistory.slice(-5);
    const isIncreasing = recent.every((value, index) =>
      index === 0 || value > recent[index - 1]
    );

    return isIncreasing && (recent[recent.length - 1] - recent[0]) > 20; // 20MB increase
  }

  /**
   * Triggers memory cleanup
   */
  private static triggerMemoryCleanup(): void {
    // Clear caches
    ApiCache.clearExpired();
    MemoizationCache.clearExpired();

    // Force garbage collection if available
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }

    console.info('Memory cleanup triggered', {
      memoryUsage: this.getCurrentMemoryUsage()
    });
  }
}

// ============================================================================
// API CACHING SYSTEM
// ============================================================================

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * Intelligent API caching system
 */
class ApiCache {
  private static cache = new Map<string, CacheEntry<any>>();
  private static readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private static readonly MAX_CACHE_SIZE = 100;

  /**
   * Gets cached data
   */
  static get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return entry.data;
  }

  /**
   * Sets cached data
   */
  static set<T>(key: string, data: T, config: Partial<CacheConfig> = {}): void {
    const ttl = config.ttl || this.DEFAULT_TTL;

    // Ensure cache size limit
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastUsed();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      accessCount: 1,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);

    // Persist if configured
    if (config.persistent) {
      this.persistToStorage(key, entry, config);
    }
  }

  /**
   * Clears expired entries
   */
  static clearExpired(): void {
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Evicts least recently used entry
   */
  private static evictLeastUsed(): void {
    let lruKey: string | null = null;
    let lruTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
    }
  }

  /**
   * Persists cache entry to storage
   */
  private static persistToStorage(key: string, entry: CacheEntry<any>, config: Partial<CacheConfig>): void {
    try {
      const serialized = config.serialize ?
        config.serialize(entry.data) :
        JSON.stringify(entry.data);

      localStorage.setItem(`cache_${key}`, JSON.stringify({
        ...entry,
        data: serialized
      }));
    } catch (error) {
      console.warn('Failed to persist cache entry:', error);
    }
  }

  /**
   * Loads cache entry from storage
   */
  static loadFromStorage(key: string, config: Partial<CacheConfig> = {}): any {
    try {
      const stored = localStorage.getItem(`cache_${key}`);
      if (!stored) return null;

      const entry = JSON.parse(stored);

      // Check if expired
      if (Date.now() - entry.timestamp > entry.ttl) {
        localStorage.removeItem(`cache_${key}`);
        return null;
      }

      const data = config.deserialize ?
        config.deserialize(entry.data) :
        JSON.parse(entry.data);

      // Restore to memory cache
      this.cache.set(key, {
        ...entry,
        data,
        lastAccessed: Date.now()
      });

      return data;
    } catch (error) {
      console.warn('Failed to load cache entry from storage:', error);
      return null;
    }
  }

  /**
   * Gets cache statistics
   */
  static getStats(): { size: number; hitRatio: number; memoryUsage: number } {
    const totalAccess = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.accessCount, 0);

    const memoryUsage = JSON.stringify(Array.from(this.cache.entries())).length / 1024; // KB

    return {
      size: this.cache.size,
      hitRatio: totalAccess > 0 ? this.cache.size / totalAccess : 0,
      memoryUsage
    };
  }

  /**
   * Clears all cache
   */
  static clear(): void {
    this.cache.clear();
  }
}

// ============================================================================
// MEMOIZATION CACHE
// ============================================================================

interface MemoEntry<T> {
  result: T;
  deps: any[];
  timestamp: number;
  ttl?: number;
}

/**
 * Advanced memoization cache for expensive computations
 */
class MemoizationCache {
  private static cache = new Map<string, MemoEntry<any>>();
  private static readonly MAX_CACHE_SIZE = 50;

  /**
   * Memoizes a function result
   */
  static memoize<T>(
    key: string,
    fn: () => T,
    config: MemoizationConfig
  ): T {
    const existing = this.cache.get(key);

    // Check if we have a valid cached result
    if (existing && this.areDepsEqual(existing.deps, config.deps, config.isEqual)) {
      // Check TTL if specified
      if (config.ttl && Date.now() - existing.timestamp > config.ttl) {
        this.cache.delete(key);
      } else {
        return existing.result;
      }
    }

    // Compute new result
    const result = fn();

    // Ensure cache size limit
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldest();
    }

    // Cache the result
    this.cache.set(key, {
      result,
      deps: [...config.deps],
      timestamp: Date.now(),
      ttl: config.ttl
    });

    return result;
  }

  /**
   * Checks if dependencies are equal
   */
  private static areDepsEqual(
    oldDeps: any[],
    newDeps: any[],
    isEqual?: (a: any, b: any) => boolean
  ): boolean {
    if (oldDeps.length !== newDeps.length) return false;

    for (let i = 0; i < oldDeps.length; i++) {
      if (isEqual) {
        if (!isEqual(oldDeps[i], newDeps[i])) return false;
      } else {
        if (oldDeps[i] !== newDeps[i]) return false;
      }
    }

    return true;
  }

  /**
   * Evicts oldest entry
   */
  private static evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clears expired entries
   */
  static clearExpired(): void {
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.ttl && now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clears all memoized results
   */
  static clear(): void {
    this.cache.clear();
  }
}

// ============================================================================
// PERFORMANCE HOOKS
// ============================================================================

/**
 * Hook for measuring component render performance
 */
function useRenderPerformance(componentName: string) {
  const renderCount = useRef(0);
  const startTime = useRef(0);

  useEffect(() => {
    startTime.current = performance.now();
    renderCount.current++;
  });

  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;

    if (renderTime > 16) { // More than one frame (60fps)
      console.warn(`Slow render detected in ${componentName}:`, {
        renderTime,
        renderCount: renderCount.current
      });
    }

    // Report metrics
    PerformanceMonitor.recordMetric({
      renderTime,
      memoryUsage: MemoryManager.getCurrentMemoryUsage(),
      rerenderCount: renderCount.current,
      timestamp: new Date()
    });
  });

  return {
    renderCount: renderCount.current,
    measureRender: (fn: () => void) => {
      const start = performance.now();
      fn();
      const end = performance.now();
      return end - start;
    }
  };
}

/**
 * Hook for optimized API calls with caching
 */
function useOptimizedApiCall<T>(
  apiCall: () => Promise<T>,
  cacheKey: string,
  config: Partial<CacheConfig> = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (forceRefresh = false) => {
    // Check cache first
    if (!forceRefresh) {
      const cached = ApiCache.get<T>(cacheKey);
      if (cached) {
        setData(cached);
        return cached;
      }
    }

    setLoading(true);
    setError(null);

    try {
      const startTime = performance.now();
      const result = await apiCall();
      const endTime = performance.now();

      // Cache the result
      ApiCache.set(cacheKey, result, config);
      setData(result);

      // Record performance metrics
      PerformanceMonitor.recordMetric({
        apiResponseTime: endTime - startTime,
        renderTime: 0,
        memoryUsage: MemoryManager.getCurrentMemoryUsage(),
        rerenderCount: 0,
        timestamp: new Date()
      });

      return result;
    } catch (err) {
      const error = err as Error;
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, cacheKey, config]);

  // Load from persistent storage on mount
  useEffect(() => {
    if (config.persistent) {
      const cached = ApiCache.loadFromStorage(cacheKey, config);
      if (cached) {
        setData(cached);
      }
    }
  }, [cacheKey, config]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    clearCache: () => ApiCache.set(cacheKey, null as any, { ttl: 0 })
  };
}

/**
 * Hook for advanced memoization
 */
function useAdvancedMemo<T>(
  factory: () => T,
  config: MemoizationConfig
): T {
  const key = useMemo(() =>
    `memo_${JSON.stringify(config.deps)}_${Date.now()}`,
    [config.deps]
  );

  return useMemo(() =>
    MemoizationCache.memoize(key, factory, config),
    [key, factory, config]
  );
}

/**
 * Hook for lazy loading components
 */
function useLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
) {
  const [Component, setComponent] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);

  const loadComponent = useCallback(async () => {
    if (Component || loading) return;

    setLoading(true);
    setError(null);

    try {
      const module = await importFn();
      setComponent(() => module.default);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [importFn, Component, loading]);

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (config.preload) {
      loadComponent();
      return;
    }

    if (!elementRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadComponent();
          observerRef.current?.disconnect();
        }
      },
      {
        threshold: config.threshold || 0.1,
        rootMargin: config.rootMargin || '50px'
      }
    );

    observerRef.current.observe(elementRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [loadComponent, config]);

  return {
    Component,
    loading,
    error,
    elementRef,
    retry: loadComponent
  };
}

// ============================================================================
// PERFORMANCE MONITOR
// ============================================================================

/**
 * Performance monitoring and reporting system
 */
class PerformanceMonitor {
  private static metrics: PerformanceMetrics[] = [];
  private static readonly MAX_METRICS = 1000;

  /**
   * Records a performance metric
   */
  static recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);

    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics.shift();
    }

    // Log slow operations
    if (metric.renderTime > 50) {
      console.warn('Slow render detected:', metric);
    }

    if (metric.apiResponseTime && metric.apiResponseTime > 2000) {
      console.warn('Slow API response detected:', metric);
    }
  }

  /**
   * Gets performance statistics
   */
  static getStats(): {
    avgRenderTime: number;
    avgApiResponseTime: number;
    avgMemoryUsage: number;
    totalRerenders: number;
    cacheStats: ReturnType<typeof ApiCache.getStats>;
  } {
    const renderTimes = this.metrics.map(m => m.renderTime).filter(t => t > 0);
    const apiTimes = this.metrics.map(m => m.apiResponseTime).filter((t): t is number => t !== undefined && t > 0);
    const memoryUsages = this.metrics.map(m => m.memoryUsage).filter(m => m > 0);

    return {
      avgRenderTime: renderTimes.length > 0 ?
        renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length : 0,
      avgApiResponseTime: apiTimes.length > 0 ?
        apiTimes.reduce((a, b) => a + b, 0) / apiTimes.length : 0,
      avgMemoryUsage: memoryUsages.length > 0 ?
        memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length : 0,
      totalRerenders: this.metrics.reduce((sum, m) => sum + m.rerenderCount, 0),
      cacheStats: ApiCache.getStats()
    };
  }

  /**
   * Exports performance data for analysis
   */
  static exportData(): string {
    return JSON.stringify({
      metrics: this.metrics,
      stats: this.getStats(),
      timestamp: new Date().toISOString()
    }, null, 2);
  }

  /**
   * Clears all metrics
   */
  static clear(): void {
    this.metrics = [];
  }
}

// ============================================================================
// BUNDLE ANALYZER
// ============================================================================

/**
 * Bundle analysis utilities
 */
class BundleAnalyzer {

  /**
   * Analyzes current bundle size
   */
  static async analyzeBundleSize(): Promise<{
    totalSize: number;
    gzippedSize: number;
    chunks: Array<{ name: string; size: number }>;
  }> {
    // This would integrate with webpack-bundle-analyzer in a real implementation
    // For now, provide estimated metrics

    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const chunks: Array<{ name: string; size: number }> = [];
    let totalSize = 0;

    for (const script of scripts) {
      try {
        const response = await fetch((script as HTMLScriptElement).src, { method: 'HEAD' });
        const size = parseInt(response.headers.get('content-length') || '0', 10);
        const name = (script as HTMLScriptElement).src.split('/').pop() || 'unknown';

        chunks.push({ name, size });
        totalSize += size;
      } catch (error) {
        console.warn('Failed to analyze chunk size:', error);
      }
    }

    return {
      totalSize,
      gzippedSize: Math.round(totalSize * 0.3), // Estimated gzip ratio
      chunks
    };
  }

  /**
   * Identifies unused code
   */
  static identifyUnusedCode(): string[] {
    const unusedSelectors: string[] = [];

    // Analyze CSS
    for (const stylesheet of Array.from(document.styleSheets)) {
      try {
        for (const rule of Array.from(stylesheet.cssRules || [])) {
          if (rule instanceof CSSStyleRule) {
            if (!document.querySelector(rule.selectorText)) {
              unusedSelectors.push(rule.selectorText);
            }
          }
        }
      } catch (error) {
        // Cross-origin stylesheets can't be analyzed
      }
    }

    return unusedSelectors;
  }
}

// ============================================================================
// INITIALIZATION
// ============================================================================

/**
 * Initializes performance optimization system
 */
export function initializePerformanceOptimization(): void {
  // Start memory monitoring
  MemoryManager.startMemoryMonitoring();

  // Set up performance observer
  if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure') {
          PerformanceMonitor.recordMetric({
            renderTime: entry.duration,
            memoryUsage: MemoryManager.getCurrentMemoryUsage(),
            rerenderCount: 0,
            timestamp: new Date()
          });
        }
      }
    });

    observer.observe({ entryTypes: ['measure', 'navigation'] });
  }

  console.info('Performance optimization system initialized');
}

/**
 * Cleanup performance optimization system
 */
export function cleanupPerformanceOptimization(): void {
  MemoryManager.stopMemoryMonitoring();
  ApiCache.clear();
  MemoizationCache.clear();
  PerformanceMonitor.clear();
}

// Export all components
export {
  MemoryManager,
  ApiCache,
  MemoizationCache,
  PerformanceMonitor,
  BundleAnalyzer,
  useRenderPerformance,
  useOptimizedApiCall,
  useAdvancedMemo,
  useLazyComponent
};