{"mcp.servers": {"github.com/executeautomation/mcp-playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}, "description": "Playwright MCP Server for browser automation, testing, and web scraping", "capabilities": ["browser_automation", "screenshot_capture", "web_scraping", "javascript_execution", "test_generation"]}, "github.com/modelcontextprotocol/servers/tree/main/src/memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": ".mcp/servers/memory/memory.json"}, "description": "Knowledge Graph Memory Server for persistent memory using a local knowledge graph", "capabilities": ["knowledge_graph", "persistent_memory", "entity_management", "relationship_tracking", "observation_storage"]}}, "mcp.enableLogging": true, "mcp.logLevel": "info"}