"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format } from "date-fns";
import type { AppRequest, Tire } from "@/types";
import { cn } from "@/lib/utils";

interface DettaglioReportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  requestData: AppRequest | null;
  tiresData: Tire[];
}

const ReportItem: React.FC<{ label: string; value: React.ReactNode; className?: string; isHeader?: boolean }> = ({ label, value, classN<PERSON>, isHeader }) => (
  <div className={cn("grid grid-cols-3 gap-2 py-1.5 border-b border-border/20 last:border-b-0", className)}>
    <Label className={cn("font-medium col-span-1", isHeader ? "text-base text-foreground" : "text-sm text-muted-foreground")}>{label}:</Label>
    <div className={cn("col-span-2 break-words", isHeader ? "text-base font-semibold text-foreground" : "text-sm")}>
      {value === undefined || value === null || (typeof value === 'string' && value.trim() === "") ?
        <span className="italic text-muted-foreground/70">N/A</span> :
        value
      }
    </div>
  </div>
);


export function DettaglioReportDialog({ isOpen, onClose, requestData, tiresData }: DettaglioReportDialogProps) {
  if (!isOpen) {
    return null;
  }

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return null;
    try {
      return format(new Date(date), "PPP p");
    } catch (error) {
      return "Data non valida";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
      <DialogContent className="max-w-3xl w-[90vw] md:w-full max-h-[85vh] flex flex-col p-0">
        <DialogHeader className="p-4 border-b sticky top-0 bg-background z-10">
          <DialogTitle className="text-xl font-semibold text-center">
            Riepilogo Dettaglio Richiesta e Pneumatici
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-grow p-4 space-y-6">
          {requestData ? (
            <section>
              <h3 className="text-lg font-semibold mb-3 text-primary">Dettagli Richiesta</h3>
              <div className="space-y-0.5 rounded-md p-3 bg-card">
                <ReportItem label="ID Richiesta" value={requestData.id} isHeader />
                <ReportItem label="Stato" value={requestData.status} />
                <ReportItem label="Richiesta Da" value={requestData.requestBy} />
                <ReportItem label="Tipo" value={requestData.type} />
                <ReportItem label="Data Richiesta" value={formatDate(requestData.requestDate)} />
                <ReportItem label="Data Target" value={formatDate(requestData.targetDate)} />
                <ReportItem label="Data Desiderata" value={formatDate(requestData.wishDate)} />
                <ReportItem label="PID/Progetto" value={requestData.pidProject || requestData.projectNo} />
                <ReportItem label="Misura Pneumatici (Richiesta)" value={requestData.tireSize} />
                <ReportItem label="Totale N° Pneumatici (Richiesta)" value={requestData.totalN} />
                <ReportItem label="Num. Pneus x Set" value={`${requestData.numPneusSet ?? 'N/A'} (${requestData.numPneusSetUnit || 'SET'})`} />
                <ReportItem label="Destinazione" value={requestData.destination} />
                <ReportItem label="Interna" value={requestData.internal ? "Sì" : "No"} />
                <ReportItem label="Responsabile" value={requestData.inChargeOf} />
                <ReportItem
                  label="Note Richiesta"
                  value={requestData.note ? <p className="whitespace-pre-wrap text-sm bg-muted/30 p-2 rounded-sm">{requestData.note}</p> : null}
                  className="items-start"
                />
              </div>
            </section>
          ) : (
            <p className="text-muted-foreground text-center">Nessun dato di richiesta disponibile.</p>
          )}

          <Separator className="my-6" />

          <section>
            <h3 className="text-lg font-semibold mb-3 text-primary">Pneumatici Associati ({tiresData.length})</h3>
            {tiresData.length > 0 ? (
              <div className="rounded-md bg-card">
                <Table>
                  <TableHeader className="bg-card">
                    <TableRow>
                      <TableHead>Tug#</TableHead>
                      <TableHead>Progetto</TableHead>
                      <TableHead>Spec#</TableHead>
                      <TableHead>Misura</TableHead>
                      <TableHead>Pattern</TableHead>
                      <TableHead>Disposizione</TableHead>
                      <TableHead className="text-right">N°</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tiresData.map((tire) => (
                      <TableRow key={tire.id}>
                        <TableCell>{tire.tugNo}</TableCell>
                        <TableCell>{tire.projectNo}</TableCell>
                        <TableCell>{tire.specNo}</TableCell>
                        <TableCell>{tire.tireSize}</TableCell>
                        <TableCell>{tire.pattern}</TableCell>
                        <TableCell>{tire.disposition}</TableCell>
                        <TableCell className="text-right">{tire.quantity}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <p className="text-muted-foreground text-center">Nessun pneumatico associato a questa richiesta.</p>
            )}
          </section>
        </ScrollArea>

        <DialogFooter className="p-4 border-t sticky bottom-0 bg-background z-10">
          <DialogClose asChild>
            <Button variant="default">Chiudi</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
