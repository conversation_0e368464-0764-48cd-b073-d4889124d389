import sqlite3
import random
from datetime import datetime, timed<PERSON><PERSON>

def populate_request_detail_cut():
    """
    Popola la tabella REQUEST_DETAIL_CUT con 50 righe di esempio
    e crea associazioni con processing nella tabella cut_processing
    """
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    # Ottieni tutti i request_details esistenti
    cursor.execute("SELECT id FROM request_details")
    request_details = [row[0] for row in cursor.fetchall()]

    # Ottieni tutti i processing esistenti
    cursor.execute("SELECT id FROM processing")
    processing_ids = [row[0] for row in cursor.fetchall()]

    print(f"Found {len(request_details)} request details and {len(processing_ids)} processing")

    # Stati possibili per i cuts
    statuses = ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"]

    # Note di esempio
    sample_notes = [
        "Standard cut for testing",
        "High precision cut required",
        "Cut for durability analysis",
        "Performance testing cut",
        "Quality control sample",
        "Research and development cut",
        "Batch testing sample",
        "Reference cut for comparison",
        "Special compound testing",
        "Wear pattern analysis",
        "Temperature resistance test",
        "Load capacity verification",
        "Sidewall integrity check",
        "Tread pattern evaluation",
        "Compound adhesion test"
    ]

    # Pulisci le tabelle esistenti per evitare duplicati
    cursor.execute("DELETE FROM cut_processing")
    cursor.execute("DELETE FROM REQUEST_DETAIL_CUT WHERE id > 13")  # Mantieni i dati esistenti

    cuts_created = 0
    associations_created = 0

    # Crea 50 nuovi cuts
    for i in range(50):
        # Seleziona un request_detail casuale
        request_detail_id = random.choice(request_details)

        # Genera dati casuali per il cut
        cut_price = round(random.uniform(5.0, 200.0), 2)
        status = random.choice(statuses)
        notes = random.choice(sample_notes)

        # Inserisci il cut
        cursor.execute("""
            INSERT INTO REQUEST_DETAIL_CUT (id_request_details, notes, cut_price, status)
            VALUES (?, ?, ?, ?)
        """, (request_detail_id, notes, cut_price, status))

        cut_id = cursor.lastrowid
        cuts_created += 1

        # Crea associazioni con processing (1-3 processing per cut)
        num_processing = random.randint(1, 3)
        selected_processing = random.sample(processing_ids, min(num_processing, len(processing_ids)))

        for processing_id in selected_processing:
            quantity = random.randint(1, 5)
            processing_notes = f"Processing {processing_id} for cut {cut_id}"
            created_date = datetime.now() - timedelta(days=random.randint(0, 30))

            cursor.execute("""
                INSERT INTO cut_processing (cut_id, processing_id, quantity, notes, created_date)
                VALUES (?, ?, ?, ?, ?)
            """, (cut_id, processing_id, quantity, processing_notes, created_date.isoformat()))

            associations_created += 1

    conn.commit()

    # Verifica i risultati
    cursor.execute("SELECT COUNT(*) FROM REQUEST_DETAIL_CUT")
    total_cuts = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM cut_processing")
    total_associations = cursor.fetchone()[0]

    print(f"\n✅ Popolamento completato!")
    print(f"📊 Cuts creati: {cuts_created}")
    print(f"🔗 Associazioni processing create: {associations_created}")
    print(f"📈 Totale cuts nel database: {total_cuts}")
    print(f"📈 Totale associazioni nel database: {total_associations}")

    # Mostra alcuni esempi
    print(f"\n📋 Esempi di cuts creati:")
    cursor.execute("""
        SELECT rdc.id, rdc.id_request_details, rdc.notes, rdc.cut_price, rdc.status,
               COUNT(cp.id) as processing_count
        FROM REQUEST_DETAIL_CUT rdc
        LEFT JOIN cut_processing cp ON rdc.id = cp.cut_id
        WHERE rdc.id > 13
        GROUP BY rdc.id
        LIMIT 5
    """)

    for row in cursor.fetchall():
        print(f"  Cut ID: {row[0]}, Request Detail: {row[1]}, Price: €{row[3]}, Status: {row[4]}, Processing: {row[5]}")

    # Mostra esempi di associazioni
    print(f"\n🔗 Esempi di associazioni processing:")
    cursor.execute("""
        SELECT cp.id, cp.cut_id, cp.processing_id, cp.quantity, p.description1
        FROM cut_processing cp
        JOIN processing p ON cp.processing_id = p.id
        LIMIT 5
    """)

    for row in cursor.fetchall():
        print(f"  Association ID: {row[0]}, Cut: {row[1]}, Processing: {row[2]} ({row[4]}), Qty: {row[3]}")

    conn.close()

if __name__ == "__main__":
    populate_request_detail_cut()
