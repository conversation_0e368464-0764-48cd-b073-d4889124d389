<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tire API Endpoints</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Test Tire API Endpoints</h1>
    
    <div class="endpoint">
        <h3>1. Test /api/v1/tires (Enhanced Tires)</h3>
        <button onclick="testEnhancedTires()">Test Enhanced Tires</button>
        <div id="enhanced-result" class="result"></div>
    </div>
    
    <div class="endpoint">
        <h3>2. Test /api/v1/requests/REQ001/tires (Request Tires)</h3>
        <button onclick="testRequestTires()">Test Request Tires</button>
        <div id="request-result" class="result"></div>
    </div>
    
    <div class="endpoint">
        <h3>3. Test Data Mapping</h3>
        <button onclick="testDataMapping()">Test Data Mapping</button>
        <div id="mapping-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8002/api/v1';
        
        async function testEnhancedTires() {
            const resultDiv = document.getElementById('enhanced-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const response = await fetch(`${API_BASE}/tires`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ Enhanced Tires API Success</h4>
                    <p>Found ${data.length} tires</p>
                    <h5>Sample tire data:</h5>
                    <pre>${JSON.stringify(data[0], null, 2)}</pre>
                    <h5>Field Check:</h5>
                    <ul>
                        <li>tugNo: ${data[0]?.tugNo || 'MISSING'}</li>
                        <li>specNo: ${data[0]?.specNo || 'MISSING'}</li>
                        <li>projectNo: ${data[0]?.projectNo || 'MISSING'}</li>
                        <li>loadIndex: ${data[0]?.loadIndex || 'MISSING'}</li>
                    </ul>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        async function testRequestTires() {
            const resultDiv = document.getElementById('request-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const response = await fetch(`${API_BASE}/requests/REQ001/tires`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ Request Tires API Success</h4>
                    <p>Found ${data.length} tires for REQ001</p>
                    <h5>Sample tire data:</h5>
                    <pre>${JSON.stringify(data[0], null, 2)}</pre>
                    <h5>Field Check:</h5>
                    <ul>
                        <li>tug_number: ${data[0]?.tug_number || 'MISSING'}</li>
                        <li>spec_number: ${data[0]?.spec_number || 'MISSING'}</li>
                        <li>project_number: ${data[0]?.project_number || 'MISSING'}</li>
                    </ul>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        async function testDataMapping() {
            const resultDiv = document.getElementById('mapping-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                // Fetch request tires data
                const response = await fetch(`${API_BASE}/requests/REQ001/tires`);
                const apiData = await response.json();
                
                // Simulate the frontend mapping function
                function mapApiTireToTire(apiTire) {
                    return {
                        id: apiTire.id,
                        tugNo: apiTire.tug_number,
                        section: apiTire.section,
                        projectNo: apiTire.project_number,
                        specNo: apiTire.spec_number,
                        tireSize: apiTire.tire_size,
                        pattern: apiTire.pattern,
                        note: apiTire.note,
                        disposition: apiTire.disposition,
                        quantity: apiTire.process_number,
                    };
                }
                
                const mappedData = apiData.map(mapApiTireToTire);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ Data Mapping Test Success</h4>
                    <h5>Original API Data:</h5>
                    <pre>${JSON.stringify(apiData[0], null, 2)}</pre>
                    <h5>Mapped Frontend Data:</h5>
                    <pre>${JSON.stringify(mappedData[0], null, 2)}</pre>
                    <h5>Mapping Verification:</h5>
                    <ul>
                        <li>tugNo mapped: ${mappedData[0]?.tugNo ? '✅' : '❌'}</li>
                        <li>specNo mapped: ${mappedData[0]?.specNo ? '✅' : '❌'}</li>
                        <li>projectNo mapped: ${mappedData[0]?.projectNo ? '✅' : '❌'}</li>
                    </ul>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
