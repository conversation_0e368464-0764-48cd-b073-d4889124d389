"""
Enhanced Tire Router - Simplified Database Structure

This router handles the enhanced tire catalog (master data) with the new simplified structure.
Replaces the old tire table with enhanced features like search, filtering, and soft delete.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from .. import crud, models, schemas
from ..database import get_db
from ..auth import get_current_user

router = APIRouter(
    prefix="/tires",
    tags=["tires-enhanced"],
    responses={404: {"description": "Not found"}},
)

@router.get("/", response_model=List[schemas.TireEnhancedRead])
def get_tires(
    skip: int = 0,
    limit: int = 100,
    owner: Optional[str] = Query(None, description="Filter by owner"),
    pattern: Optional[str] = Query(None, description="Filter by pattern"),
    size: Optional[str] = Query(None, description="Filter by tire size"),
    project_no: Optional[str] = Query(None, description="Filter by project number"),
    is_active: Optional[bool] = Query(True, description="Filter by active status"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all tires from the enhanced catalog with optional filtering.

    - **skip**: Number of records to skip (pagination)
    - **limit**: Maximum number of records to return
    - **owner**: Filter by tire owner (partial match)
    - **pattern**: Filter by tire pattern (partial match)
    - **size**: Filter by tire size (partial match)
    - **project_no**: Filter by project number (partial match)
    - **is_active**: Filter by active status (default: True)
    """
    tires = crud.get_tires_enhanced(
        db=db,
        skip=skip,
        limit=limit,
        owner=owner,
        pattern=pattern,
        size=size,
        project_no=project_no,
        is_active=is_active
    )
    return tires

@router.get("/{tire_id}", response_model=schemas.TireEnhancedRead)
def get_tire(
    tire_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a specific tire by ID from the enhanced catalog.
    """
    tire = crud.get_tire_enhanced(db=db, tire_id=tire_id)
    if tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return tire

@router.post("/", response_model=schemas.TireEnhancedRead)
def create_tire(
    tire: schemas.TireEnhancedCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new tire in the enhanced catalog.

    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    # Check if tug_no already exists
    existing_tire = db.query(models.Tire).filter(models.Tire.tug_no == tire.tug_no).first()
    if existing_tire:
        raise HTTPException(status_code=400, detail="Tire with this TUG number already exists")

    return crud.create_tire_enhanced(db=db, tire=tire)

@router.put("/{tire_id}", response_model=schemas.TireEnhancedRead)
def update_tire(
    tire_id: str,
    tire: schemas.TireEnhancedUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Update a tire in the enhanced catalog.

    Requires admin or editor role.
    """
    if current_user.role not in ["admin", "editor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    # Check if tug_no is being updated and already exists
    if tire.tug_no:
        existing_tire = db.query(models.Tire).filter(
            models.Tire.tug_no == tire.tug_no,
            models.Tire.id != tire_id
        ).first()
        if existing_tire:
            raise HTTPException(status_code=400, detail="Tire with this TUG number already exists")

    updated_tire = crud.update_tire_enhanced(db=db, tire_id=tire_id, tire=tire)
    if updated_tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return updated_tire

@router.delete("/{tire_id}", response_model=schemas.TireEnhancedRead)
def delete_tire(
    tire_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Soft delete a tire (set is_active = False).

    Requires admin role.
    """
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin role required")

    # Check if tire is referenced by any active request items
    active_references = db.query(models.RequestItem).filter(
        models.RequestItem.tire_id == tire_id
    ).count()

    if active_references > 0:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot delete tire: {active_references} active request items reference this tire"
        )

    deleted_tire = crud.delete_tire_enhanced(db=db, tire_id=tire_id)
    if deleted_tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return deleted_tire

@router.get("/search/by-tug/{tug_no}", response_model=schemas.TireEnhancedRead)
def get_tire_by_tug(
    tug_no: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get a tire by TUG number (unique identifier).
    """
    tire = db.query(models.Tire).filter(models.Tire.tug_no == tug_no).first()
    if tire is None:
        raise HTTPException(status_code=404, detail="Tire not found")
    return tire

@router.get("/stats/summary")
def get_tire_stats(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get tire catalog statistics.
    """
    total_tires = db.query(models.Tire).count()
    active_tires = db.query(models.Tire).filter(models.Tire.is_active == True).count()
    inactive_tires = total_tires - active_tires

    # Get top owners
    from sqlalchemy import func
    top_owners = db.query(
        models.Tire.owner,
        func.count(models.Tire.id).label('count')
    ).filter(models.Tire.is_active == True).group_by(models.Tire.owner).order_by(
        func.count(models.Tire.id).desc()
    ).limit(5).all()

    # Get top patterns
    top_patterns = db.query(
        models.Tire.pattern,
        func.count(models.Tire.id).label('count')
    ).filter(models.Tire.is_active == True).group_by(models.Tire.pattern).order_by(
        func.count(models.Tire.id).desc()
    ).limit(5).all()

    return {
        "total_tires": total_tires,
        "active_tires": active_tires,
        "inactive_tires": inactive_tires,
        "top_owners": [{"owner": owner, "count": count} for owner, count in top_owners],
        "top_patterns": [{"pattern": pattern, "count": count} for pattern, count in top_patterns]
    }
