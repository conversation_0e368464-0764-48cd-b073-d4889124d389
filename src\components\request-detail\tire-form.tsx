
"use client";

import type * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle2, XCircle } from "lucide-react";
import type { TireFormData, Tire } from "@/types";
import { cn } from "@/lib/utils";

interface FormFieldProps {
  id: string;
  label: string;
  children: React.ReactNode;
  className?: string;
}

const FormField: React.FC<FormFieldProps> = ({ id, label, children, className }) => (
  <div className={cn("space-y-1.5", className)}>
    <Label htmlFor={id}>{label}</Label>
    {children}
  </div>
);

interface TireFormProps {
  formData: TireFormData;
  onFormChange: (field: keyof TireFormData, value: any) => void;
  onSave: () => void;
  onCancel: () => void;
  isNewTire: boolean;
}

const dispositionOptions: Tire["disposition"][] = ["AVAILABLE", "SCRAP", "TESTED", "REPAIR"];

export function TireForm({ formData, onFormChange, onSave, onCancel, isNewTire }: TireFormProps) {
  // Utility function to safely handle null/undefined values for inputs
  const safeStringValue = (value: string | null | undefined): string => {
    return value ?? "";
  };

  const handleInputChange = (field: keyof TireFormData, value: string | number | undefined) => {
    onFormChange(field, value);
  };

  const handleNumericInputChange = (field: 'quantity', value: string) => {
    const numValue = value === '' ? undefined : parseInt(value, 10);
    if (value === '' || (!isNaN(numValue!) && numValue! >= 0)) {
       onFormChange(field, numValue);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">TIRE: {isNewTire ? '(New Tire)' : `(Editing ${formData.tugNo || 'Tire'})`}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-12 gap-x-6 gap-y-4">
          <div className="md:col-span-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4">
            <FormField id="tugNo" label="Tug#:">
              <Input
                id="tugNo"
                value={formData.tugNo || ""}
                onChange={(e) => handleInputChange('tugNo', e.target.value)}
              />
            </FormField>
            <FormField id="projectN" label="Project N:">
              <Input
                id="projectN"
                value={formData.projectN || ""}
                onChange={(e) => handleInputChange('projectN', e.target.value)}
              />
            </FormField>
            <FormField id="tireSizeTireForm" label="Tire Size:">
              <Input
                id="tireSizeTireForm"
                value={formData.tireSize || ""}
                onChange={(e) => handleInputChange('tireSize', e.target.value)}
              />
            </FormField>
            <FormField id="disposition" label="Disposition:">
              <Select
                value={formData.disposition || "AVAILABLE"}
                onValueChange={(value) => handleInputChange('disposition', value as Tire["disposition"])}
              >
                <SelectTrigger id="disposition">
                  <SelectValue placeholder="Select disposition" />
                </SelectTrigger>
                <SelectContent>
                  {dispositionOptions.map(opt => (
                    <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>
            <FormField id="quantity" label="N°:">
              <Input
                id="quantity"
                type="number"
                value={formData.quantity === undefined ? '' : formData.quantity}
                onChange={(e) => handleNumericInputChange('quantity', e.target.value)}
                min="0"
              />
            </FormField>
            <FormField id="noteTireForm" label="Note:" className="sm:col-span-2 lg:col-span-3">
              <Textarea
                id="noteTireForm"
                placeholder="Enter tire notes here..."
                rows={3}
                value={safeStringValue(formData.note)}
                onChange={(e) => handleInputChange('note', e.target.value)}
              />
            </FormField>
          </div>
          <div className="md:col-span-2 flex flex-col md:flex-row md:items-start justify-end md:justify-start pt-0 md:pt-7 gap-2">
            <Button variant="outline" size="icon" onClick={onSave} aria-label="Save tire details" className="text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700 w-full md:w-10">
              <CheckCircle2 className="h-5 w-5" />
            </Button>
            <Button variant="outline" size="icon" onClick={onCancel} aria-label="Cancel tire edits" className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700 w-full md:w-10">
              <XCircle className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
