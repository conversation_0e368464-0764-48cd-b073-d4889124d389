"""
Generic CRUD Base Classes for CutRequestStudio Backend

This module provides generic CRUD operations to eliminate code duplication
across different entity types (User, Tire, Request, RequestDetail, etc.).

Author: Kilo Code
Date: 28 Maggio 2025
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel
from fastapi import HTTPException
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic classes
ModelType = TypeVar("ModelType")  # SQLAlchemy model
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)  # Pydantic create schema
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)  # Pydantic update schema

class CRUDError(Exception):
    """Custom exception for CRUD operations"""
    pass

class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    Generic CRUD class with default methods to Create, Read, Update, Delete (CRUD).

    This class consolidates common CRUD operations that were duplicated across
    6 different entities in the original codebase:
    - User (crud.py:7)
    - Tire (crud.py:209)
    - Request (crud.py:67)
    - RequestDetail (crud.py:175)
    - RequestDetailCut (crud.py:316)
    - CutProcessing (crud.py:412)

    **Parameters:**
    - **model**: A SQLAlchemy model class
    - **create_schema**: A Pydantic model (schema) class for creation
    - **update_schema**: A Pydantic model (schema) class for updates

    **Example:**
    ```python
    from . import models, schemas

    # Create CRUD instance for User entity
    user_crud = CRUDBase[models.User, schemas.UserCreate, schemas.UserUpdate](models.User)

    # Use the CRUD operations
    user = user_crud.get(db, user_id=1)
    users = user_crud.get_multi(db, skip=0, limit=10)
    new_user = user_crud.create(db, obj_in=user_create_data)
    ```
    """

    def __init__(self, model: Type[ModelType]):
        """
        Initialize CRUD object with model class

        **Parameters:**
        - **model**: A SQLAlchemy model class
        """
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """
        Get a single record by ID

        **Parameters:**
        - **db**: Database session
        - **id**: Primary key value

        **Returns:**
        - Model instance or None if not found

        **Example:**
        ```python
        user = user_crud.get(db, id=1)
        if user:
            print(f"Found user: {user.email}")
        ```
        """
        try:
            return db.query(self.model).filter(self.model.id == id).first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting {self.model.__name__} with id {id}: {e}")
            raise CRUDError(f"Failed to get {self.model.__name__}") from e

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None
    ) -> List[ModelType]:
        """
        Get multiple records with pagination and optional filtering

        **Parameters:**
        - **db**: Database session
        - **skip**: Number of records to skip (for pagination)
        - **limit**: Maximum number of records to return
        - **filters**: Optional dictionary of field:value filters
        - **order_by**: Optional field name to order by

        **Returns:**
        - List of model instances

        **Example:**
        ```python
        # Get users with pagination
        users = user_crud.get_multi(db, skip=0, limit=10)

        # Get users with filters
        active_users = user_crud.get_multi(
            db,
            filters={"is_active": True, "role": "editor"}
        )
        ```
        """
        try:
            query = db.query(self.model)

            # Apply filters if provided
            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field):
                        query = query.filter(getattr(self.model, field) == value)
                    else:
                        logger.warning(f"Field {field} not found in {self.model.__name__}")

            # Apply ordering if provided
            if order_by and hasattr(self.model, order_by):
                query = query.order_by(getattr(self.model, order_by))

            return query.offset(skip).limit(limit).all()

        except SQLAlchemyError as e:
            logger.error(f"Error getting multiple {self.model.__name__}: {e}")
            raise CRUDError(f"Failed to get {self.model.__name__} list") from e

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """
        Create a new record

        **Parameters:**
        - **db**: Database session
        - **obj_in**: Pydantic schema with creation data

        **Returns:**
        - Created model instance

        **Example:**
        ```python
        user_data = schemas.UserCreate(
            email="<EMAIL>",
            name="John Doe"
        )
        new_user = user_crud.create(db, obj_in=user_data)
        ```
        """
        try:
            obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
            db_obj = self.model(**obj_data)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error creating {self.model.__name__}: {e}")
            raise CRUDError(f"Failed to create {self.model.__name__}") from e

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        Update an existing record

        **Parameters:**
        - **db**: Database session
        - **db_obj**: Existing model instance to update
        - **obj_in**: Pydantic schema or dict with update data

        **Returns:**
        - Updated model instance

        **Example:**
        ```python
        user = user_crud.get(db, id=1)
        update_data = schemas.UserUpdate(name="Jane Doe")
        updated_user = user_crud.update(db, db_obj=user, obj_in=update_data)
        ```
        """
        try:
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                update_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in

            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
                else:
                    logger.warning(f"Field {field} not found in {self.model.__name__}")

            db.commit()
            db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error updating {self.model.__name__}: {e}")
            raise CRUDError(f"Failed to update {self.model.__name__}") from e

    def remove(self, db: Session, *, id: Any) -> Optional[ModelType]:
        """
        Delete a record by ID

        **Parameters:**
        - **db**: Database session
        - **id**: Primary key value

        **Returns:**
        - Deleted model instance or None if not found

        **Example:**
        ```python
        deleted_user = user_crud.remove(db, id=1)
        if deleted_user:
            print(f"Deleted user: {deleted_user.email}")
        ```
        """
        try:
            obj = db.query(self.model).get(id)
            if obj:
                db.delete(obj)
                db.commit()
                return obj
            return None
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error deleting {self.model.__name__} with id {id}: {e}")
            raise CRUDError(f"Failed to delete {self.model.__name__}") from e

    def get_by_field(
        self,
        db: Session,
        field_name: str,
        field_value: Any
    ) -> Optional[ModelType]:
        """
        Get a single record by any field

        **Parameters:**
        - **db**: Database session
        - **field_name**: Name of the field to filter by
        - **field_value**: Value to match

        **Returns:**
        - Model instance or None if not found

        **Example:**
        ```python
        user = user_crud.get_by_field(db, "email", "<EMAIL>")
        ```
        """
        try:
            if not hasattr(self.model, field_name):
                raise ValueError(f"Field {field_name} not found in {self.model.__name__}")

            return db.query(self.model).filter(
                getattr(self.model, field_name) == field_value
            ).first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting {self.model.__name__} by {field_name}: {e}")
            raise CRUDError(f"Failed to get {self.model.__name__} by {field_name}") from e

    def exists(self, db: Session, id: Any) -> bool:
        """
        Check if a record exists by ID

        **Parameters:**
        - **db**: Database session
        - **id**: Primary key value

        **Returns:**
        - True if record exists, False otherwise

        **Example:**
        ```python
        if user_crud.exists(db, id=1):
            print("User exists")
        ```
        """
        try:
            return db.query(self.model).filter(self.model.id == id).first() is not None
        except SQLAlchemyError as e:
            logger.error(f"Error checking existence of {self.model.__name__} with id {id}: {e}")
            return False

    def count(self, db: Session, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filtering

        **Parameters:**
        - **db**: Database session
        - **filters**: Optional dictionary of field:value filters

        **Returns:**
        - Number of matching records

        **Example:**
        ```python
        total_users = user_crud.count(db)
        active_users = user_crud.count(db, filters={"is_active": True})
        ```
        """
        try:
            query = db.query(self.model)

            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field):
                        query = query.filter(getattr(self.model, field) == value)

            return query.count()
        except SQLAlchemyError as e:
            logger.error(f"Error counting {self.model.__name__}: {e}")
            raise CRUDError(f"Failed to count {self.model.__name__}") from e


class CRUDFactory:
    """
    Factory class to create CRUD instances for different entities

    This factory eliminates the need to manually create CRUD instances
    and provides a centralized way to manage all entity CRUD operations.
    """

    _instances: Dict[str, CRUDBase] = {}

    @classmethod
    def get_crud(
        cls,
        model: Type[ModelType],
        create_schema: Type[CreateSchemaType],
        update_schema: Type[UpdateSchemaType],
        name: Optional[str] = None
    ) -> CRUDBase[ModelType, CreateSchemaType, UpdateSchemaType]:
        """
        Get or create a CRUD instance for the given model

        **Parameters:**
        - **model**: SQLAlchemy model class
        - **create_schema**: Pydantic create schema class
        - **update_schema**: Pydantic update schema class
        - **name**: Optional name for the instance (defaults to model name)

        **Returns:**
        - CRUD instance

        **Example:**
        ```python
        from . import models, schemas

        user_crud = CRUDFactory.get_crud(
            models.User,
            schemas.UserCreate,
            schemas.UserUpdate
        )
        ```
        """
        instance_name = name or model.__name__

        if instance_name not in cls._instances:
            cls._instances[instance_name] = CRUDBase[ModelType, CreateSchemaType, UpdateSchemaType](model)

        return cls._instances[instance_name]

    @classmethod
    def clear_cache(cls):
        """Clear the CRUD instance cache"""
        cls._instances.clear()


# Utility functions for common operations
def safe_get_or_404(crud: CRUDBase, db: Session, id: Any, entity_name: str = "Entity"):
    """
    Get an entity by ID or raise 404 HTTPException

    **Parameters:**
    - **crud**: CRUD instance
    - **db**: Database session
    - **id**: Entity ID
    - **entity_name**: Name for error message

    **Returns:**
    - Entity instance

    **Raises:**
    - HTTPException: 404 if entity not found
    """
    entity = crud.get(db, id=id)
    if not entity:
        raise HTTPException(status_code=404, detail=f"{entity_name} not found")
    return entity


def safe_update_or_404(
    crud: CRUDBase,
    db: Session,
    id: Any,
    update_data: Any,
    entity_name: str = "Entity"
):
    """
    Update an entity by ID or raise 404 HTTPException

    **Parameters:**
    - **crud**: CRUD instance
    - **db**: Database session
    - **id**: Entity ID
    - **update_data**: Update data
    - **entity_name**: Name for error message

    **Returns:**
    - Updated entity instance

    **Raises:**
    - HTTPException: 404 if entity not found
    """
    entity = safe_get_or_404(crud, db, id, entity_name)
    return crud.update(db, db_obj=entity, obj_in=update_data)


def safe_delete_or_404(
    crud: CRUDBase,
    db: Session,
    id: Any,
    entity_name: str = "Entity"
):
    """
    Delete an entity by ID or raise 404 HTTPException

    **Parameters:**
    - **crud**: CRUD instance
    - **db**: Database session
    - **id**: Entity ID
    - **entity_name**: Name for error message

    **Returns:**
    - Deleted entity instance

    **Raises:**
    - HTTPException: 404 if entity not found
    """
    entity = safe_get_or_404(crud, db, id, entity_name)
    return crud.remove(db, id=id)