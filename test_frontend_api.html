<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend API</title>
</head>
<body>
    <h1>Test Frontend API</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = 'Loading...';
            
            try {
                // Test the tires endpoint
                const response = await fetch('http://127.0.0.1:8001/api/v1/requests/REQ001/tires');
                const data = await response.json();
                
                console.log('API Response:', data);
                
                resultsDiv.innerHTML = `
                    <h2>Success!</h2>
                    <p>Found ${data.length} tires</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // Check if all required fields are present
                data.forEach((tire, index) => {
                    console.log(`Tire ${index + 1}:`, {
                        id: tire.id,
                        tug_number: tire.tug_number,
                        spec_number: tire.spec_number,
                        project_number: tire.project_number,
                        tire_size: tire.tire_size,
                        pattern: tire.pattern,
                        disposition: tire.disposition,
                        process_number: tire.process_number
                    });
                });
                
            } catch (error) {
                console.error('API Error:', error);
                resultsDiv.innerHTML = `
                    <h2>Error!</h2>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
