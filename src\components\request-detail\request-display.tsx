
"use client";

import type * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { FileText, Users } from "lucide-react";
import { format } from "date-fns";
import type { AppRequest } from "@/types";
import { cn } from "@/lib/utils";

interface FormFieldDisplayProps {
  label: string;
  value: string | number | React.ReactNode;
  className?: string;
}

const FormFieldDisplay: React.FC<FormFieldDisplayProps> = ({ label, value, className }) => {
  // Safely handle null/undefined values for inputs
  const safeValue = (val: any): string => {
    if (val === null || val === undefined) return "";
    return String(val);
  };

  return (
    <div className={cn("space-y-1.5", className)}>
      <Label className="text-sm font-medium text-muted-foreground">{label}</Label>
      {typeof value === 'string' || typeof value === 'number' ? (
        <Input value={safeValue(value)} readOnly className="bg-muted/50 border-none cursor-default" />
      ) : (
        <div className="h-10 flex items-center px-3 py-2 text-sm rounded-md bg-muted/50">{value}</div>
      )}
    </div>
  );
};

interface RequestDisplayProps {
  request: AppRequest | null;
  onIconClick: (action: "report" | "inChargeOf") => void;
  disableinChargeOfButton?: boolean;
}

export function RequestDisplay({ request, onIconClick, disableinChargeOfButton = false }: RequestDisplayProps) {
  // Safely handle null/undefined values for inputs
  const safeValue = (val: any): string => {
    if (val === null || val === undefined) return "";
    return String(val);
  };

  if (!request) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Request Details</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No request selected or data available.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">REQUEST:</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-4">
          <FormFieldDisplay label="REQUEST:" value={request.id} />
          <FormFieldDisplay label="Status:" value={request.status} />
          <FormFieldDisplay label="Total N°:" value={request.totalN ?? 'N/A'} />
          <FormFieldDisplay label="Request Date:" value={request.requestDate ? format(new Date(request.requestDate), "PPP") : "N/A"} />

          <div className="space-y-1.5">
            <Label className="text-sm font-medium text-muted-foreground">Internal:</Label>
            <div className="flex items-center h-10 px-3 py-2 text-sm rounded-md bg-muted/50 border-none cursor-default">
                <Checkbox checked={request.internal} disabled className="mr-2" aria-readonly/>
                <span>{request.internal ? "Yes" : "No"}</span>
            </div>
          </div>

          <FormFieldDisplay label="Request By:" value={request.requestBy} />
          <FormFieldDisplay label="Type:" value={request.type} />
          <FormFieldDisplay label="Wish Date:" value={request.wishDate ? format(new Date(request.wishDate), "PPP") : "N/A"} />

          <div className="space-y-1.5">
            <Label htmlFor="report-button" className="text-sm font-medium text-muted-foreground">Report:</Label>
            <Button id="report-button" variant="outline" size="icon" className="w-full" aria-label="View Report" onClick={() => onIconClick("report")}>
              <FileText className="h-5 w-5" />
            </Button>
          </div>

          <FormFieldDisplay label="Pid/Project#:" value={request.pidProject || request.projectNo} className="xl:col-span-1" />
          <FormFieldDisplay label="Tire size:" value={request.tireSize} />

          <div className="space-y-1.5 xl:col-span-2">
            <Label htmlFor="inChargeOf-display" className="text-sm font-medium text-muted-foreground">In Charge Of:</Label>
            <div className="flex items-center gap-2">
              <Input id="inChargeOf-display" value={safeValue(request.inChargeOf) || "N/A"} readOnly className="flex-grow bg-muted/50 border-none cursor-default" />
              <Button variant="outline" size="icon" aria-label="Select Person In Charge" onClick={() => onIconClick("inChargeOf")}
                className={`w-full ${disableinChargeOfButton ? "opacity-50 cursor-not-allowed" : ""}`}
                disabled={disableinChargeOfButton}>
                <Users className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="sm:col-span-2 lg:col-span-3 xl:col-span-4 space-y-1.5">
            <Label className="text-sm font-medium text-muted-foreground">Note:</Label>
            <Textarea value={request.note || "N/A"} readOnly rows={3} className="bg-muted/50 border-none cursor-default" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
