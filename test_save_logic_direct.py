#!/usr/bin/env python3
"""
Test diretto della logica di salvataggio senza passare per l'API.
Questo ci permette di testare direttamente la funzione update_or_create_request_detail.
"""

import sys
sys.path.append('./backend')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import backend.models as models
import backend.schemas as schemas
import backend.crud as crud
from datetime import datetime

# Create database connection
SQLALCHEMY_DATABASE_URL = 'sqlite:///./backend/app.db'
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={'check_same_thread': False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_request_detail_creation():
    """Test della creazione di request details"""
    print("🔍 Testing direct request detail creation...")

    db = SessionLocal()

    try:
        # Test data che simula quello che il frontend invia
        test_detail_data = {
            "id": "test-detail-1",
            "request_id": "REQ001",
            "tug_number": "TUG-TEST-NEW",
            "section": "FROM_MANAGEMENT",
            "project_number": "PRJ-TEST",
            "spec_number": "SPEC-TEST",
            "tire_size": "225/45R17",
            "pattern": "TEST_PATTERN",
            "note": "Added via management...",
            "disposition": "AVAILABLE",
            "process_number": 0
        }

        print(f"Test data: {test_detail_data}")

        # Prova a creare lo schema RequestDetailBase
        try:
            detail_schema = schemas.RequestDetailBase(**test_detail_data)
            print("✅ Schema RequestDetailBase created successfully")
            print(f"Schema dict: {detail_schema.dict()}")
        except Exception as e:
            print(f"❌ Schema creation failed: {e}")
            return

        # Prova a usare la funzione crud
        try:
            result = crud.update_or_create_request_detail(db, detail_schema)
            print("✅ CRUD operation successful")
            print(f"Result ID: {result.id}")
            print(f"Result tug_number: {result.tug_number}")
        except Exception as e:
            print(f"❌ CRUD operation failed: {e}")
            import traceback
            traceback.print_exc()

    finally:
        db.close()

def test_request_update_with_details():
    """Test dell'aggiornamento di una richiesta con request_details"""
    print("\n🔍 Testing request update with details...")

    db = SessionLocal()

    try:
        # Simula il payload che il frontend invia
        request_data = {
            "id": "REQ001",
            "request_by": "giovanni.rossi",
            "project_no": "PRJ-TEST",
            "pid_project": "PID-TEST",
            "tire_size": "225/45R17",
            "request_date": datetime.now(),
            "target_date": datetime.now(),
            "status": "DRAFT",
            "type": "NEWDEV",
            "total_n": 2,
            "destination": "PLANT_A",
            "internal": False,
            "wish_date": None,
            "num_pneus_set": 4,
            "num_pneus_set_unit": "SET",
            "in_charge_of": "Test Department",
            "note": "Test note",
            "attachments": [],
            "request_details": [
                {
                    "id": "detail-1",
                    "request_id": "REQ001",
                    "tug_number": "TUG-TEST-NEW",
                    "section": "FROM_MANAGEMENT",
                    "project_number": "PRJ-TEST",
                    "spec_number": "SPEC-TEST",
                    "tire_size": "225/45R17",
                    "pattern": "TEST_PATTERN",
                    "note": "Added via management...",
                    "disposition": "AVAILABLE",
                    "process_number": 0
                }
            ]
        }

        # Prova a creare lo schema RequestUpdate
        try:
            request_schema = schemas.RequestUpdate(**request_data)
            print("✅ Schema RequestUpdate created successfully")
            print(f"Has request_details: {hasattr(request_schema, 'request_details')}")
            print(f"Request details count: {len(request_schema.request_details) if request_schema.request_details else 0}")
        except Exception as e:
            print(f"❌ Schema RequestUpdate creation failed: {e}")
            import traceback
            traceback.print_exc()
            return

        # Prova a aggiornare la richiesta
        try:
            result = crud.update_request(db, "REQ001", request_schema)
            print("✅ Request update successful")
            print(f"Result ID: {result.id}")
        except Exception as e:
            print(f"❌ Request update failed: {e}")
            import traceback
            traceback.print_exc()

        # Prova a gestire i request_details separatamente
        if request_schema.request_details:
            print(f"\n🔍 Processing {len(request_schema.request_details)} request details...")
            for detail in request_schema.request_details:
                try:
                    detail_result = crud.update_or_create_request_detail(db, detail)
                    print(f"✅ Detail {detail.id} processed successfully")
                except Exception as e:
                    print(f"❌ Detail {detail.id} processing failed: {e}")
                    import traceback
                    traceback.print_exc()

    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Starting direct save logic test...")
    test_request_detail_creation()
    test_request_update_with_details()
    print("\n✅ Test completed!")
